{"name": "react-boilerplat", "version": "1.0.0", "private": true, "proxy": "https://182.70.118.145:18443", "dependencies": {"@ant-design/icons": "^5.5.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-sql": "^6.5.5", "@floating-ui/dom": "^1.6.4", "@react-querybuilder/antd": "^7.7.1", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@uiw/codemirror-theme-monokai": "^4.21.21", "@uiw/codemirror-theme-xcode": "^4.21.21", "@uiw/react-codemirror": "^4.21.21", "@vertx/eventbus-bridge-client.js": "^1.0.0-3-SNAPSHOT", "animated-number-react": "^0.1.2", "antd": "^5.21.6", "axios": "^1.2.2", "caniuse-lite": "^1.0.30001597", "classnames": "^2.3.2", "color": "^4.2.3", "cytoscape": "^3.31.1", "cytoscape-dagre": "^2.5.0", "cytoscape-html-label": "^1.1.7", "cytoscape-panzoom": "^2.5.3", "cytoscape-popper": "^4.0.0", "echarts": "^5.4.2", "echarts-for-react": "^3.0.2", "html2canvas": "^1.4.1", "http-proxy-middleware": "^2.0.6", "humanize-duration": "^3.28.0", "jspdf": "^2.5.1", "jspdf-autotable": "^3.7.1", "lodash": "^4.17.21", "mammoth": "^1.9.1", "marked": "^15.0.4", "mem": "^10.0.0", "mitt": "^3.0.0", "moment": "^2.29.4", "moment-timezone": "^0.5.40", "nodemailer": "^6.9.7", "prism-themes": "^1.9.0", "prismjs": "^1.29.0", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-country-flag": "^3.1.0", "react-cytoscapejs": "^2.0.0", "react-diff-viewer": "^3.1.1", "react-dom": "^18.2.0", "react-gauge-component": "^1.2.64", "react-grid-layout": "^1.3.4", "react-json-view": "^1.21.3", "react-loading": "^2.0.3", "react-markdown": "^9.0.1", "react-pdf": "^9.2.1", "react-querybuilder": "^7.7.1", "react-resizable": "^3.0.5", "react-router-dom": "^6.6.1", "react-scripts": "5.0.1", "react-simple-code-editor": "^0.14.1", "react-sortablejs": "^6.1.4", "react-textfit": "^1.1.1", "react-transition-group": "^4.4.5", "sortablejs": "^1.15.0", "use-react-router-breadcrumbs": "^4.0.1", "uuid": "^9.0.0", "web-vitals": "^3.1.0", "xlsx": "^0.18.5"}, "scripts": {"start": "PORT=3001 && craco start", "build": "craco build", "test": "craco test", "lint": "eslint .", "lint:fix": "eslint --fix", "format": "prettier --write './**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc.js"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@craco/craco": "^7.0.0", "@svgr/webpack": "^6.5.1", "@types/sortablejs": "^1.15.1", "babel-plugin-import": "^1.13.5", "craco-less": "^2.0.0", "eslint": "^8.32.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.3", "tailwindcss": "^3.2.4"}}