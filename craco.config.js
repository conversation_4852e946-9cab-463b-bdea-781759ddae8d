const CracoLess = require('craco-less');
const aliases = require('./aliases.config').webpack;

module.exports = {
  babel: {
    plugins: ['@babel/plugin-syntax-jsx']
  },
  webpack: {
    alias: aliases
    //   configure: (config, { env, paths }) => {
    //     config.module.rules.unshift({
    //       test: /\.svg(\?v=\d+\.\d+\.\d+)?$/i,
    //       type: 'asset',
    //       resourceQuery: /url/,
    //     })
    //     config.module.rules.unshift({
    //       test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
    //       resourceQuery: { not: [/url/] }, // exclude react component if *.svg?url
    //       use: [
    //         {
    //           loader: 'babel-loader',
    //         },
    //         {
    //           loader: '@svgr/webpack',
    //           options: {
    //             babel: false,
    //             icon: true,
    //           },
    //         },
    //       ],
    //     })
    //     return config
    //   },
  },
  plugins: [
    {
      plugin: CracoLess,
      options: {
        lessLoaderOptions: {
          lessOptions: {
            // modifyVars: mapToken,
          }
        }
      }
    }
  ],
  devServer: (devServerConfig, { env, paths, proxy, allowedHost }) => {
    /* ... */
    devServerConfig.allowedHosts = 'all';
    devServerConfig.compress = true;
    devServerConfig.port = 3001;
    devServerConfig.client = {
      progress: true,
      reconnect: 3,
      webSocketTransport: 'ws'
    };
    devServerConfig.webSocketServer = 'ws';
    devServerConfig.proxy = {
      '/api': {
        target: 'https://demo.zirozen.io',
        secure: false
      },
      '/eventbus': {
        target: 'https://demo.zirozen.io',
        secure: false
      }
    };
    devServerConfig.allowedHosts = 'all';
    return devServerConfig;
  }
};
