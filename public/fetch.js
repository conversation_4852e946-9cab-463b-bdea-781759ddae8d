// Store token in service worker global scope
let authToken = null;

// Listen for messages from main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SET_TOKEN') {
    authToken = event.data.token;
    console.log('Token updated in service worker:', authToken);
  }
});

// eslint-disable-next-line
self.addEventListener('fetch', (event) => {
  console.log(event);
  event.respondWith(customHeaderRequestFetch(event));
});

function customHeaderRequestFetch(event) {
  console.log(event.request.headers);
  // decide for yourself which values you provide to mode and credentials

  // Copy existing headers
  const headers = new Headers(event.request.headers);

  // Set Authorization header with token from localStorage if available
  if (authToken) {
    headers.set('Authorization', `Bearer ${authToken}`);
  }
  // Delete a header
  headers.delete('x-request');

  const newRequest = new Request(event.request, {
    mode: 'cors',
    credentials: 'omit',
    headers: headers
  });
  return fetch(newRequest);
}
