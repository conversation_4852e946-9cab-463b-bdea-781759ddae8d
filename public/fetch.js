// Store token in service worker global scope
let authToken = null;

// Listen for messages from main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SET_TOKEN') {
    try {
      // Try to parse as <PERSON><PERSON><PERSON> first, fallback to string
      authToken =
        typeof event.data.token === 'string' ? JSON.parse(event.data.token) : event.data.token;
    } catch (e) {
      // If JSON parsing fails, use as string
      authToken = event.data.token;
    }
    console.log('Token updated in service worker:', authToken);
  }
});

// eslint-disable-next-line
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  const isGetRequest = event.request.method === 'GET';
  const isApiRequest = url.pathname.startsWith('/api');
  const missingAuthHeader = !event.request.headers.has('Authorization');

  if (isGetRequest && isApiRequest && missingAuthHeader && authToken) {
    event.respondWith(customHeaderRequestFetch(event));
  }
});

function customHeaderRequestFetch(event) {
  const request = event.request.clone();
  const headers = new Headers(request.headers);

  // Set Authorization header with token from localStorage if available
  if (authToken) {
    // Handle both JSON object with access_token and plain string token
    const tokenValue =
      typeof authToken === 'object' && authToken.access_token ? authToken.access_token : authToken;

    headers.set('Authorization', `Bearer ${tokenValue}`);
  }

  const newRequest = new Request(event.request, {
    headers: headers,
    credentials: 'include'
  });

  // Log all headers for debugging
  console.log('New Request headers:', Array.from(newRequest.headers.entries()));
  return fetch(newRequest);
}
