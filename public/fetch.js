// Store token in service worker global scope
let authToken = null;

// Listen for messages from main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SET_TOKEN') {
    authToken = JSON.parse(event.data.token);
    console.log('Token updated in service worker:', authToken);
  }
});

// eslint-disable-next-line
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  const isGetRequest = event.request.method === 'GET';
  const isApiRequest = url.pathname.startsWith('/api/download');
  const hasAuthHeader = event.request.headers.has('Authorization');
  if (isGetRequest && isApiRequest && !hasAuthHeader) {
    return event.respondWith(customHeaderRequestFetch(event));
  } else {
    return fetch(event.request);
  }
});

function customHeaderRequestFetch(event) {
  // Copy existing headers
  const headers = new Headers(event.request.headers);
  // Set Authorization header with token from localStorage if available
  if (authToken) {
    headers.set('Authorization', `Bearer ${authToken.access_token}`);
  }

  headers.entries().forEach((header) => {
    console.log(header);
  });

  const newRequest = new Request(event.request, {
    headers: headers
  });
  return fetch(newRequest);
}
