import { useEffect, useMemo, useRef, useState } from 'react';
import { Spin } from 'antd';
import GridLayout from 'react-grid-layout';
import { useParams } from 'react-router-dom';
import { getReportApi } from '../modules/reports/api/reports';
import { Branding } from '../components/Branding';
import { useAuth } from '../hooks/auth';
import WidgetContainer from '../components/widget/WidgetContainer';
import buildResult from '../components/widget/views/result-builder';

export default function ReportExportPage() {
  const [loading, setLoading] = useState(true);
  const [reportWidgetData, setReportWidgetData] = useState({});
  const [report, setReport] = useState({});
  const [width, setWidth] = useState(null);
  const containerRef = useRef(null);
  const params = useParams();
  const { branding } = Branding.useBranding();
  const { token } = useAuth();

  const GRID_COLUMNS = 4;
  const ROW_HEIGHT = 250;

  const widgetMap = useMemo(() => {
    return (report.widgets || []).reduce((acc, widget) => {
      acc[widget.guid] = widget;
      return acc;
    }, {});
    // eslint-disable-next-line
  }, [report.widgets]);

  useEffect(() => {
    getReportApi(params.id).then((data) => {
      setReport(data);
      window.__REPORT__ = {
        ...data,
        maxColumns: GRID_COLUMNS,
        rowHeight: ROW_HEIGHT,
        layout: data.layout
          .sort((a, b) => {
            // same row, compare X
            if (a.y === b.y) {
              return a.x - b.x;
            }

            // different row, compare Y
            return a.y - b.y;
          })
          .map((i) => ({ ...i, isDraggable: false, isResizable: false })),
        logo: `${
          branding.ref
            ? `${window.location.origin}/api/download?id=${
                branding.ref || branding.response
                // eslint-disable-next-line
              }&mid=${token.access_token}`
            : `${window.location.origin}/images/zirozen1.png`
        }`
      };
      setLoading(false);
    });
    // eslint-disable-next-line
  }, [params]);

  useEffect(() => {
    if (containerRef.current) {
      setWidth(containerRef.current.offsetWidth);
    }
  }, [containerRef]);

  useEffect(() => {
    if (Object.keys(widgetMap).length === 0) {
      return;
    }
    if (Object.keys(reportWidgetData).length >= Object.keys(widgetMap).length) {
      let formattedGridData = {};
      Object.keys(widgetMap).forEach((key) => {
        if (widgetMap[key].type.toLowerCase() === 'grid') {
          formattedGridData[key] = buildResult(reportWidgetData[key], widgetMap[key]);
        }
      });
      setTimeout(() => {
        window.__WIDGET_DATA__ = formattedGridData;
        if (window.setReport) {
          window.setReport(window.__REPORT__);
        }
        if (window.captureViewPort) {
          window.captureViewPort();
        }
      }, 1000);
    }
  }, [reportWidgetData, widgetMap]);

  function handleWidgetDataReceived(guid, data) {
    setReportWidgetData((prev) => ({
      ...prev,
      [guid]: widgetMap[guid].type.toLowerCase() === 'grid' ? data : null
    }));
  }

  // function handleReportDataReceivd(data) {
  //   if (data.error) {
  //     throw new Error('Unable to received data');
  //     // handle error here
  //   } else {
  //     const result = buildResult(data);
  //     setTimeout(() => {
  //       window.__WIDGET_DATA__ = {
  //         rows: result.data,
  //         columns: result.columns
  //       };
  //       if (window.setReport) {
  //         window.setReport(window.__REPORT__);
  //       }
  //       if (window.captureViewPort) {
  //         window.captureViewPort();
  //       }
  //     }, 1000);
  //   }
  // }

  return (
    <div className="" ref={containerRef}>
      {!loading && report.id ? (
        <GridLayout
          className="layout"
          containerPadding={[0, 10]}
          layout={report.layout}
          rowHeight={ROW_HEIGHT}
          cols={GRID_COLUMNS}
          width={width}
          margin={[8, 8]}
          compactType="vertical">
          {report.layout.map((item) => (
            <div key={item.i} className="widget-container for-pdf-print" data-guid={item.i}>
              <WidgetContainer
                isPreview
                forPrint
                timeline={report.timeline}
                widget={widgetMap[item.i]}
                socketEventHandlerProps={{
                  onDataReceived(data) {
                    handleWidgetDataReceived(item.i, data);
                  }
                }}
              />
            </div>
          ))}
        </GridLayout>
      ) : (
        <Spin spinning={loading}>
          <div className="flex flex-col min-w-0 min-h-0 flex-1" />
        </Spin>
      )}
    </div>
  );
}
