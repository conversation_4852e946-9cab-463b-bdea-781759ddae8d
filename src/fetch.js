// eslint-disable-next-line
self.addEventListener('fetch', (event) => {
  console.log(event);
  event.respondWith(customHeaderRequestFetch(event));
});

function customHeaderRequestFetch(event) {
  console.log(event.request.headers);
  // decide for yourself which values you provide to mode and credentials

  // Copy existing headers
  const headers = new Headers(event.request.headers);

  // Set a new header
  headers.set('Authorization', 'The Most Amazing Header Ever');
  // Delete a header
  headers.delete('x-request');

  const newRequest = new Request(event.request, {
    mode: 'cors',
    credentials: 'omit',
    headers: headers
  });
  return fetch(newRequest);
}
