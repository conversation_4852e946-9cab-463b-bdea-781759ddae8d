import React from 'react';
import ReactDOM from 'react-dom/client';
import './design/index.less';
import 'antd/dist/reset.css';
import App from './Root.jsx';

ReactDOM.createRoot(document.getElementById('root')).render(
  // <React.StrictMode>
  <App />
  // </React.StrictMode>
);

navigator.serviceWorker.getRegistrations().then((registrations) => {
  for (const registration of registrations) {
    registration.unregister();
  }
  navigator.serviceWorker.register(new URL('./fetch.js', import.meta.url)).then((response) => {
    console.log(response);
  });
});

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
// reportWebVitals(console.log);
