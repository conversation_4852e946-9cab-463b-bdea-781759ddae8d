import React from 'react';
import ReactDOM from 'react-dom/client';
import './design/index.less';
import 'antd/dist/reset.css';
import App from './Root.jsx';

ReactDOM.createRoot(document.getElementById('root')).render(
  // <React.StrictMode>
  <App />
  // </React.StrictMode>
);

navigator.serviceWorker.getRegistrations().then((registrations) => {
  for (const registration of registrations) {
    registration.unregister();
  }
  navigator.serviceWorker.register('/fetch.js', { scope: '/' }).then((registration) => {
    console.log('Service worker registered:', registration);

    // Send token to service worker when it's ready
    const sendTokenToServiceWorker = () => {
      const token = localStorage.getItem('token'); // Adjust key name as needed
      if (token && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'SET_TOKEN',
          token: token
        });
      }
    };

    // Send token immediately if service worker is already controlling
    if (navigator.serviceWorker.controller) {
      sendTokenToServiceWorker();
    }

    // Listen for service worker to become ready
    navigator.serviceWorker.ready.then(() => {
      sendTokenToServiceWorker();
    });

    // Listen for localStorage changes and update service worker
    window.addEventListener('storage', (e) => {
      if (e.key === 'token') {
        sendTokenToServiceWorker();
      }
    });

    // Also send token when it's updated programmatically
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function (key, value) {
      originalSetItem.apply(this, arguments);
      if (key === 'token') {
        sendTokenToServiceWorker();
      }
    };
  });
});

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
// reportWebVitals(console.log);
