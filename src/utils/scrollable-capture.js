import html2canvas from 'html2canvas';

export default async function exportScrollableElement(htmlElement, filename, options) {
  const originalScrollTop = htmlElement.scrollTop;
  htmlElement.scrollTop = 0;
  const totalHeight = htmlElement.scrollHeight + 50;
  const visibleHeight = htmlElement.offsetHeight;
  const imagesToCapture = Math.ceil(totalHeight / visibleHeight);
  const canvas = await captureImages(htmlElement, imagesToCapture, visibleHeight, options);
  htmlElement.scrollTop = originalScrollTop;
  var link = document.createElement('a');
  link.setAttribute('download', `${filename}.png`);
  link.setAttribute(
    'href',
    canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream')
  );
  link.click();
}

function getImage(url) {
  return new Promise((resolve) => {
    const image = new Image();
    image.src = url;
    image.onload = function () {
      resolve(image);
    };
  });
}

async function captureImages(element, imagesToCapture, singleHeight, options = {}) {
  const rootCanvas = document.createElement('canvas');
  const ctx = rootCanvas.getContext('2d');
  let totalHeight = element.scrollHeight - 2;
  if ((options.prepend || []).length > 0) {
    totalHeight += options.prepend.reduce((t, el) => t + el.offsetHeight, 0);
  }
  rootCanvas.width = element.clientWidth;
  rootCanvas.height = totalHeight;
  ctx.clearRect(0, 0, rootCanvas.width, rootCanvas.height);
  let yOffset = 0;
  if ((options.prepend || []).length > 0) {
    for (let i = 0; i < options.prepend.length; i++) {
      const canvas = await html2canvas(options.prepend[i], {
        scale: 1
      });
      const image = await getImage(canvas.toDataURL('image/png'));
      ctx.drawImage(image, 0, yOffset, image.naturalWidth, image.naturalHeight);
      yOffset += options.prepend[i].offsetHeight;
    }
  }
  let usedHeight = 0;
  for (let i = 0; i < imagesToCapture; i++) {
    const canvas = await html2canvas(element, {
      scale: 1,
      height: singleHeight
    });
    const image = await getImage(canvas.toDataURL('image/png'));
    const remainingHeight = element.scrollHeight - usedHeight;
    let dy = i * singleHeight;
    dy += yOffset;
    if (remainingHeight < singleHeight) {
      ctx.drawImage(
        image,
        0,
        image.naturalHeight - remainingHeight,
        image.naturalWidth,
        remainingHeight,
        0,
        dy,
        image.naturalWidth,
        remainingHeight
      );
    } else {
      ctx.drawImage(image, 0, dy, image.naturalWidth, image.naturalHeight);
    }
    // ctx.drawImage(image, 0, 0, 0, i * singleHeight)
    element.scrollTop += singleHeight;
    usedHeight += singleHeight;
  }

  return rootCanvas;
}
