import { useState, createContext, useContext } from 'react';
import { Outlet } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import SplitPane from '@/src/components/SplitPane';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import PlatformFilters from '../components/PlatformFilter';
import PatchStateFilters from '../components/PatchStateFilters';

const PatchLayoutContext = createContext();

export default function PatchLayout() {
  const [showMenu, setShowMenu] = useState(true);
  const [isMenuVisible, setIsMenuVisible] = useState(true);
  const [platform, setSelectedPlatform] = useState('all');
  const [state, setSelectedState] = useState('all');

  return (
    <PatchLayoutContext.Provider
      value={{
        hideMenu: () => setShowMenu(false),
        showMenu: () => setShowMenu(true),
        displayNone: () => setIsMenuVisible(false),
        displayBlock: () => setIsMenuVisible(true),
        filters: {
          platform,
          state
        }
      }}>
      <div className="h-full flex flex-col">
        <PageHeading icon="patch" title="Patch" />
        <div className="flex-1 min-h-0 flex flex-col">
          <PermissionChecker permission={constants.View_Patch} redirect>
            <SplitPane
              hasMenu={showMenu}
              isMenuVisible={isMenuVisible}
              onVisibleChange={(i) => setIsMenuVisible(i)}
              leftPane={
                <>
                  <PlatformFilters
                    value={platform}
                    onSelect={(platform) => setSelectedPlatform(platform)}
                  />

                  <PatchStateFilters value={state} onSelect={(s) => setSelectedState(s)} />
                </>
              }
              rightPane={<Outlet />}
            />
          </PermissionChecker>
        </div>
      </div>
    </PatchLayoutContext.Provider>
  );
}

export function usePatchLayout() {
  return useContext(PatchLayoutContext);
}
