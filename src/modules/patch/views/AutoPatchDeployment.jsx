import { useEffect } from 'react';
import { usePatchLayout } from '../layout/PatchLayout';
import PageHeading from '@/src/components/PageHeading';

import Deployments from '@modules/device-automation/views/Deployments';
import { DEPLOYMENT_TYPE } from '@modules/device-automation/deployments';
import { ComputerGroups } from '@/src/components/pickers/ComputerGroupsPicker';

export default function AutoPatchDeployment() {
  const layout = usePatchLayout();
  useEffect(() => {
    layout.hideMenu();

    return () => layout.showMenu();
    // eslint-disable-next-line
  }, []);

  return (
    <>
      <PageHeading icon="patch" title={`Zero Touch Deployment`} />
      <ComputerGroups.Provider>
        <Deployments type={DEPLOYMENT_TYPE.AUTO_PATCH_DEPLOYMENT} />;
      </ComputerGroups.Provider>
    </>
  );
}
