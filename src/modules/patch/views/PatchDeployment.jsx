import { useEffect } from 'react';
import { usePatchLayout } from '../layout/PatchLayout';
import PageHeading from '@/src/components/PageHeading';

import Deployments from '@modules/device-automation/views/Deployments';
import { DEPLOYMENT_TYPE } from '@modules/device-automation/deployments';

export default function PatchDeployment({ type = 'Patch' }) {
  const layout = usePatchLayout();
  useEffect(() => {
    layout.hideMenu();

    return () => layout.showMenu();
    // eslint-disable-next-line
  }, []);

  return (
    <>
      <PageHeading icon="patch" title={`Patch Deployment`} />
      {/* <DeploymentPolicy.Provider> */}
      <Deployments type={DEPLOYMENT_TYPE.PATCH} />;{/* </DeploymentPolicy.Provider> */};
    </>
  );
}
