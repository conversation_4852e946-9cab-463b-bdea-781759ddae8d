import { useState } from 'react';
import Capitalize from 'lodash/capitalize';

import { <PERSON><PERSON>, Drawer, Select } from 'antd';
import { useNavigate } from 'react-router-dom';
import { CrudProvider } from '@/src/hooks/crud';
import Severity from '@/src/components/Severity';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import {
  createPatchApi,
  getAllPatchApi,
  addPatchExceptionApi,
  changePatchApprovalStatusApi
} from '../api/patch-list';
import PatchForm from '../components/PatchForm';
import { PatchLanguage } from '@/src/components/pickers/PatchLanguagePicker';
import Icon from '@/src/components/Icon';
import { usePatchLayout } from '../layout/PatchLayout';
import PageHeading from '@/src/components/PageHeading';
import CopyOnDoubleClick from '@components/common/CopyOnDoubleClick';
import EndpointTabs from '../components/patch-tabs/Endpoints';

import { User } from '@/src/components/pickers/UserPicker';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import { Location } from '@/src/components/pickers/LocationPicker';

import ExternalLink from '@components/common/ExternalLink';
import { useLayout } from '@/src/layouts/Layout';
import ExceptionModel from '@modules/vulnerability/components/ExceptionModel';

export default function List() {
  const { filters } = usePatchLayout();
  const [assetsForPatch, setShowAssetForPatch] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);
  const [isModalopen, setIsModalopen] = useState(false);
  const [updateKey, setUpdateKey] = useState(0); // Key to force re-renders

  const patchApprovalOptions = [
    { label: 'Approved', value: 'approved' },
    { label: 'Decline', value: 'decline' },
    { label: 'Not Approved', value: 'not_approved' },
    { label: 'Pending Upload', value: 'pending_upload' }
  ];

  const layout = useLayout();

  // const rebootMap = {
  //   may_be: 'May Be',
  //   yes: 'Yes',
  //   no: 'No'
  // };

  const columns = [
    {
      title: 'Title',
      key: 'title',
      dataIndex: 'title',
      type: 'view_link'
    },
    {
      title: 'ID',
      key: 'name',
      dataIndex: 'name'
    },
    {
      title: 'Endpoints',
      dataIndex: 'totalEndPoints',
      key: 'totalEndPoints',
      sortable: false,
      render({ record }) {
        return (
          <Button type="link" onClick={() => setShowAssetForPatch(record)}>
            {record.totalEndPoints}
          </Button>
        );
      },
      align: 'center'
    },

    {
      title: 'Name',
      key: 'name',
      dataIndex: 'name'
    },
    {
      title: 'Severity',
      dataIndex: 'patchSeverity',
      key: 'patchSeverity',
      render({ record }) {
        return <Severity severity={record.patchSeverity} useTag />;
      }
    },
    {
      title: 'Platform',
      dataIndex: 'platform',
      key: 'platform',
      sortable: false,
      render({ record }) {
        return (
          <div className="flex items-center">
            <Icon
              name={`platform_${record.osPlatform.toLowerCase()}`}
              title={record.osPlatform}
              className="text-lg mr-2"
            />
            {record.osPlatform}
          </div>
        );
      }
    },
    // {
    //   title: 'Architecture',
    //   dataIndex: 'osArch',
    //   key: 'osArch',
    //   sortable: false
    // },
    // {
    //   title: 'Bulletin ID',
    //   dataIndex: 'bulletinId',
    //   key: 'bulletinId',
    //   sortable: false
    // },
    // {
    //   title: 'Reboot Required?',
    //   dataIndex: 'rebootBehaviour',
    //   key: 'rebootBehaviour',
    //   render({ record }) {
    //     return rebootMap[record.rebootBehaviour];
    //   }
    // },

    {
      title: 'Release Date',
      dataIndex: 'releaseDate',
      key: 'releaseDate',
      sortable: false,
      type: 'datetime'
    },
    {
      title: 'Category',
      dataIndex: 'patchUpdateCategory',
      key: 'patchUpdateCategory',
      sortable: false,
      render({ record }) {
        return Capitalize(record?.patchUpdateCategory || '');
      }
    },
    {
      title: 'Patch Approval Status',
      dataIndex: 'patchApprovalStatus',
      key: 'patchApprovalStatus',
      sortable: false,
      hidden: true,
      render({ record, update }) {
        return (
          <Select
            value={record?.patchApprovalStatus.toLowerCase()}
            options={patchApprovalOptions}
            onChange={(value) => {
              update({ patchApprovalStatus: value, id: record.id });
            }}
          />
        );
      }
    },
    {
      title: 'Patch Test Status',
      dataIndex: 'patchTestStatus',
      key: 'patchTestStatus',
      sortable: false,
      hidden: true,
      render({ record }) {
        return Capitalize((record?.patchTestStatus || '').replace('_', ' '));
      }
    },

    {
      title: 'KBID',
      dataIndex: 'kbId',
      key: 'kbId',
      sortable: false,
      render({ record }) {
        return <ExternalLink url={record?.supportUrl}>{record?.kbId}</ExternalLink>;
      }
    },

    {
      title: 'CVE Number',
      dataIndex: 'cveNumber',
      key: 'cveNumber',
      sortable: false,
      render({ record }) {
        return <CopyOnDoubleClick text={record.cveNumber} />;
      }
    }

    // {
    //   title: 'Created On',
    //   dataIndex: 'createdAt',
    //   key: 'createdAt',
    //   type: 'datetime'
    // }
  ];

  const navigate = useNavigate();

  function onPatchExceptionAdd(formData) {
    return addPatchExceptionApi(selectedItems, formData).then(() => {
      setIsModalopen(false);
      setUpdateKey((prevKey) => prevKey + 1);

      return layout.message.success(`Vulnerability Exception Added.`);
    });
  }

  return (
    <>
      <PageHeading icon="patch" title={`Patches`} />
      <PatchLanguage.Provider>
        <CrudProvider
          key={`${JSON.stringify(filters)}-${updateKey}`}
          columns={columns}
          beforeCreateSlot={() => (
            <>
              {filters.state !== 'ignored' ? (
                <Button
                  type="primary"
                  className="mr-2"
                  onClick={() => {
                    if (selectedItems?.length) {
                      setIsModalopen(true);
                    } else {
                      layout.message.error(`Please Select One or more Patch to add Exception`);
                    }
                  }}>
                  Add Exceptions
                </Button>
              ) : null}
            </>
          )}
          allowSelection={filters.state !== 'ignored'}
          onChange={setSelectedItems}
          resourceTitle="Patch"
          defaultFormItem={{
            source: 'Manually',
            status: 'draft',
            patchApprovalStatus: 'approved'
          }}
          onView={(item) => navigate(`/patch/patches/${item.id}`)}
          hasSearch
          createFn={createPatchApi}
          createSlot={(createFn) => (
            <PermissionChecker permission={constants.Create_Patch}>
              <Button type="primary" onClick={createFn}>
                Create
              </Button>
            </PermissionChecker>
          )}
          fetchFn={(...args) => getAllPatchApi(...args, filters)}
          updateFn={changePatchApprovalStatusApi}
          formFields={(item) => <PatchForm item={item} />}
        />
      </PatchLanguage.Provider>
      <Drawer
        title={`Endpoints`}
        placement="right"
        width="70%"
        onClose={() => setShowAssetForPatch(null)}
        destroyOnClose
        open={Boolean(assetsForPatch)}>
        {Boolean(assetsForPatch) && (
          <User.Provider>
            <Department.Provider>
              <Location.Provider>
                <EndpointTabs patch={assetsForPatch} className="flex-1" forUsedCout={true} />
              </Location.Provider>
            </Department.Provider>
          </User.Provider>
        )}
      </Drawer>
      {isModalopen && (
        <ExceptionModel
          open={true}
          onAdd={onPatchExceptionAdd}
          onCancel={() => {
            setIsModalopen(false);
          }}
        />
      )}
    </>
  );
}
