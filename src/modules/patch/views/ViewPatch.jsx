import { Spin, Row, Col } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { getPatchApi, updatePatchApi } from '../api/patch-list';
import PageHeading from '@/src/components/PageHeading';
import PatchDetail from '../components/PatchDetail';
import { usePatchLayout } from '../layout/PatchLayout';

export default function ViewPatch() {
  const context = usePatchLayout();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [patch, setPatch] = useState(null);
  const params = useParams();

  useEffect(() => {
    context.displayNone();
    return () => {
      context.displayBlock();
    };
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    getPatchApi(params.id)
      .then((data) => {
        setPatch(data);
        setLoading(false);
      })
      .catch((e) => {
        navigate('/patch');
      });
    // eslint-disable-next-line
  }, [params]);

  function updatePatch(data) {
    updatePatchApi({ ...patch, ...data }).then((response) => setPatch(response));
  }

  return !loading && patch.id ? (
    <div className="flex flex-col min-h-0 h-full">
      <PageHeading icon="patch" title={`Patch`} />
      <Row className="h-full flex-1 min-h-0 relative">
        <Col span={24} className="h-full flex flex-col min-h-0">
          <PatchDetail patch={patch} onChange={updatePatch} />
        </Col>
      </Row>
    </div>
  ) : (
    <Spin spinning={loading}>
      <div className="flex flex-col min-w-0 min-h-0 flex-1" />
    </Spin>
  );
}
