import Icon from '@/src/components/Icon';
import { Row, Col } from 'antd';
import PatchCategoryPicker from './PatchCategoryPicker';
import PatchSeverityPicker from './PatchSeverityPicker';
import PatchArchPicker from './PatchArchPicker';

export default function PatchInfoRibbon({ patch }) {
  const approvalLabel = {
    approved: 'Approved',
    not_approved: 'Not Approved',
    decline: 'Decline'
  };

  const approvalColor = {
    approved: 'text-success',
    decline: 'text-danger',
    not_approved: 'text-warning'
  };
  return (
    <Row gutter={16} className="mb-4">
      <Col span={24}>
        <Row gutter={16}>
          <Col
            span={12}
            className="flex flex-col justify-around border-border border-solid border-r border-l-0 border-t-0 border-b-0">
            <div className="bg-seperator rounded px-4 py-2 h-full flex flex-col justify-around">
              <Row gutter={16} className="mb-2">
                <Col span={12}>
                  <Icon name="settings" className="mr-2" />
                  Category
                </Col>
                <Col span={12} className="font-semibold">
                  <PatchCategoryPicker disabled textOnly value={patch.patchUpdateCategory} />
                </Col>
              </Row>
              <Row gutter={16} className="mb-2">
                <Col span={12}>
                  <Icon name="os" className="mr-2" />
                  Severity
                </Col>
                <Col span={12} className="font-semibold">
                  <PatchSeverityPicker disabled textOnly value={patch.patchSeverity} />
                </Col>
              </Row>
              <Row gutter={16} className="mb-2">
                <Col span={12}>
                  <Icon name="agent_version" className="mr-2" />
                  Approval Status
                </Col>
                <Col
                  span={12}
                  className={`font-semibold ${approvalColor[patch.patchApprovalStatus]}`}>
                  {approvalLabel[patch.patchApprovalStatus]}
                </Col>
              </Row>
              <Row gutter={16} className="mb-2">
                <Col span={12}>
                  <Icon name="last_seen" className="mr-2" />
                  Bulletin ID
                </Col>
                <Col span={12} className="font-semibold">
                  {patch.bulletinId}
                </Col>
              </Row>
            </div>
          </Col>
          <Col span={12}>
            <div className="bg-seperator rounded px-4 py-2 h-full flex flex-col justify-around">
              <Row gutter={16} className="mb-2">
                <Col span={12}>
                  <Icon name="host" className="mr-2" />
                  KB
                </Col>
                <Col span={12} className="font-semibold">
                  {patch.kbId}
                </Col>
              </Row>
              <Row gutter={16} className="mb-2 flex items-center">
                <Col span={12}>
                  <Icon name="user" className="mr-2" />
                  UUID
                </Col>
                <Col span={12} className="font-semibold">
                  {patch.uuid}
                </Col>
              </Row>
              <Row gutter={16} className="mb-2 flex items-center">
                <Col span={12}>
                  <Icon name="department" className="mr-2" />
                  Reference URL
                </Col>
                <Col span={12} className="font-semibold">
                  <a href={patch.supportUrl} target="_blank" rel="noreferrer">
                    {patch.supportUrl}
                  </a>
                </Col>
              </Row>
              <Row gutter={16} className="mb-2 flex items-center">
                <Col span={12}>
                  <Icon name="location" className="mr-2" />
                  Architecture
                </Col>
                <Col span={12} className="font-semibold">
                  <PatchArchPicker value={patch.osArch} textOnly />
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Col>
    </Row>
  );
}
