import Icon from '@/src/components/Icon';
import Capitalize from 'lodash/capitalize';
import { Row, Col, Tag, Divider } from 'antd';
import PatchCategoryPicker from '../PatchCategoryPicker';
import PatchSeverityPicker from '../PatchSeverityPicker';
import PatchRebootPicker from '../PatchRebootOptionPicker';
import PatchArchPicker from '../PatchArchPicker';
import { useAuth } from '@/src/hooks/auth';
import { bytesToSize } from '@/src/utils/bytes';
import List from '@modules/vulnerability/views/List';

export default function SummaryTab({ patch }) {
  const { formatDateTime } = useAuth();
  const approvalLabel = {
    approved: 'Approved',
    not_approved: 'Not Approved',
    decline: 'Decline',
    pending_upload: 'Pending Upload'
  };

  const testLabel = {
    test_success: 'Success',
    test_failed: 'Failed',
    in_testing: 'In Testing',
    not_tested: 'Not Tested'
  };

  const approvalColor = {
    approved: 'text-success',
    decline: 'text-danger',
    not_approved: 'text-warning',
    pending_upload: 'text-danger'
  };

  const testColor = {
    test_success: 'text-success',
    test_failed: 'text-danger',
    in_testing: 'text-warning',
    not_tested: 'text-danger'
  };

  return (
    <Row>
      <Col span={24}>
        <Row gutter={8} className="pb-2 h-full">
          <Col span={24} className="h-full flex-1">
            <div className="flex flex-1 flex-col p-2 bg-seperator rounded-lg h-full">
              <div className="px-2 mb-1 flex justify-between">
                <div className="my-2 flex items-center">
                  <Icon name={`platform_${patch.osPlatform}`} className="text-5xl" />
                  <div className="font-semibold mx-2 text-xl">{patch.title}</div>
                </div>
              </div>
              <div>{patch.description}</div>
              <Divider className="my-2" />
              <Row gutter={32}>
                <Col span={6}>
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Category
                    </div>
                    <div className="font-bold text-base">
                      <PatchCategoryPicker disabled textOnly value={patch.patchUpdateCategory} />
                    </div>
                  </div>
                </Col>
                <Col span={6}>
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Severity
                    </div>
                    <div className="font-bold text-base">
                      <PatchSeverityPicker disabled textOnly value={patch.patchSeverity} />
                    </div>
                  </div>
                </Col>
                <Col span={6}>
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Approval Status
                    </div>
                    <div
                      className={`font-bold text-base ${approvalColor[patch.patchApprovalStatus]}`}>
                      {approvalLabel[patch.patchApprovalStatus]}
                    </div>
                  </div>
                </Col>
                <Col span={6}>
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Test Status
                    </div>
                    <div className={`font-bold text-base ${testColor[patch.patchTestStatus]}`}>
                      {testLabel[patch.patchTestStatus]}
                    </div>
                  </div>
                </Col>
              </Row>
              <Row gutter={32} className="mt-4">
                <Col span={6}>
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Bulletin ID
                    </div>
                    <div className="font-bold text-base">{patch.bulletinId || '---'}</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      KB
                    </div>
                    <div className="font-bold text-base">{patch.kbId || '---'}</div>
                  </div>
                </Col>
                <Col span={6}>
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Released On
                    </div>
                    <div className="font-bold text-base">
                      {patch.releaseDate && patch.releaseDate !== ''
                        ? formatDateTime(patch.releaseDate)
                        : '---'}
                    </div>
                  </div>
                </Col>
              </Row>
              <Row gutter={32} className="my-4">
                <Col span={6}>
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      UUID
                    </div>
                    <div className="font-bold text-base">{patch.uuid || '---'}</div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Reference URL
                    </div>
                    <div className="font-semibold text-ellipsis">
                      <a href={patch.supportUrl} target="_blank" rel="noreferrer">
                        {patch.supportUrl}
                      </a>
                    </div>
                  </div>
                </Col>
                <Col span={6}>
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Tags
                    </div>
                    <div>
                      {(patch.tags || []).map((tag) => (
                        <Tag color="processing" key={tag}>
                          {tag}
                        </Tag>
                      ))}
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Col>
      <Col span={24}>
        <Row gutter={8} className="pb-2 h-full">
          <Col span={24} className="h-full flex-1">
            <div className="flex flex-1 flex-col p-2 bg-seperator rounded-lg h-full">
              <Row gutter={32}>
                <Col span={6} className="my-2">
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Architecture
                    </div>
                    <div className="font-bold">
                      <PatchArchPicker value={patch.osArch} textOnly />
                    </div>
                  </div>
                </Col>
                <Col span={6} className="my-2">
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Source
                    </div>
                    <div className="font-bold">{Capitalize(patch.source)}</div>
                  </div>
                </Col>
                <Col span={6} className="my-2">
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Status
                    </div>
                    <div className="font-bold">{Capitalize(patch.status)}</div>
                  </div>
                </Col>
                <Col span={6} className="my-2">
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Download Status
                    </div>
                    <div className="font-bold">{Capitalize(patch.downloadStatus || '---')}</div>
                  </div>
                </Col>
                <Col span={6} className="my-2">
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Downloaded On
                    </div>
                    <div className="font-bold">{patch.downloadOn || '---'}</div>
                  </div>
                </Col>
                <Col span={6} className="my-2">
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Size
                    </div>
                    <div className="font-bold">{bytesToSize(patch.downloadSize) || '---'}</div>
                  </div>
                </Col>
                <Col span={6} className="my-2">
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Reboot Required
                    </div>
                    <div className="font-bold">
                      <PatchRebootPicker value={patch.rebootBehaviour} textOnly />
                    </div>
                  </div>
                </Col>
                <Col span={6} className="my-2">
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Created At
                    </div>
                    <div className="font-bold">{formatDateTime(patch.createdAt) || '---'}</div>
                  </div>
                </Col>
                <Col span={6} className="my-2">
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      Last Updated At
                    </div>
                    <div className="font-bold">{formatDateTime(patch.updatedAt) || '---'}</div>
                  </div>
                </Col>
                <Col span={6} className="my-2">
                  <div className="flex justify-center flex-col h-full">
                    <div className="text-xs inline-flex items-center text-neutral-light">
                      <Icon name="settings" className="mr-1" />
                      CVE Number
                    </div>
                    <div className="font-bold  text-ellipsis" title={patch.cveNumber}>
                      {patch.cveNumber || '---'}
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Col>
      <Col span={24} className=" flex flex-col  my-2 text-primary text-base grow-0">
        Vulnerabilities
      </Col>
      <Col span={24}>
        <List
          parentPage={'patch'}
          hideOverview={true}
          filters={{ cveNumber: patch.cveNumber, patchName: patch.name }}
        />
      </Col>
    </Row>
  );
}
