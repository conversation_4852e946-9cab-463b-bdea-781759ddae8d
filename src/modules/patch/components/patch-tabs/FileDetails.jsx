import PackageUploader, { ALLOWED_PACKAGE_EXTENSIONS } from '@/src/components/PackageUploader';
import { bytesToSize } from '@/src/utils/bytes';
import { Table, Divider, Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { useAuth } from '@/src/hooks/auth';

export default function FileDetails({ patch, onChange }) {
  const [files, setFiles] = useState([]);
  const { token } = useAuth();

  useEffect(() => {
    if (files.length) {
      let pendingFiles = files.filter((f) => f.status !== 'done');
      if (pendingFiles.length) {
        return;
      } else {
        onChange({ downloadFileDetails: files });
        setFiles([]);
      }
    }
  }, [files, onChange, patch]);

  return (
    <div className="flex flex-col flex-1 min-h-0">
      <div className="flex flex-col min-h-0 flex-1 overflow-auto">
        <div className="flex flex-col">
          <PackageUploader
            className="flex flex-col"
            value={files}
            extensions={ALLOWED_PACKAGE_EXTENSIONS}
            url={`/upload/patch/${patch.id}`}
            onChange={setFiles}
            multiple={true}
            useDragger
          />
        </div>
        <Divider className="my-2" />
        <div className="flex flex-col">
          <Table
            bordered
            showHeader={false}
            dataSource={patch.downloadFileDetails}
            pagination={false}
            tableLayout="fixed"
            columns={[
              {
                key: '#',
                title: '',
                width: '200px',
                dataIndex: 'key',
                render(text, _, index) {
                  return index + 1;
                }
              },
              {
                key: 'fileName',
                title: 'Name',
                dataIndex: 'fileName'
              },
              {
                key: 'size',
                title: 'Size',
                width: '120px',
                dataIndex: 'size',
                render(_, record) {
                  return bytesToSize(record.size);
                }
              },
              {
                key: 'actions',
                title: '',
                width: '120px',
                dataIndex: 'actions',
                render(_, record) {
                  return (
                    <>
                      <Button
                        type="link"
                        href={`${
                          record.downloadUrl ||
                          `/api/patch/download/patch/${record.refName}?mid=${token.access_token}`
                        }`}
                        title="Download">
                        <DownloadOutlined className="text-xl" />
                      </Button>
                    </>
                  );
                }
              }
            ]}
          />
        </div>
      </div>
    </div>
  );
}
