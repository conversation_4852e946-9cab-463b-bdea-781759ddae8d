import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON>, Row, Col, Form, Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { CrudProvider } from '@/src/hooks/crud';
import Score from '@/src/components/Score';
import SystemLogo from '@/src/components/SystemLogo';
import { Location } from '@/src/components/pickers/LocationPicker';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import { User } from '@/src/components/pickers/UserPicker';
import {
  addPatchAssetRelationApi,
  deletePatchAssetRelatinApi,
  getEndpointsForPatchApi,
  removePatchExceptionApi
} from '../../api/patch-list';
import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';
import PatchDeploymentDrawerForm from '@modules/patch/components/PatchDeploymentDrawerForm';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';

export default function EndpointTabs({ patch, forUsedCout }) {
  const categories = [
    {
      label: 'Missing',
      key: 'missing'
    },
    {
      label: 'Installed',
      key: 'installed'
    },
    {
      label: 'Exceptions',
      key: 'ignored'
    }
  ];

  const [countsMap, setCountsMap] = useState({});
  const navigate = useNavigate();
  const [endpointMap, setendpointMap] = useState({});
  const [modal, contextHolder] = Modal.useModal();
  const [selectedCategory, setSelectedCategory] = useState('missing');
  const [selectedItems, setSelectedItems] = useState([]);
  const [isPatchDeploymentFormOpen, setIsPatchDeploymentFormOpen] = useState(false);

  const columns = [
    {
      title: 'Host Name',
      dataIndex: 'hostname',
      width: 100,
      key: 'hostname',
      render({ record }) {
        return (
          <div className="flex items-center">
            {/* eslint-disable-next-line */}
            <a
              className="cursor-pointer"
              onClick={() => navigate(`/inventory/endpoints/${record.endpointId}`)}>
              {record.hostname}
            </a>
          </div>
        );
      }
    },
    {
      title: 'Risk',
      dataIndex: 'risk',
      key: 'risk',
      sortable: false,
      align: 'center',
      hidden: true,
      render({ record }) {
        return <Score value={record.risk} useCircle category="risk" size={40} />;
      }
    },
    {
      title: 'Assigned To',
      dataIndex: 'owner',
      key: 'owner',
      render({ record }) {
        return <User.Picker disabled textOnly value={record.owner} />;
      }
    },
    {
      title: 'OS Version',
      dataIndex: 'os_version',
      key: 'os_version',
      hidden: true,
      ellipsis: true
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
      hidden: true,
      render({ record }) {
        return <Department.Picker value={record.department} disabled textOnly />;
      }
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      render({ record }) {
        return <Location.Picker value={record.location} disabled textOnly />;
      }
    },
    {
      title: 'Vendor',
      dataIndex: 'hardware_vendor',
      key: 'hardware_vendor',
      render({ record }) {
        return (
          <>
            <div className="flex items-center">
              <SystemLogo
                name={record.hardware_vendor}
                className="w-8 flex-shrink-0"
                type="vendor"
                disabled
              />
              <span className="ml-1">{record.hardware_vendor}</span>
            </div>
          </>
        );
      }
    },
    {
      title: 'Hardware Model',
      dataIndex: 'hardware_model',
      key: 'hardware_model'
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version'
    },
    {
      title: 'Arch',
      dataIndex: 'arch',
      key: 'arch',
      hidden: true
    },
    {
      title: 'Build',
      dataIndex: 'build',
      key: 'build',
      hidden: true
    },
    {
      title: 'Code Name',
      dataIndex: 'code_name',
      key: 'code_name',
      hidden: true
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      deletePermissions: [constants.Delete_Patch],
      buttons: ['delete'],
      prependAction({ record, fetchData }) {
        if (selectedCategory === 'ignored') {
          return (
            <Button onClick={() => handleRemoveException(record, fetchData)}>
              Remove Exception
            </Button>
          );
        }
        return null;
      }
    }
  ];

  function handleRemoveException(record, fetchData) {
    modal.confirm({
      title: 'Confirm',
      icon: <ExclamationCircleOutlined />,
      content: `Are you sure you want to remove exception for ${record.hostname}?`,
      okText: 'Yes',
      cancelText: 'Cancel',
      centered: true,
      confirmLoading: true,
      destroyOnClose: true,
      maskClosable: false,
      okType: 'danger',
      onOk() {
        removePatchExceptionApi(record.patchId, {
          scope: { assetFilter: 2, assets: [+record.endpointId] }
        }).then(fetchData);
      }
    });
  }

  return (
    <div className={`flex flex-col min-h-0 overflow-auto ${forUsedCout ? 'h-full' : ''}`}>
      <div className={`flex min-h-0 ${forUsedCout ? 'flex-col h-full' : ''}`}>
        {/* {!forUsedCout ? ( */}
        <div className="flex flex-col">
          <Tabs
            activeKey={selectedCategory}
            tabPosition={!forUsedCout ? 'left' : undefined}
            onChange={(key) => setSelectedCategory(key)}
            style={{ height: `${forUsedCout ? 'unset' : 200}` }}
            items={categories.map((category) => ({
              label: `${category.label} ${
                category.key in countsMap ? `(${countsMap[category.key]})` : ''
              }`,
              key: category.key
            }))}
          />
        </div>
        {/* ) : null} */}

        <div className="flex flex-1 min-h-0 flex-col min-w-0">
          <CrudProvider
            defaultPageSize={20}
            titleColumn="hostname"
            columns={columns}
            key={selectedCategory}
            onChange={setSelectedItems}
            defaultFormItem={{
              patchState: selectedCategory,
              patchId: patch.id,
              scope: {}
            }}
            allowSelection={selectedCategory === 'missing'}
            drawerTitle={() => `Add ${selectedCategory} Endpoints`}
            resourceTitle="Endpoints"
            hasSearch
            deleteFn={deletePatchAssetRelatinApi}
            beforeCreateSlot={() => (
              <>
                {selectedItems?.length ? (
                  <PermissionChecker permission={constants.Create_Patch}>
                    <Button
                      type="primary"
                      className="mr-2"
                      onClick={() => {
                        setIsPatchDeploymentFormOpen(true);
                      }}>
                      Install
                    </Button>
                  </PermissionChecker>
                ) : null}
              </>
            )}
            createSlot={(createFn) =>
              patch.source === 'manually' ? (
                <Button type="primary" onClick={createFn}>
                  Add {selectedCategory} Endpoints
                </Button>
              ) : null
            }
            formActions={({ processingForm, resetForm, submitForm, formItem }) => (
              <>
                <Button
                  type="primary"
                  loading={processingForm}
                  className="mr-2"
                  onClick={() => {
                    submitForm();
                  }}>
                  Add
                </Button>
                <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
                  Reset
                </Button>
              </>
            )}
            fetchFn={(...args) =>
              getEndpointsForPatchApi(patch.id, ...args, {
                ...(selectedCategory === 'All' ? {} : { category: selectedCategory })
              }).then((data) => {
                setCountsMap((perv) => ({ ...perv, [selectedCategory]: data.totalCount }));
                setendpointMap(() =>
                  (data.result || []).reduce(
                    (acc, endpoint) => ({
                      ...acc,
                      [endpoint.id]: endpoint.endpointId
                    }),
                    {}
                  )
                );
                return data;
              })
            }
            createFn={addPatchAssetRelationApi}
            formFields={(item) => (
              <Row>
                <Col span={24}>
                  <Form.Item label=" " noStyle name="scope">
                    <AssetScopePicker
                      label="Scope"
                      gutter={16}
                      name={['scope', 'assetFilter']}
                      subname={['scope', 'assets']}
                    />
                  </Form.Item>
                </Col>
              </Row>
            )}
          />

          <PatchDeploymentDrawerForm
            defaultItem={{
              deploymentType: 'install',
              deploymentStage: 'draft',
              deploymentCategory: 'Patch',
              refIds: [+patch.id],
              assets: selectedItems,
              scope: {
                assetFilter: 2,
                assets: selectedItems.map((id) => endpointMap[id]).filter(Boolean)
              }
            }}
            isOpen={isPatchDeploymentFormOpen}
            onClose={() => setIsPatchDeploymentFormOpen(false)}
          />
        </div>
      </div>
      {contextHolder}
    </div>
  );
}
