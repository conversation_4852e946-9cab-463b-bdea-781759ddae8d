import { CrudProvider } from '@/src/hooks/crud';
import Capitalize from 'lodash/capitalize';
import { Tag } from 'antd';

import { getAllTaskHistorysApi } from '../api/task-history';

export default function TaskHistory({ config = {} }) {
  function getFormatedResult(result) {
    return atob(result);
  }
  const tagColorMap = {
    waiting: 'warning',
    initiated: 'processing',
    in_progress: 'processing',
    failed: 'error',
    cancelled: 'error',
    success: 'success',
    reboot_required: 'warning'
  };
  const columns = [
    {
      title: 'Id',
      dataIndex: 'id',
      key: 'id',
      sortable: false
    },
    {
      title: 'Created On',
      dataIndex: 'createdTime',
      key: 'createdTime',
      type: 'datetime',
      sortable: false
    },
    {
      title: 'Updated On',
      dataIndex: 'updatedTime',
      key: 'updatedTime',
      type: 'datetime',
      sortable: false
    },
    {
      title: 'Status',
      key: 'taskStatus',
      dataIndex: 'taskStatus',
      width: 80,
      align: 'center',
      sortable: false,
      render({ record }) {
        return (
          <Tag
            color={tagColorMap[record.taskStatus.toLowerCase()]}
            className="inline-flex items-center justify-center"
            style={{
              textAlign: 'center',
              textTransform: 'uppercase',
              minWidth: '80px'
            }}>
            {Capitalize((record.taskStatus || '').toLowerCase()).replaceAll('_', ' ')}
          </Tag>
        );
      }
    },
    {
      title: 'Task Result',
      dataIndex: 'result',
      key: 'result',
      ellipsis: true,
      width: '30%',
      render({ record, view }) {
        const hasLink = ['failed'].includes(record.taskStatus);
        return (
          <div className="flex items-center">
            {hasLink ? (
              <div
                className={hasLink ? 'cursor-pointer text-primary' : ''}
                onClick={() => (hasLink ? view(record) : null)}>
                View Result
              </div>
            ) : null}
          </div>
        );
      }
    }
  ];

  return (
    <CrudProvider
      hasSearch={false}
      columns={columns}
      resourceTitle="Deployment Task"
      disableFormScrolling
      disableColumnSelection
      drawerTitle={(item) => `Result`}
      fetchFn={(...args) => getAllTaskHistorysApi(...args, { config })}
      formFields={(item) => (
        <div className="py-4">
          <div style={{ whiteSpace: 'pre-line' }}>{getFormatedResult(item.taskResult)}</div>
        </div>
      )}
    />
  );
}
