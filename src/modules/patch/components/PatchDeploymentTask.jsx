import { Tag } from 'antd';
import Capitalize from 'lodash/capitalize';
import { CrudProvider } from '@/src/hooks/crud';
import { getAllDeploymentTasktsApi } from '../api/patch-deployement';
import { User } from '@/src/components/pickers/UserPicker';
import { useNavigate } from 'react-router-dom';

// import { Asset } from '@/src/components/pickers/AssetPicker';
// import { Link } from 'react-router-dom';
// import PackageLogo from './PackageLogo';
// import { Asset } from '@/src/components/pickers/AssetPicker';

export default function PatchDeploymentTask({ deploymentId }) {
  const navigate = useNavigate();

  const tagColorMap = {
    waiting: 'warning',
    initiated: 'processing',
    in_progress: 'processing',
    failed: 'error',
    cancelled: 'error',
    success: 'success',
    reboot_required: 'warning'
  };

  const columns = [
    // {
    //   title: 'Endpoint',
    //   dataIndex: 'endpoint',
    //   key: 'endpoint',
    //   width: '20%',
    //   render({ record }) {
    //     return (
    //       <Link to={`/inventory/endpoints/${record.endpoint}`}>
    //         <Asset.Picker value={record.endpoint} disabled textOnly />
    //       </Link>
    //     );
    //   }
    // },
    {
      title: 'Name',
      dataIndex: 'displayName',
      key: 'displayName',
      ellipsis: true,
      width: '30%',
      render({ record, view }) {
        const hasLink = ['success', 'failed', 'reboot_required'].includes(record.taskStatus);
        return (
          <div className="flex items-center">
            {/* <PackageLogo package={record} style={{ width: '30px' }} className="mr-2" disabled /> */}
            <div
              className={hasLink ? 'cursor-pointer text-primary' : ''}
              onClick={() => (hasLink ? view(record) : null)}>
              {record.displayName || 'View Output'}
            </div>
          </div>
        );
      }
    },

    {
      title: 'Status',
      key: 'taskStatus',
      dataIndex: 'taskStatus',
      width: 80,
      align: 'center',
      sortable: false,
      render({ record }) {
        return (
          <Tag
            color={tagColorMap[record.taskStatus.toLowerCase()]}
            className="inline-flex items-center justify-center"
            style={{
              textAlign: 'center',
              textTransform: 'uppercase',
              minWidth: '80px'
            }}>
            {Capitalize((record.taskStatus || '').toLowerCase()).replaceAll('_', ' ')}
          </Tag>
        );
      }
    },
    {
      title: 'Endpoint Name',
      key: 'assetName',
      dataIndex: 'assetName',
      width: 80,
      sortable: false,
      render({ record }) {
        return (
          <div className="flex items-center">
            {/* eslint-disable-next-line */}
            <a
              className="cursor-pointer"
              onClick={() => navigate(`/inventory/endpoints/${record.agentId}`)}
              target="_blank">
              {record.assetName}
            </a>
          </div>
        );
      }
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      sortable: false,
      render({ record }) {
        return <User.Picker textOnly value={record.createdBy} disabled />;
      }
    },
    {
      title: 'Execution finish time',
      dataIndex: 'updatedTime',
      key: 'updatedTime',
      type: 'datetime'
    },

    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    }
  ];

  return (
    <CrudProvider
      columns={columns}
      resourceTitle="Deployment Task"
      disableFormScrolling
      disableColumnSelection
      hasSearch
      drawerTitle={(item) => `${item.displayName} Output`}
      fetchFn={(...args) => getAllDeploymentTasktsApi(...args, { deploymentId })}
      formFields={(item) => (
        <div className="py-4">
          <div style={{ whiteSpace: 'pre-line' }}>{item.result}</div>
        </div>
      )}
    />
  );
}
