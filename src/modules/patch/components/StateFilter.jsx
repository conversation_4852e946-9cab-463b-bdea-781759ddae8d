import { Tree } from 'antd';
// import { useLocation, useNavigate } from 'react-router-dom';

export default function StateFilter({ onSelect, value }) {
  const platforms = [
    { key: 'all', title: 'All' },
    { key: 'installed', title: 'Installed' },
    { key: 'missing', title: 'Missing' }
  ];
  //   const route = useLocation();
  //   const navigate = useNavigate();
  function onSelectItem(data, e) {
    // eslint-disable-next-line
    onSelect(e.node.key);
    // const paths = route.pathname
    //   .split('/')
    //   .filter((i) => !/^\d+$/.test(i))
    //   .join('/');
    // navigate(paths);
  }

  return (
    <Tree
      className="inventory-tree my-2"
      blockNode
      selectedKeys={[value]}
      autoExpandParent
      titleRender={(item) => {
        return (
          <div
            className="flex items-center text-label flex-1 justify-between"
            style={{ whiteSpace: 'nowrap' }}>
            <div>{item.title}</div>
            {/* <Badge
              showZero
              overflowCount={9999999999}
              className="ml-2 count-badge"
              count={item.count} */}
            {/* /> */}
          </div>
        );
      }}
      onSelect={onSelectItem}
      treeData={platforms}
    />
  );

  // return (
  //   <Menu
  //     mode="inline"
  //     defaultSelectedKeys={['all']}
  //     className="settings-menu"
  //     style={{ borderInlineEnd: 'none' }}
  //     items={platforms.map((item) => ({
  //       key: item.key,
  //       label: (
  //         <div
  //           className="flex items-center text-label flex-1 justify-between"
  //           style={{ whiteSpace: 'nowrap' }}>
  //           <div>
  //             {item.key !== 'all' ? (
  //               <Icon name={`platform_${item.key.toLowerCase()}`} className="mr-2 text-lg" />
  //             ) : null}
  //             {item.title}{' '}
  //           </div>
  //           {/* <Badge
  //               showZero
  //               overflowCount={9999999999}
  //               className="ml-2 count-badge"
  //               count={item.count}
  //             /> */}
  //         </div>
  //       )
  //     }))}
  //     onSelect={handleSeveritySelected}
  //   />
  // );
}
