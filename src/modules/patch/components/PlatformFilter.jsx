import { Tree } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '@/src/components/Icon';

export default function PlatformFilters({ onSelect, value }) {
  const platforms = [
    { key: 'all', title: 'All' },
    { key: 'windows', title: 'Windows' },
    { key: 'ubuntu', title: 'Linux' },
    { key: 'mac', title: 'macOS' }
  ];
  const route = useLocation();
  const navigate = useNavigate();
  function onSelectItem(data, e) {
    // eslint-disable-next-line
    onSelect(e.node.key);
    const paths = route.pathname
      .split('/')
      .filter((i) => !/^\d+$/.test(i))
      .join('/');
    navigate(paths);
  }

  return (
    <Tree
      className="inventory-tree my-2"
      blockNode
      selectedKeys={[value]}
      autoExpandParent
      titleRender={(item) => {
        return (
          <div
            className="flex items-center text-label flex-1 justify-between"
            style={{ whiteSpace: 'nowrap' }}>
            <div>
              {item.key !== 'all' ? (
                <Icon
                  name={`platform_${item.key === 'ubuntu' ? 'linux' : item.key.toLowerCase()}`}
                  className="mr-2 text-lg"
                />
              ) : null}
              {item.title}{' '}
            </div>
            {/* <Badge
              showZero
              overflowCount={9999999999}
              className="ml-2 count-badge"
              count={item.count} */}
            {/* /> */}
          </div>
        );
      }}
      onSelect={onSelectItem}
      treeData={platforms}
    />
  );

  // return (
  //   <Menu
  //     mode="inline"
  //     defaultSelectedKeys={['all']}
  //     className="settings-menu"
  //     style={{ borderInlineEnd: 'none' }}
  //     items={platforms.map((item) => ({
  //       key: item.key,
  //       label: (
  //         <div
  //           className="flex items-center text-label flex-1 justify-between"
  //           style={{ whiteSpace: 'nowrap' }}>
  //           <div>
  //             {item.key !== 'all' ? (
  //               <Icon name={`platform_${item.key.toLowerCase()}`} className="mr-2 text-lg" />
  //             ) : null}
  //             {item.title}{' '}
  //           </div>
  //           {/* <Badge
  //               showZero
  //               overflowCount={9999999999}
  //               className="ml-2 count-badge"
  //               count={item.count}
  //             /> */}
  //         </div>
  //       )
  //     }))}
  //     onSelect={handleSeveritySelected}
  //   />
  // );
}
