import React from 'react';
// import { useState } from 'react';
import { Button } from 'antd';

import DrawerForm from '@/src/components/DrawerForm';
import { Asset } from '@/src/components/pickers/AssetPicker';
import { DeploymentPolicy } from '@/src/components/pickers/DeploymentPolicyPicker';
import { User } from '@/src/components/pickers/UserPicker';

import PatchDeploymentForm from '@modules/patch/components/PatchDeploymentForm';
import { createPatchDeploymentApi } from '@modules/patch/api/patch-deployement';

const PatchDeploymentDrawerForm = ({ defaultItem, isOpen, onClose }) => {
  //   const [isOpen, setIsOpen] = useState(false);

  async function submitPatchdeploymentApi(data) {
    return createPatchDeploymentApi(data).then((data) => {
      onClose();
      return data;
    });
  }

  return isOpen ? (
    <Asset.Provider>
      <DeploymentPolicy.Provider>
        <User.Provider>
          <DrawerForm
            drawerTitle="Create Patch Deployment"
            isOpen={isOpen}
            createFn={submitPatchdeploymentApi}
            formFields={(item, _, { disabled }) => (
              <PatchDeploymentForm item={item} disabled={disabled} />
            )}
            defaultItem={defaultItem}
            onClose={onClose}
            formActions={({ processingForm, resetForm, submitForm, formItem }) => (
              <>
                <Button
                  type="primary"
                  loading={processingForm}
                  className="mr-2"
                  onClick={() => {
                    submitForm({
                      deploymentStage:
                        formItem.deploymentStage === 'draft'
                          ? 'initiated'
                          : formItem.deploymentStage || 'initiated'
                    });
                  }}>
                  Publish
                </Button>
                {formItem.deploymentStage === 'draft' ? (
                  <Button
                    type="primary"
                    loading={processingForm}
                    className="mr-2"
                    onClick={() => {
                      submitForm({
                        deploymentStage: 'draft'
                      });
                    }}>
                    Save As Draft
                  </Button>
                ) : null}
                <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
                  Reset
                </Button>
              </>
            )}
          />
        </User.Provider>
      </DeploymentPolicy.Provider>
    </Asset.Provider>
  ) : null;
};

export default PatchDeploymentDrawerForm;
