import { useState } from 'react';
import constants from '@/src/constants/index';
import CloneDeep from 'lodash/cloneDeep';
import { FileOutlined, WarningOutlined } from '@ant-design/icons';
import { Tabs, Row, Drawer, Form, Col, Dropdown, Button, Modal } from 'antd';
import { CloseCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';
import SummaryTab from './patch-tabs/SummaryTab';
import Icon from '@/src/components/Icon';
import PatchInfoRibbon from './PatchInfoRibbon';
import PatchForm from './PatchForm';
import { PatchLanguage } from '@/src/components/pickers/PatchLanguagePicker';
import { updatePatchApi, changePatchApprovalStatusApi } from '../api/patch-list';
import EndpointTabs from './patch-tabs/Endpoints';
import AffectedProductsTab from './patch-tabs/AffectedProducts';
import { User } from '@/src/components/pickers/UserPicker';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import { Location } from '@/src/components/pickers/LocationPicker';
import FileDetails from './patch-tabs/FileDetails';
import { Permissions } from '@/src/components/Permissions';

import PatchDeploymentDrawerForm from '@modules/patch/components/PatchDeploymentDrawerForm';

export default function PatchDetail({ patch, onChange }) {
  const [activeKey, setActiveKey] = useState('summary');
  const [modal, contextHolder] = Modal.useModal();
  const [processingForm, setProcessingForm] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isPatchDeploymentFormOpen, setIsPatchDeploymentFormOpen] = useState(false);
  const { hasPermission } = Permissions.usePermission();

  const actions = hasPermission(constants.Update_Patch)
    ? [
        {
          key: 'edit',
          label: (
            <span className="inline-flex items-center">
              <Icon name="edit" className="mr-1 text-lg" />
              <span>Edit</span>
            </span>
          )
        },
        ...(patch.source === 'manually' && patch.status === 'draft'
          ? [
              {
                key: 'publish',
                label: (
                  <span className="inline-flex items-center text-success">
                    <CheckCircleOutlined className="mr-1 text-lg" />
                    <span>Publish</span>
                  </span>
                )
              }
            ]
          : []),
        ...(patch.patchApprovalStatus === 'not_approved'
          ? [
              {
                key: 'approved',
                label: (
                  <span className="inline-flex items-center text-success">
                    <CheckCircleOutlined className="mr-1 text-lg" />
                    <span>Approve</span>
                  </span>
                )
              }
            ]
          : []),
        ...(patch.patchApprovalStatus === 'decline'
          ? [
              {
                key: 'not_approved',
                label: (
                  <span className="inline-flex items-center text-warning">
                    <WarningOutlined className="mr-1 text-lg" />
                    <span>Not Approve</span>
                  </span>
                )
              },
              {
                key: 'approved',
                label: (
                  <span className="inline-flex items-center text-success">
                    <CheckCircleOutlined className="mr-1 text-lg" />
                    <span>Approve</span>
                  </span>
                )
              }
            ]
          : []),
        ...(patch.patchApprovalStatus === 'approved' || patch.patchApprovalStatus === 'not_approved'
          ? [
              ...(patch.patchApprovalStatus === 'approved'
                ? [
                    {
                      key: 'not_approved',
                      label: (
                        <span className="inline-flex items-center text-warning">
                          <WarningOutlined className="mr-1 text-lg" />
                          <span>Not Approve</span>
                        </span>
                      )
                    }
                  ]
                : []),
              {
                key: 'decline',
                label: (
                  <span className="inline-flex items-center">
                    <CloseCircleOutlined className="mr-1 text-lg" />
                    <span>Decline</span>
                  </span>
                ),
                danger: true
              }
            ]
          : []),
        ...(patch.patchApprovalStatus === 'approved'
          ? [
              {
                key: 'install',
                label: (
                  <span className="inline-flex items-center">
                    <Icon name="install" className="mr-1 text-lg" />
                    <span>Install</span>
                  </span>
                )
              }
            ]
          : [])
      ]
    : [];

  const tabs = [
    {
      label: (
        <span className="flex items-center">
          <Icon name="settings" className="text-lg" />
          Summary
        </span>
      ),
      key: 'summary',
      component: <SummaryTab patch={patch} />
    },
    {
      label: (
        <span className="flex items-center">
          <Icon name="inventory" className="text-lg" />
          Endpoints
        </span>
      ),
      key: 'endpoints',
      component: <EndpointTabs patch={patch} />
    },
    {
      label: (
        <span className="flex items-center">
          <Icon name="automation" className="text-lg" />
          Affected Products
        </span>
      ),
      key: 'affected_products',
      component: <AffectedProductsTab patch={patch} />
    },
    {
      label: (
        <span className="flex items-center">
          <FileOutlined className="text-lg" />
          File Details
        </span>
      ),
      key: 'file_details',
      component: <FileDetails patch={patch} onChange={onChange} />
    }
  ];

  function submitForm(data) {
    setProcessingForm(true);
    updatePatchApi({ ...data, id: patch.id })
      .then((response) => {
        onChange(response);
        setProcessingForm(false);
        setIsEditing(false);
      })
      .catch((e) => {
        setProcessingForm(false);
      });
  }

  function onMenuClick({ key }) {
    if (key === 'edit') {
      return setIsEditing(true);
    }
    if (key === 'decline') {
      return modal.confirm({
        title: 'Confirm',
        icon: (
          <span className="text-danger text-base mr-2">
            <CloseCircleOutlined />
          </span>
        ),
        content: `Are you sure you want to decline ${patch.title}?`,
        okText: 'Yes',
        cancelText: 'Cancel',
        centered: true,
        confirmLoading: true,
        destroyOnClose: true,
        maskClosable: false,
        okType: 'danger',
        onOk() {
          return changePatchApprovalStatusApi({
            id: patch.id,
            patchApprovalStatus: 'decline'
          }).then((p) => onChange(p));
        }
      });
    }
    if (key === 'approved') {
      return modal.confirm({
        title: 'Confirm',
        icon: (
          <span className="text-success text-base mr-2">
            <CheckCircleOutlined />
          </span>
        ),
        content: `Are you sure you want to approve ${patch.title}?`,
        okText: 'Yes',
        cancelText: 'Cancel',
        centered: true,
        confirmLoading: true,
        destroyOnClose: true,
        maskClosable: false,
        okType: 'primary',
        onOk() {
          return changePatchApprovalStatusApi({
            id: patch.id,
            patchApprovalStatus: 'approved'
          }).then((p) => onChange(p));
        }
      });
    }
    if (key === 'not_approved') {
      return modal.confirm({
        title: 'Confirm',
        icon: (
          <span className="text-success text-base mr-2">
            <CheckCircleOutlined />
          </span>
        ),
        content: `Are you sure you want to not approve ${patch.title}?`,
        okText: 'Yes',
        cancelText: 'Cancel',
        centered: true,
        confirmLoading: true,
        destroyOnClose: true,
        maskClosable: false,
        okType: 'primary',
        onOk() {
          return changePatchApprovalStatusApi({
            id: patch.id,
            patchApprovalStatus: 'not_approved'
          }).then((p) => onChange(p));
        }
      });
    }
    if (key === 'publish') {
      return modal.confirm({
        title: 'Confirm',
        icon: (
          <span className="text-success text-base mr-2">
            <CheckCircleOutlined />
          </span>
        ),
        content: `Are you sure you want to publish ${patch.title}?`,
        okText: 'Yes',
        cancelText: 'Cancel',
        centered: true,
        confirmLoading: true,
        destroyOnClose: true,
        maskClosable: false,
        okType: 'primary',
        onOk() {
          return updatePatchApi({ id: patch.id, status: 'publish' }).then((p) => onChange(p));
        }
      });
    }

    if (key === 'install') {
      setIsPatchDeploymentFormOpen(true);
    }
  }

  return (
    <User.Provider>
      <Department.Provider>
        <Location.Provider>
          <div className="flex flex-col min-h-0 flex-1">
            <Row gutter={16} className="h-full flex-1 min-h-0">
              <Col span={24} className="h-full flex flex-col py-2 flex-1">
                <Row gutter={16}>
                  <Col span={24}>
                    <div className="pr-2">
                      <div className="bg-seperator py-2 pl-2 rounded-lg mb-2 flex justify-between min-w-0">
                        <div className="flex-1 min-w-0">
                          <Tabs
                            activeKey={activeKey}
                            onChange={(key) => {
                              setActiveKey(key);
                            }}
                            items={tabs}
                            className="sticky-tabs transparent no-border no-margin"
                          />
                        </div>
                        <Dropdown
                          menu={{ items: actions, onClick: onMenuClick }}
                          trigger="click"
                          placement="bottomRight">
                          <Button
                            type="link"
                            className="flex-shrink-0"
                            onClick={(e) => e.preventDefault()}>
                            <span className="inline-flex items-center text-color">
                              <Icon name="settings_dropdown" className="mr-1 text-2xl" />
                              <Icon name="chevron-down" />
                            </span>
                          </Button>
                        </Dropdown>
                      </div>
                    </div>
                  </Col>
                </Row>
                <div className="flex-1 flex flex-col min-h-0 overflow-y-auto pr-2">
                  {activeKey === 'summary' ? null : <PatchInfoRibbon patch={patch} />}
                  {tabs.find((tab) => tab.key === activeKey).component}
                </div>
              </Col>
            </Row>
            {contextHolder}
            <Drawer
              title={`Edit ${patch.title}`}
              width={'50%'}
              onClose={() => setIsEditing(false)}
              destroyOnClose
              maskClosable={false}
              open={isEditing}>
              <Form
                layout="vertical"
                className="h-full"
                onFinish={submitForm}
                initialValues={CloneDeep(patch)}>
                <Row className="h-full">
                  <div className="flex flex-1 min-h-0 flex-col h-full min-w-0">
                    <div className={`flex-1 min-h-0 ${'overflow-auto overflow-x-hidden'}`}>
                      <Col span={24} className="flex-1 h-full">
                        <PatchLanguage.Provider>
                          <PatchForm item={patch} />
                        </PatchLanguage.Provider>
                      </Col>
                    </div>
                    <div className="flex-shrink-0">
                      <Col span={24} className="text-right">
                        <Button
                          type="primary"
                          loading={processingForm}
                          htmlType="submit"
                          className="mr-2">
                          Update
                        </Button>
                        <Button type="primary" ghost htmlType="reset">
                          Reset
                        </Button>
                      </Col>
                    </div>
                  </div>
                </Row>
              </Form>
            </Drawer>
            <PatchDeploymentDrawerForm
              defaultItem={{
                deploymentType: 'install',
                deploymentStage: 'draft',
                deploymentCategory: 'Patch',
                refIds: [+patch.id]
              }}
              isOpen={isPatchDeploymentFormOpen}
              onClose={() => setIsPatchDeploymentFormOpen(false)}
            />
          </div>
        </Location.Provider>
      </Department.Provider>
    </User.Provider>
  );
}
