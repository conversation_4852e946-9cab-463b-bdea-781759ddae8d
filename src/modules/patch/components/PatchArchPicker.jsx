import { Select } from 'antd';

export default function PatchArchPicker({ textOnly, ...props }) {
  const archOptions = [
    { label: '64 BIT', value: 'x64' },
    { label: '32 BIT', value: 'x86' }
  ];
  if (textOnly) {
    const arch = archOptions.find((s) => s.value === props.value);
    return (arch || {}).label || '---';
  }
  return <Select options={archOptions} placeholder="Select" {...props} />;
}
