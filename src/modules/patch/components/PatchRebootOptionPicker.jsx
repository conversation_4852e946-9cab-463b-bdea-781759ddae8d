import { Select } from 'antd';
import Capitalize from 'lodash/capitalize';

export default function PatchRebootPicker({ textOnly, ...props }) {
  const rebootOptions = ['may_be', 'yes', 'no'].map((i) => ({
    label: i
      .split('_')
      .map((i) => Capitalize(i))
      .join(' '),
    value: i
  }));
  if (textOnly) {
    const reboot = rebootOptions.find((s) => s.value === props.value);
    return (reboot || {}).label || '---';
  }
  return <Select options={rebootOptions} placeholder="Select" {...props} />;
}
