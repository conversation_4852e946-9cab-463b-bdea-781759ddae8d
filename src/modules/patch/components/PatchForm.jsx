import PlatformPicker from '@/src/components/pickers/PlatformPicker';
import { Input, Form, Col, Row, Switch, Select } from 'antd';
import { PatchLanguage } from '@/src/components/pickers/PatchLanguagePicker';
import AffectedProducts from '@/src/components/pickers/AffectedProducts';
// import PackageUploader from '@/src/components/PackageUploader';
import PatchCategoryPicker from './PatchCategoryPicker';
import PatchSeverityPicker from './PatchSeverityPicker';
import PatchArchPicker from './PatchArchPicker';
import PatchRebootPicker from './PatchRebootOptionPicker';

export default function PatchForm({ item = {} }) {
  // const form = Form.useFormInstance();

  return (
    <>
      <Row gutter={32}>
        <Col span={12}>
          <Form.Item label="Title" name="title" rules={[{ required: true }]}>
            <Input placeholder="Title" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Platform" name="osPlatform" rules={[{ required: true }]}>
            <PlatformPicker />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Description" name="description">
            <Input.TextArea placeholder="Description" rows={5} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Category" name="patchUpdateCategory" rules={[{ required: true }]}>
            <PatchCategoryPicker />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Severity" name="patchSeverity" rules={[{ required: true }]}>
            <PatchSeverityPicker />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Bulletin ID" name="bulletinId">
            <Input placeholder="Bulletin ID" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="KB Number" name="kbId">
            <Input placeholder="KB Number" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Release Date" name="releaseDate">
            <Input placeholder="Release Date" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Reboot Required" name="rebootBehaviour" rules={[{ required: true }]}>
            <PatchRebootPicker />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Support Uninstallation" name="isUninstallable" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Architecture" name="osArch" rules={[{ required: true }]}>
            <PatchArchPicker />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Reference URL" name="supportUrl">
            <Input placeholder="Reference URL" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Languages Supported" name="languageSupported">
            <PatchLanguage.Picker mode="multiple" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Tags" name="tags">
            <Select mode="tags" placeholder="Add Tags" />
          </Form.Item>
        </Col>
        {item.id ? null : (
          <>
            <Col span={24}>
              <Form.Item
                label="Affected Products"
                name="affectedProducts"
                rules={[{ required: true }]}>
                <AffectedProducts />
              </Form.Item>
            </Col>
            {/* <Col span={24}>
              <Form.Item
                label="Patch Files"
                name="downloadFileDetails"
                rules={[{ required: true }]}>
                <PackageUploader useDragger multiple={true} />
              </Form.Item>
            </Col> */}
          </>
        )}
      </Row>
    </>
  );
}
