import api from '@api';

const sortKeyMap = {};

const taskSearchableColumns = [];
export function getAllTaskHistorysApi(offset, size, sortFilter, { config = {} }) {
  return api
    .post(`/patch/agent/task/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {
            sortBy: 'updatedTime'
          }),
      qualification: [
        ...(config.type
          ? [
              {
                operator: 'Equals',
                column: 'task_type',
                value: config.type,
                condition: 'and'
              }
            ]
          : []),
        ...(config.assetId
          ? [
              {
                operator: 'Equals',
                column: 'agent_id',
                value: config.assetId,
                condition: 'and'
              }
            ]
          : [])
      ].concat(
        sortFilter.searchTerm
          ? taskSearchableColumns.map((c) => ({
              operator: 'Contains',
              column: c,
              value: sortFilter.searchTerm,
              condition: 'or'
            }))
          : []
      )
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result || []
      };
    });
}
