import {
  transformAssetScope,
  transformAssetScopeForServer
} from '@/src/components/pickers/AssetScopePicker';
import api from '@api';

const END_POINT = `/fim`;

const transformContext = (item) => {
  return [
    {
      category: item.category,
      config_type: item.config_type,
      include_paths: item.include_path.join('\n'),
      exclude_paths: (item.exclude_path || []).join('\n')
    }
  ];
};

const transformContextForServer = (paths) => {
  return {
    category: paths[0].category,
    config_type: paths[0].config_type,
    include_path: paths[0].include_paths.split('\n'),
    exclude_path: (paths[0].exclude_paths || '').split('\n')
  };
};

const transform = (item) => ({
  id: item.id,
  name: item.name,
  description: item.description,
  ...transformAssetScope(item),
  context: transformContext(item.context),
  createdAt: item.created_time,
  createdBy: item.created_by,
  status: <PERSON><PERSON><PERSON>(item.status)
});

const transformForServer = (item) => ({
  id: item.id,
  name: item.name,
  description: item.description,
  ...transformAssetScopeForServer(item),
  status: item.status ? 1 : 0,
  context: transformContextForServer(item.context)
});

const sortKeyMap = {
  createdAt: 'created_time'
};

const searchableColumns = ['name', 'description', 'context'];

export function getAllConfigurationApi(offset, size, sortFilter = {}) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getConfigurationApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateConfigurationApi(item) {
  return api
    .put(`${END_POINT}/${item.id}`, transformForServer(item))
    .then((data) => getConfigurationApi(data.result));
}

export function createConfigurationApi(item) {
  return api
    .post(`${END_POINT}`, transformForServer(item))
    .then((data) => getConfigurationApi(data.result));
}

export function deleteConfigurationApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}

export function getAvailableVersionsApi(assetId, configId, filePath) {
  return api
    .post(`/fim/events/config/versions`, {
      asset_id: assetId,
      config_id: configId,
      file_path: filePath
    })
    .then(({ result }) => result);
}

export function getFileContentApi(eventId) {
  return api.get(`/fim/events/config/${eventId}`).then(({ result }) => result);
}
