import { Row, Col } from 'antd';
import XLSXViewer from './XLSXViewer';

export default function XLSXDiffViewer({ oldValue, newValue }) {
  return (
    <div className="overflow-x-hidden flex flex-col flex-1 h-full">
      <Row gutter={16} className="min-h-0 h-full">
        <Col span={12} className="h-full overflow-y-auto">
          {oldValue !== undefined ? <XLSXViewer file={oldValue} /> : null}
        </Col>
        <Col span={12} className="h-full overflow-y-auto">
          <div className="h-full overflow-y-auto pr-2">
            {newValue !== undefined ? <XLSXViewer file={newValue} /> : null}
          </div>
        </Col>
      </Row>
    </div>
  );
}
