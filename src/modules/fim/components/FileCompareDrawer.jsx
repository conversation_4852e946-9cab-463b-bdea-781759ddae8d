import { Drawer, Row, Col, Select, Form } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { LockOutlined } from '@ant-design/icons';
import FindIndex from 'lodash/findIndex';
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import { getAvailableVersionsApi, getFileContentApi } from '../api/configuration-api';
import Loading from '@/src/components/Loading';
import { useLayout } from '@/src/layouts/Layout';
import { useAuth } from '@/src/hooks/auth';
import Icon from '@/src/components/Icon';
import PDFDiffViewer from './PDFDiffViewer';
import XLSXDiffViewer from './XLSXDiffViewer';
import DocxDiffViewer from './DocxDiffViewer';

export default function FileCompareDrawer({ onClose, event }) {
  const { theme } = useLayout();
  const { formatDateTime } = useAuth();
  const [versions, setVersions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedLhsVersion, setSelectedLhsVersion] = useState(undefined);
  const [selectedRhsVersion, setSelectedRhsVersion] = useState(undefined);
  const [selectedLhsVersionDetail, setSelectedLhsVersionDetail] = useState(``);
  const [selectedRhsVersionDetail, setSelectedRhsVersionDetail] = useState(``);

  useEffect(() => {
    getAvailableVersionsApi(event.asset_id, event.config_id, event.target_path).then((data) => {
      setVersions(data);
      let rhsIndex = FindIndex(data, (i) => i.event_id === event.event_id);
      if (rhsIndex !== -1) {
        setSelectedRhsVersion(data[rhsIndex].event_id);
        setSelectedLhsVersion(data[rhsIndex + 1].event_id);
        setLoading(false);
      }
    });
  }, [event.asset_id, event.config_id, event.event_id, event.target_path]);

  useEffect(() => {
    if (selectedLhsVersion) {
      getFileContentApi(selectedLhsVersion).then((data) => {
        setSelectedLhsVersionDetail(data);
      });
    }
  }, [selectedLhsVersion, versions]);

  useEffect(() => {
    if (selectedRhsVersion) {
      getFileContentApi(selectedRhsVersion).then((data) => {
        setSelectedRhsVersionDetail(data);
      });
    }
  }, [selectedRhsVersion, versions]);

  const versionsOptions = useMemo(
    () =>
      versions.map((version) => ({
        label: (
          <div className="flex justify-between">
            <span>V{version.file_version}</span>
            <span>{formatDateTime(version.created_time, false)}</span>
          </div>
        ),
        value: version.event_id
      })),
    // eslint-disable-next-line
    [versions]
  );

  let diffNode = useMemo(() => {
    let extension = event.target_path.split('.').pop();
    if (extension === 'pdf') {
      return (
        <PDFDiffViewer
          oldValue={selectedLhsVersionDetail.file_content}
          newValue={selectedRhsVersionDetail.file_content}
        />
      );
    } else if (extension === 'xls' || extension === 'xlsx') {
      return (
        <XLSXDiffViewer
          oldValue={selectedLhsVersionDetail.file_content}
          newValue={selectedRhsVersionDetail.file_content}
        />
      );
    } else if (extension === 'docx') {
      return (
        <DocxDiffViewer
          oldValue={selectedLhsVersionDetail.file_content}
          newValue={selectedRhsVersionDetail.file_content}
        />
      );
    }
    return (
      selectedLhsVersionDetail &&
      selectedRhsVersionDetail && (
        <ReactDiffViewer
          oldValue={atob(selectedLhsVersionDetail.file_content)}
          newValue={atob(selectedRhsVersionDetail.file_content)}
          splitView={true}
          showDiffOnly={false}
          compareMethod={DiffMethod.WORDS}
          useDarkTheme={theme === 'dark'}
          // leftTitle={selectedLhsVersion}
          // rightTitle={selectedRhsVersion}
        />
      )
    );
  }, [event.target_path, selectedLhsVersionDetail, selectedRhsVersionDetail, theme]);

  return (
    <Drawer title={event.target_path.split('/').pop()} width="85%" open={true} onClose={onClose}>
      {loading ? (
        <Loading />
      ) : (
        <div className="flex flex-col h-full">
          <Form layout="vertical">
            <Row gutter={16} className="pr-2">
              <Col
                span={12}
                className="border-solid border-r border-border border-l-0 border-t-0 border-b-0">
                <Form.Item label="Select File Version">
                  <Select
                    className="w-full"
                    options={versionsOptions}
                    placeholder="Select version"
                    value={selectedLhsVersion}
                    onChange={(value) => setSelectedLhsVersion(value)}
                  />
                </Form.Item>
                <Row gutter={16} className="relative -top-4">
                  <Col span={24} className="flex flex-col justify-around border-border">
                    <div className="bg-seperator rounded px-4 py-2 h-full flex flex-col justify-around">
                      <Row className="mb-2">
                        <Col span={5}>
                          <LockOutlined className="mr-2" />
                          MD5
                        </Col>
                        <Col span={19} className="font-semibold">
                          {selectedLhsVersionDetail.md5_hash}
                        </Col>
                      </Row>
                      <Row className="mb-2">
                        <Col span={5}>
                          <LockOutlined className="mr-2" />
                          SHA256
                        </Col>
                        <Col span={19} className="font-semibold">
                          {selectedLhsVersionDetail.sha_256_hash}
                        </Col>
                      </Row>
                      <Row className="mb-2">
                        <Col span={5}>
                          <Icon name="last_seen" className="mr-2" />
                          Changed at
                        </Col>
                        <Col span={19} className="font-semibold">
                          {formatDateTime(selectedLhsVersionDetail.created_time, false)}
                        </Col>
                      </Row>
                    </div>
                  </Col>
                </Row>
              </Col>
              <Col span={12}>
                <Form.Item label="Select File Version">
                  <Select
                    className="w-full"
                    options={versionsOptions}
                    placeholder="Select version"
                    value={selectedRhsVersion}
                    onChange={(value) => setSelectedRhsVersion(value)}
                  />
                </Form.Item>
                <Row gutter={16} className="relative -top-4">
                  <Col span={24} className="flex flex-col justify-around border-border">
                    <div className="bg-seperator rounded px-4 py-2 h-full flex flex-col justify-around">
                      <Row className="mb-2">
                        <Col span={5}>
                          <LockOutlined className="mr-2" />
                          MD5
                        </Col>
                        <Col span={19} className="font-semibold">
                          {selectedRhsVersionDetail.md5_hash}
                        </Col>
                      </Row>
                      <Row className="mb-2">
                        <Col span={5}>
                          <LockOutlined className="mr-2" />
                          SHA256
                        </Col>
                        <Col span={19} className="font-semibold">
                          {selectedRhsVersionDetail.sha_256_hash}
                        </Col>
                      </Row>
                      <Row className="mb-2">
                        <Col span={5}>
                          <Icon name="last_seen" className="mr-2" />
                          Changed at
                        </Col>
                        <Col span={19} className="font-semibold">
                          {formatDateTime(selectedRhsVersionDetail.created_time, false)}
                        </Col>
                      </Row>
                    </div>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Form>
          <div className="flex flex-1 flex-col min-h-0 overflow-auto shadow-xs">{diffNode}</div>
        </div>
      )}
    </Drawer>
  );
}
