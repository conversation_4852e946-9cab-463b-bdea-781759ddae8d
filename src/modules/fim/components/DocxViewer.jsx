import { useEffect, useRef, useState } from 'react';
import * as mammoth from 'mammoth';

const maxWidth = 600;

export default function DocxViewer({ file }) {
  const containerRef = useRef(null);
  const [containerWidth, setWidth] = useState(null);
  const [__html, setHtml] = useState('<h4>Loading file...</h4>');

  function base64ToBuffer(base64) {
    // Convert binary string to Uint8Array
    const raw = atob(base64);
    const uint8Array = new Uint8Array(raw.length);
    for (let i = 0; i < raw.length; i++) {
      uint8Array[i] = raw.charCodeAt(i);
    }
    return uint8Array;
  }

  useEffect(() => {
    if (containerRef.current) {
      let resizeObserver = new ResizeObserver(() => {
        setWidth(containerRef.current.offsetWidth);
      });

      resizeObserver.observe(containerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, [containerRef]);

  useEffect(() => {
    (async () => {
      let buffer = base64ToBuffer(file);
      const { value } = await mammoth.convertToHtml({ arrayBuffer: buffer });
      setHtml(value);
    })();
  }, [file]);

  return (
    <div className="w-full" ref={containerRef}>
      <div
        style={{ width: `${containerWidth || maxWidth}px` }}
        className="overflow-x-auto excel-table-container"
        dangerouslySetInnerHTML={{ __html }}
      />
    </div>
  );
}
