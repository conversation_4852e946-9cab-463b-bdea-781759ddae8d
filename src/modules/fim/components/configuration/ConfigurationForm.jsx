import { useState } from 'react';
import { Form, Input, Row, Col, Button, Switch, Divider } from 'antd';
import CloneDeep from 'lodash/cloneDeep';
import { Department } from '@/src/components/pickers/DepartmentPicker.jsx';
import Icon from '@/src/components/Icon';
import { Link } from 'react-router-dom';
import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';
import PathInput from './PathInput';
import { Asset } from '@/src/components/pickers/AssetPicker';

export default function ConfigurationForm({ defaultValue, onSubmit, processing, disabled }) {
  const [formKey, setFormKey] = useState(1);

  function resetForm() {
    setFormKey(formKey + 1);
  }

  return (
    <Asset.Provider>
      <Department.Provider>
        <div className="flex flex-col">
          <div className="flex items-center">
            <Link to=".." className="mr-2 text-lg">
              <Icon name="arrow-left" />
            </Link>
            <h1 className="text-primary my-4 text-lg">
              {defaultValue.id ? (disabled ? 'View' : 'Edit') : 'Create'} FIM Configuration
            </h1>
          </div>
          <div className="border-solid border-border rounded px-2 py-2">
            <Row>
              <Col span={16}></Col>
              <Form
                layout="vertical"
                disabled={disabled}
                key={formKey}
                onFinish={onSubmit}
                initialValues={CloneDeep(defaultValue)}>
                <Row gutter={32}>
                  <Col span={6}>
                    <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                      <Input placeholder="Name" />
                    </Form.Item>
                  </Col>
                  <Col span={2}>
                    <Form.Item label="Status" name="status" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={16}>
                    <Form.Item label=" " noStyle name="scope">
                      <AssetScopePicker
                        label="Scope"
                        gutter={16}
                        skipProvider={true}
                        name={['scope', 'assetFilter']}
                        subname={['scope', 'assets']}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={32}>
                  <Col span={24}>
                    <Form.Item label="Description" name="description" rules={[{ required: true }]}>
                      <Input.TextArea placeholder="Description" rows={5} />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={24}>
                    <Form.Item
                      label="Include Paths"
                      name="includePaths"
                      rules={[{ required: true }]}>
                      <PathInput
                        disabled={disabled}
                        name="includePaths"
                        rootBtnText="Add Include Path"
                      />
                    </Form.Item>
                  </Col>
                  <Divider />
                  <Col span={24}>
                    <Form.Item
                      label="Exclude Paths"
                      name="excludePaths"
                      rules={[{ required: true }]}>
                      <PathInput
                        disabled={disabled}
                        name="excludePaths"
                        rootBtnText="Add Exclude Path"
                      />
                    </Form.Item>
                  </Col>
                  {!disabled && (
                    <Col span={24} className="text-right">
                      <Button
                        type="primary"
                        loading={processing}
                        htmlType="submit"
                        className="mr-2">
                        {defaultValue.id ? 'Update' : 'Create'}
                      </Button>
                      <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
                        Reset
                      </Button>
                    </Col>
                  )}
                </Row>
              </Form>
            </Row>
          </div>
        </div>
      </Department.Provider>
    </Asset.Provider>
  );
}
