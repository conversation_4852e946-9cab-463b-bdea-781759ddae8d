import { Row, Col, Form, Input, Button, Divider } from 'antd';
import Icon from '@/src/components/Icon';
import Repeater from '@/src/components/Repeater';

export default function PathInput({ disabled, name, rootBtnText }) {
  return (
    <Repeater name={name} disabled={disabled} defaultItem={{ paths: [] }} addBtnText={rootBtnText}>
      {({ key, name, ...restField }, actions) => (
        <div className="flex flex-col" key={key}>
          <div className="flex items-center">
            <div
              className={`flex-1 mr-2 px-2 py-1 rounded border-solid border-border
        ${disabled ? '' : 'bg-border'}`}>
              <Row gutter={32}>
                <Col span={6}>
                  <Form.Item
                    {...restField}
                    name={[name, 'category']}
                    label="Category"
                    rules={[{ required: true }]}>
                    <Input placeholder="Category" />
                  </Form.Item>
                </Col>
                <Col span={18}>
                  <Form.Item
                    {...restField}
                    name={[name, 'paths']}
                    label="Paths"
                    rules={[{ required: true }]}>
                    <Input.TextArea placeholder="Paths (seperated by newline)" rows={5} />
                  </Form.Item>
                </Col>
              </Row>
            </div>
            <div className="flex-shrink-0 flex items-center">
              {!disabled && (
                <Button
                  shape="circle"
                  type="danger"
                  style={{ visibility: name === 0 ? 'hidden' : 'visible' }}
                  onClick={() => actions.remove(name)}>
                  <Icon name="close" className="text-danger" style={{ fontSize: '1.5rem' }} />
                </Button>
              )}
            </div>
          </div>
          <Divider />
        </div>
      )}
    </Repeater>
  );
}
