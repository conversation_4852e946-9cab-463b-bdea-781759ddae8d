import { useEffect, useMemo, useRef, useState } from 'react';
import { pdfjs, Document, Page } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

pdfjs.GlobalWorkerOptions.workerSrc = '/pdf-viewer/pdf.worker.min.mjs';

const options = {
  cMapUrl: '/pdf-viewer/cmaps/',
  standardFontDataUrl: '/pdf-viewer/standard_fonts/'
};

const maxWidth = 600;

export default function PDFViewer({ file }) {
  const [numPages, setNumPages] = useState();
  const containerRef = useRef(null);
  const [containerWidth, setWidth] = useState(null);

  function onDocumentLoadSuccess({ numPages: nextNumPages }) {
    setNumPages(nextNumPages);
  }

  function base64ToBuffer(base64) {
    // Convert binary string to Uint8Array
    const raw = atob(base64);
    const uint8Array = new Uint8Array(raw.length);
    for (let i = 0; i < raw.length; i++) {
      uint8Array[i] = raw.charCodeAt(i);
    }
    return uint8Array;
  }

  useEffect(() => {
    if (containerRef.current) {
      let resizeObserver = new ResizeObserver(() => {
        setWidth(containerRef.current.offsetWidth);
      });

      resizeObserver.observe(containerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, [containerRef]);

  const binaryFile = useMemo(() => {
    return { data: base64ToBuffer(file) };
  }, [file]);

  return (
    <div className="w-full" ref={containerRef}>
      <Document file={binaryFile} onLoadSuccess={onDocumentLoadSuccess} options={options}>
        {Array.from(new Array(numPages), (_el, index) => (
          <Page
            key={`page_${index + 1}`}
            pageNumber={index + 1}
            width={containerWidth ? Math.min(containerWidth, maxWidth) : maxWidth}
          />
        ))}
      </Document>
    </div>
  );
}
