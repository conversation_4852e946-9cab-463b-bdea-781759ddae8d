import { useEffect, useMemo, useRef, useState } from 'react';
import * as XLSX from 'xlsx';

const maxWidth = 600;

export default function XLSXViewer({ file }) {
  const containerRef = useRef(null);
  const [containerWidth, setWidth] = useState(null);

  function base64ToBuffer(base64) {
    // Convert binary string to Uint8Array
    const raw = atob(base64);
    const uint8Array = new Uint8Array(raw.length);
    for (let i = 0; i < raw.length; i++) {
      uint8Array[i] = raw.charCodeAt(i);
    }
    return uint8Array;
  }

  useEffect(() => {
    if (containerRef.current) {
      let resizeObserver = new ResizeObserver(() => {
        setWidth(containerRef.current.offsetWidth);
      });

      resizeObserver.observe(containerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, [containerRef]);

  const excelHtml = useMemo(() => {
    let buffer = base64ToBuffer(file);
    const workbook = XLSX.read(buffer, { type: 'array' });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const htmlString = XLSX.utils.sheet_to_html(sheet);
    return htmlString;
  }, [file]);

  return (
    <div className="w-full" ref={containerRef}>
      <div
        style={{ width: `${containerWidth || maxWidth}px` }}
        className="overflow-x-auto excel-table-container"
        dangerouslySetInnerHTML={{ __html: excelHtml }}
      />
    </div>
  );
}
