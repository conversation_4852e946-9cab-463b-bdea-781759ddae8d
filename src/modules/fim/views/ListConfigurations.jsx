import { Switch, But<PERSON>, Row, Col, Form, Input, Divider, Select } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { useNavigate } from 'react-router-dom';
// import { FundViewOutlined } from '@ant-design/icons';
import {
  getAllConfigurationApi,
  updateConfigurationApi,
  deleteConfigurationApi,
  createConfigurationApi
} from '../api/configuration-api';
import { User } from '@/src/components/pickers/UserPicker';
import constants from '@/src/constants/index';
import PermissionChecker from '@/src/components/PermissionChecker';
import PageHeading from '@/src/components/PageHeading';
import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';
import Repeater from '@/src/components/Repeater';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import { Asset } from '@/src/components/pickers/AssetPicker';

export default function Configurations() {
  const navigate = useNavigate();
  const configTypeOptions = [
    { label: 'Files', value: 'fim' },
    { label: 'Configuration File', value: 'configuration_file' }
    // { label: 'Registry', value: 'registry' }
  ];
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render({ record, update }) {
        return (
          <Switch
            checked={record.status}
            onChange={(e) => {
              update({ ...record, status: e });
            }}
          />
        );
      },
      sortable: false
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      render({ record }) {
        return <User.Picker textOnly value={record.createdBy} disabled />;
      }
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_FIM],
      deletePermissions: [constants.Delete_FIM]
      // prependAction({ record }) {
      //   return (
      //     <Button
      //       title="View Events"
      //       type="link"
      //       onClick={() => navigate(`/fim/events?zSearch=${record.name}`)}>
      //       <FundViewOutlined style={{ fontSize: '1.1rem' }} />
      //     </Button>
      //   );
      // }
    }
  ];

  const navigateToView = () => navigate(`/fim/events`);

  return (
    <User.Provider>
      <Asset.Provider>
        <Department.Provider>
          <PageHeading icon="fim" title="FIM Configuration" />
          <CrudProvider
            columns={columns}
            resourceTitle="FIM Configuration"
            hasSearch
            defaultFormItem={{
              status: true,
              context: [
                {
                  config_type: 'fim',
                  category: undefined,
                  include_paths: undefined,
                  exclude_paths: undefined
                }
              ]
            }}
            createSlot={(create) => (
              <>
                <PermissionChecker permission={constants.Create_FIM}>
                  <Button type="primary" onClick={create}>
                    Create
                  </Button>
                </PermissionChecker>
                <Button className="ml-2" type="primary" ghost onClick={() => navigateToView()}>
                  View Events
                </Button>
              </>
            )}
            updateFn={updateConfigurationApi}
            deleteFn={deleteConfigurationApi}
            createFn={createConfigurationApi}
            fetchFn={(...args) => getAllConfigurationApi(...args)}
            formFields={(item, options, { disabled }) => (
              <>
                <Row>
                  <Col span={6}>
                    <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                      <Input placeholder="Name" />
                    </Form.Item>
                  </Col>
                  <Col span={6} className="px-4">
                    <Form.Item label="Status" name="status" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label=" " noStyle name="scope">
                      <AssetScopePicker
                        label="Scope"
                        gutter={16}
                        skipProvider={true}
                        name={['scope', 'assetFilter']}
                        subname={['scope', 'assets']}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={24}>
                    <Form.Item label="Description" name="description" rules={[{ required: true }]}>
                      <Input.TextArea placeholder="Description" rows={5} />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={24}>
                    <Form.Item label="" name="context" rules={[{ required: true }]}>
                      <Repeater canAdd={false} name="context" addBtnText="Add Context" maxItems={1}>
                        {({ key, name, ...restField }, actions) => (
                          <div className="flex flex-col" key={key}>
                            <div className="flex items-center">
                              <div
                                className={`flex-1 mr-2 px-2 py-1 rounded border-solid border-border
                        ${disabled ? '' : 'bg-border'}`}>
                                <Row gutter={32}>
                                  <Col span={12}>
                                    <Form.Item
                                      {...restField}
                                      name={[name, 'category']}
                                      label="Category"
                                      rules={[{ required: true }]}>
                                      <Input placeholder="Category" />
                                    </Form.Item>
                                  </Col>
                                  <Col span={12}>
                                    <Form.Item
                                      {...restField}
                                      name={[name, 'config_type']}
                                      label="Configuration Type"
                                      rules={[{ required: true }]}>
                                      <Select
                                        placeholder="Configuration Type"
                                        options={configTypeOptions}
                                      />
                                    </Form.Item>
                                  </Col>
                                  <Col
                                    span={
                                      item.context[0].config_type === 'configuration_file' ? 24 : 12
                                    }>
                                    <Form.Item
                                      {...restField}
                                      name={[name, 'include_paths']}
                                      label="Include Paths"
                                      rules={[{ required: true }]}>
                                      {item.context[0].config_type === 'configuration_file' ? (
                                        <Input placeholder="Path to file" />
                                      ) : (
                                        <Input.TextArea rows={5} placeholder="Include Paths" />
                                      )}
                                    </Form.Item>
                                  </Col>
                                  {item.context[0].config_type !== 'configuration_file' ? (
                                    <Col span={12}>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'exclude_paths']}
                                        label="Exclude Paths">
                                        <Input.TextArea rows={5} placeholder="Exclude Paths" />
                                      </Form.Item>
                                    </Col>
                                  ) : null}
                                </Row>
                              </div>
                              {/* <div className="flex-shrink-0 flex items-center">
                                {!disabled && (
                                  <Button
                                    shape="circle"
                                    type="danger"
                                    style={{ visibility: name === 0 ? 'hidden' : 'visible' }}
                                    onClick={() => actions.remove(name)}>
                                    <Icon
                                      name="close"
                                      className="text-danger"
                                      style={{ fontSize: '1.5rem' }}
                                    />
                                  </Button>
                                )}
                              </div> */}
                            </div>
                            <Divider />
                          </div>
                        )}
                      </Repeater>
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}
          />
        </Department.Provider>
      </Asset.Provider>
    </User.Provider>
  );
}
