import { <PERSON>, <PERSON>ge, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Col } from 'antd';
import { FileDoneOutlined } from '@ant-design/icons';
import OmitBy from 'lodash/omitBy';
import Mem from 'mem';
import StartCase from 'lodash/startCase';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import SplitPane from '@/src/components/SplitPane';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import Icon from '@/src/components/Icon';
import { Link } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import { CrudProvider } from '@/src/hooks/crud';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';
import {
  getAllEventsApi,
  getCategoriesApi,
  getFimEventChartApi,
  getFimEventChartWithCategoryApi
} from '../api/events-api';
import Chart from '@/src/components/widget/views/chart/Chart';
import { VirusTotalLink } from '@/src/components/VirusTotalLink';
import { useAuth } from '@/src/hooks/auth';
import ThreatContext from '@/src/components/ThreatContext';
import ThreatDetailDrawer from '../../alerts/components/ThreatDetailDrawer';
import FileCompareDrawer from '../components/FileCompareDrawer';

export default function FIMEvents() {
  const [showFileComparisionFor, setShowFileComparisionFor] = useState(null);
  const [type, setType] = useState('Windows');
  const [categories, setCategories] = useState({});
  const [showThreatContextFor, setThreatContextFor] = useState(null);
  const [chartData, setChartData] = useState(null);
  const [categoryWiseChartData, setCategoryWiseChartData] = useState(null);
  const [timeline, setTimeline] = useState({
    selected: 'Today'
  });
  const navigate = useNavigate();
  const { formatDateTime } = useAuth();

  useEffect(() => {
    getFimEventChartApi().then((data) => {
      setChartData(data);
    });
  }, []);

  useEffect(() => {
    getFimEventChartWithCategoryApi(type, timeline).then((data) => {
      setCategoryWiseChartData(data);
    });
  }, [timeline, type]);

  useEffect(() => {
    getCategoriesApi(timeline).then((categories) => {
      setCategories(categories);
      if (!type || Object.keys(categories).includes(type) === false) {
        setType(Object.keys(categories)[0]);
      }
    });
    // eslint-disable-next-line
  }, [timeline]);

  function handleTypeSelected(data) {
    setType(data[0]);
  }

  const defaultColumns = [
    {
      title: 'Asset',
      key: 'asset',
      dataIndex: 'asset',
      render({ record }) {
        return <Link to={`/inventory/endpoints/${record.asset_id}`}>{record.host_name}</Link>;
      }
    },
    // {
    //   title: 'Event ID',
    //   dataIndex: 'event_id',
    //   key: 'event_id',
    //   hidden: true,
    //   sortable: false
    // },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category'
    },
    {
      title: 'Target Path',
      dataIndex: 'target_path',
      key: 'target_path',
      ellipsis: true,
      sortable: false,
      render({ record }) {
        return record.target_path || record.path;
      }
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action'
    },
    {
      title: 'Event Time',
      dataIndex: 'event_time',
      key: 'event_time',

      render({ record }) {
        return record.event_time ? formatDateTime(record.event_time * 1000) : '';
      }
    }
  ];

  const excludedKeys = [
    'asset_id',
    'asset_name',
    'id',
    'threat_context',
    'config_id',
    'config_type',
    'content_change',
    'size_change',
    'metadata_change'
  ];

  const virusTotalLinks = ['md5', 'sha256'];

  const dateTimeKeys = ['event_time'];

  const capitalKeys = ['md5', 'sha256', 'sha1'];

  const capitalValueKeys = ['is_malicious', 'hashed'];

  const boxKeys = ['action', 'type', 'target_path', 'is_malicious'];

  const getTable = Mem(
    (item) => {
      let obj = item;
      obj = OmitBy(obj, (i, key) => i === null || i === '');
      const rows = Object.keys(obj)
        .filter((a) => excludedKeys.includes(a) === false)
        .map((key) => (
          <Row key={key}>
            <Col span={24}>
              <div className="ml-12 flex flex-1 items-center mb-1">
                <span className="text-primary flex-shrink-0" style={{ width: '150px' }}>
                  {capitalKeys.includes(key) ? key.toUpperCase() : StartCase(key)}
                </span>
                <span
                  className={`flex-1 flex-wrap flex break-words ${
                    boxKeys.includes(key) ? 'py-1 px-2 border-solid border-border rounded' : ''
                  }`}
                  style={
                    boxKeys.includes(key)
                      ? {
                          background:
                            key === 'is_malicious' && obj[key] === 'yes'
                              ? `var(--severity-critical-lightest)`
                              : `var(--severity-maintenance-lightest)`
                        }
                      : {}
                  }>
                  {virusTotalLinks.includes(key) ? (
                    <VirusTotalLink value={obj[key]} />
                  ) : dateTimeKeys.includes(key) ? (
                    formatDateTime(obj[key] * 1000)
                  ) : capitalValueKeys.includes(key) ? (
                    obj[key].toUpperCase()
                  ) : (
                    obj[key]
                  )}
                </span>
              </div>
            </Col>
          </Row>
        ));
      return rows;
    },
    {
      cacheKey: (arguments_) => `${arguments_[0].id}`
    }
  );

  function navigateToCreate() {
    navigate(`/fim/configuration?zOperation=create`);
  }

  return (
    <SplitPane
      hasMenu={true}
      leftPane={
        <Tree
          className="inventory-tree my-2 inventory-tree-no-switcher"
          blockNode
          defaultSelectedKeys={['Windows']}
          autoExpandParent
          titleRender={(item) => {
            return (
              <div
                className="flex items-center text-label flex-1 justify-between"
                style={{ whiteSpace: 'nowrap' }}>
                <div>
                  {item.type === 'os' && (
                    <Icon name={`platform_${item.key.toLowerCase()}`} className="mr-2 text-lg" />
                  )}
                  {item.title}{' '}
                </div>
                <Badge className="ml-2 count-badge" showZero count={item.count} />
              </div>
            );
          }}
          onSelect={handleTypeSelected}
          treeData={Object.keys(categories).map((item) => ({
            key: item,
            type: 'os',
            title: item,
            count: categories[item]
          }))}
        />
      }
      rightPane={
        <div className="h-full flex flex-col">
          <PageHeading icon="fim" title="FIM Events" />
          <div className="mt-2" style={{ height: '25%' }}>
            <Row gutter={4} className="h-full">
              <Col span={12} className="h-full">
                <div className="px-4 flex flex-col bg-seperator py-2 rounded h-full">
                  <h3 className="text-center">File Integrity Monitoring Trends</h3>
                  <div className="flex-1 min-h-0 flex flex-col">
                    {chartData && (
                      <Chart
                        widget={{
                          type: 'BarChart',
                          'x-axis': 'Event Date, Platform',
                          'y-axis': 'count',
                          widgetProperties: {
                            legendEnabled: true
                          }
                        }}
                        data={{
                          'x-axis': 'Event Date',
                          'y-axis': 'count'
                        }}
                        result={chartData}
                      />
                    )}
                  </div>
                </div>
              </Col>
              <Col span={12} className="h-full">
                <div className="px-4 flex flex-col bg-seperator py-2 rounded h-full">
                  <h3 className="text-center">Category wise Event Count</h3>
                  <div className="flex-1 min-h-0 flex flex-col">
                    {categoryWiseChartData && (
                      <Chart
                        widget={{
                          type: 'BarChart',
                          'x-axis': 'Event Date, Platform',
                          'y-axis': 'count',
                          widgetProperties: {
                            legendEnabled: true
                          }
                        }}
                        data={{
                          'x-axis': 'Event Date',
                          'y-axis': 'count'
                        }}
                        result={categoryWiseChartData}
                      />
                    )}
                  </div>
                </div>
              </Col>
            </Row>
          </div>

          <Divider className="my-1" />
          <div className="flex flex-1 flex-col min-h-0">
            <CrudProvider
              key={`${type}-${(timeline || {}).selected || ''}`}
              hasSearch={true}
              defaultSort={{
                order: 'descend',
                field: 'event_time',
                columnKey: 'event_time'
              }}
              beforeCreateSlot={() => (
                <div className="mr-2">
                  <TimelinePicker value={timeline} onChange={setTimeline} />
                </div>
              )}
              createSlot={() => (
                <Button className="mr-2" type="primary" ghost onClick={() => navigateToCreate()}>
                  Configure FIM
                </Button>
              )}
              expandable={{
                expandedRowRender: (record) => (
                  <Row>
                    <Col span={3}></Col>
                    <Col span={21}>{getTable(record)}</Col>
                  </Row>
                ),
                rowExpandable: (record) => true,
                expandIcon: ({ expanded, onExpand, record }) => (
                  <span className="text-label">
                    {expanded ? (
                      <DownOutlined className="text-label" onClick={(e) => onExpand(record, e)} />
                    ) : (
                      <RightOutlined className="text-label" onClick={(e) => onExpand(record, e)} />
                    )}
                  </span>
                )
              }}
              fetchFn={(...args) => getAllEventsApi(...args, { type, timeline })}
              columns={defaultColumns}
              prependColumns={[
                {
                  title: ' ',
                  resizable: false,
                  width: 20,
                  key: 'threat_context',
                  dataIndex: 'threat_context',
                  order: -1,
                  sortable: false,
                  selectable: false,
                  render({ record }) {
                    return <ThreatContext record={record} onClick={setThreatContextFor} />;
                  }
                },
                {
                  title: ' ',
                  resizable: false,
                  width: 20,
                  key: 'configuration_file',
                  dataIndex: 'configuration_file',
                  order: -1,
                  sortable: false,
                  selectable: false,
                  render({ record }) {
                    return record.config_type === 'configuration_file' ? (
                      <Button
                        type="link"
                        title="View File Changes"
                        onClick={() => setShowFileComparisionFor(record)}>
                        <FileDoneOutlined className="mr-2 text-base" />
                      </Button>
                    ) : null;
                  }
                }
              ]}
            />
            {showThreatContextFor ? (
              <ThreatDetailDrawer
                alert={showThreatContextFor}
                onClose={() => setThreatContextFor(null)}
              />
            ) : null}
          </div>
          {showFileComparisionFor ? (
            <FileCompareDrawer
              onClose={() => setShowFileComparisionFor(null)}
              event={showFileComparisionFor}
            />
          ) : null}
        </div>
      }
    />
  );
}
