import { Row, Col } from 'antd';
import { Outlet } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import AnimatedRoutes from '@components/AnimatedRoutes';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import Icon from '@/src/components/Icon';
import LicensedComponent from '@/src/components/LicensedComponent';

export default function FIMLayout() {
  return (
    <LicensedComponent useNotFound={true} excludedProducts={[LicensedComponent.ZiroPatch]}>
      <div className="h-full flex flex-col">
        <PageHeading
          icon={() => (
            <div className="flex bg-table-header px-2 py-2 rounded-md items-center justify-center">
              <Icon name="fim" className="text-primary-500" style={{ fontSize: '1.5rem' }} />
            </div>
          )}
          title="FIM"
        />
        <div className="flex-1 min-h-0 flex flex-col">
          <Row className="h-full">
            <PermissionChecker permission={constants.View_FIM}>
              <AnimatedRoutes element={Col} span={24} className="h-full flex flex-col">
                <Outlet />
              </AnimatedRoutes>
            </PermissionChecker>
          </Row>
        </div>
      </div>
    </LicensedComponent>
  );
}
