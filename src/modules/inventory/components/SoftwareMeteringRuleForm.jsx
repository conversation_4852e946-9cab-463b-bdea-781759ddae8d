/* eslint-disable */
import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';
import { Software } from '@/src/components/pickers/SoftwarePicker';
import { Row, Col, Form, Input, Switch, Select } from 'antd';
import { useEffect, useState } from 'react';

export default function SoftwareMeteringRuleForm() {
  const form = Form.useFormInstance();

  let scope = form.getFieldValue('scope');

  return (
    <>
      <Row gutter={32}>
        <Col span={12}>
          <Form.Item label="Name" name="rule_name" rules={[{ required: true }]}>
            <Input placeholder="Name" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Status" name="status" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Col>
        <Col span={24}>
          <AssetScopePicker
            label="Scope"
            gutter={16}
            name={['scope', 'assetFilter']}
            subname={['scope', 'assets']}
          />
        </Col>
        <Col span={24}>
          <Software.Provider scope={scope}>
            <Form.Item
              label="Software"
              dependencies={['scope']}
              name="software_id"
              rules={[{ required: true }]}>
              <Software.Picker placeholder="Select Software" />
            </Form.Item>
          </Software.Provider>
        </Col>
        <Col span={12}>
          <Form.Item label="File Name" name="file_name" rules={[{ required: true }]}>
            <Input placeholder="File Name" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Original File Name" name="original_file_name">
            <Input placeholder="Original File Name" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Product Name" name="product_name">
            <Input placeholder="Product Name" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="File Version" name="file_version">
            <Input placeholder="File Version" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Is Licensed" name="is_licensed" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Comments" name="comments">
            <Input placeholder="Comments" />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
}
