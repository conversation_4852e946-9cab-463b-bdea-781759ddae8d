import { Tag, Button } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createSoftwareMeteringRuleApi,
  getSoftwareMeteringSummaryApi
} from '../api/software-metering';
import { useState } from 'react';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';
import SoftwareMeteringDiscoveredInfo from './SoftwareMeteringDiscoveredInfo';
import SoftwareMeteringUsageDuration from './SoftwareMeteringUsageDuration';
import SoftwareMeteringChart from './SoftwareMeteringChart';
import SoftwareMeteringRuleForm from './SoftwareMeteringRuleForm';

export default function SoftwareMeteringSummary({ assetId }) {
  const [activeRule, setActiveRule] = useState(null);
  const [timeline, setTimeline] = useState({});

  const columns = [
    {
      title: 'Name',
      dataIndex: 'rule_name',
      key: 'rule_name'
    },
    {
      title: 'File Name',
      dataIndex: 'file_name',
      key: 'file_name'
    },
    {
      title: 'Used Count',
      dataIndex: 'usage_count',
      key: 'usage_count',
      align: 'center',
      ...(assetId
        ? {
            render({ record }) {
              return (
                <Tag onClick={() => setActiveRule(record)} className="cursor-pointer">
                  {record.usage_count}
                </Tag>
              );
            }
          }
        : {})
    },
    {
      title: 'Licensed',
      dataIndex: 'is_licensed',
      key: 'is_licensed',
      align: 'center',
      render({ record }) {
        return <Tag>{record.is_licensed ? 'Yes' : 'No'}</Tag>;
      }
    },
    ...(assetId
      ? []
      : [
          {
            title: 'Total Discovered',
            dataIndex: 'discovered_count',
            key: 'discovered_count',
            align: 'center',
            render({ record }) {
              return (
                <Tag onClick={() => setActiveRule(record)} className="cursor-pointer">
                  {record.discovered_count}
                </Tag>
              );
            }
          }
        ]),
    {
      title: 'Duration',
      dataIndex: 'usage_duration',
      key: 'usage_duration',
      align: 'center',
      render({ record }) {
        return <Tag>{record.usage_duration}</Tag>;
      }
    }
  ];

  return (
    <div className="flex flex-col h-full min-h-0">
      <SoftwareMeteringChart timeline={timeline} assetId={assetId} />
      <CrudProvider
        columns={columns}
        resourceTitle="Software Metering Summary"
        hasSearch
        key={`${JSON.stringify(timeline)}`}
        beforeCreateSlot={() => (
          <div className="mr-2">
            <TimelinePicker value={timeline} onChange={setTimeline} />
          </div>
        )}
        createSlot={(create) => (
          <Button className="mr-2" type="primary" ghost onClick={() => create()}>
            Configure SW Metering Rules
          </Button>
        )}
        defaultFormItem={{
          status: true
        }}
        formFields={(item) => <SoftwareMeteringRuleForm item={item} />}
        createFn={createSoftwareMeteringRuleApi}
        drawerTitle={() => `Create Software Metering Rule`}
        fetchFn={(...args) => getSoftwareMeteringSummaryApi(assetId, ...args, { timeline })}
      />
      {activeRule ? (
        assetId ? (
          <SoftwareMeteringUsageDuration
            timeline={timeline}
            rule={{ ...activeRule, asset_id: +assetId }}
            onClose={() => setActiveRule(null)}
          />
        ) : (
          <SoftwareMeteringDiscoveredInfo
            timeline={timeline}
            rule={activeRule}
            onClose={() => setActiveRule(null)}
          />
        )
      ) : null}
    </div>
  );
}
