import { useEffect, useState } from 'react';
import { Row, Col, Spin } from 'antd';
import { getAssetSummaryDetailApi } from '../../../api/hosts';
import Summary from './Summary';
import ScoreGauge from '@/src/components/ScoreGauge';

export default function HostExpandedRow({ asset }) {
  const [loading, setLoading] = useState(true);
  const [detail, setDetail] = useState({});

  useEffect(() => {
    getAssetSummaryDetailApi(asset.id).then((data) => {
      setDetail(data);
      setLoading(false);
    });
  }, [asset.id]);

  return loading ? (
    <Spin spinning={true} />
  ) : (
    <Row className="bg-page-background" gutter={8}>
      <Col span={12}>
        <div className="rounded p-2 bg-seperator h-full flex items-center justify-center">
          <Row gutter={32} className="w-full">
            <Col span={12}>
              <div className="h-full flex flex-col justify-center">
                <div className="my-2">
                  <span className="text-label flex-1 mr-2">Malicious Processes:</span>
                  <span className="font-bold text-right">{detail.softwares.malicious}</span>
                </div>
                <div className="my-2">
                  <span className="text-label flex-1 mr-2">Vulnerable Software:</span>
                  <span className="font-bold text-right">{detail.softwares.vulnerable}</span>
                </div>
                <div className="my-2">
                  <span className="text-label flex-1 mr-2">Risk Score:</span>
                  <span className="font-bold text-right">{detail.softwares.risk_score}</span>
                </div>
              </div>
            </Col>
            <Col span={12} className="flex flex-col items-center justify-center">
              <ScoreGauge
                style={{ width: 150 }}
                value={detail.softwares.risk_score}
                useReverseColor
              />
            </Col>
          </Row>
        </div>
      </Col>
      <Col span={12}>
        <div className="rounded p-2 bg-seperator h-full flex flex-col justify-center">
          <Summary detail={detail} />
        </div>
      </Col>
    </Row>
  );
}
