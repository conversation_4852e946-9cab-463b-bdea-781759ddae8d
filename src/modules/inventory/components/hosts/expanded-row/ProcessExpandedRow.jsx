import { Row, Col, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { getProcessDependencyApi } from '../../../api/hosts';
import CytoscapeGraph from '@/src/components/CytoscapeGraph';

export default function ProcessExpandedRow({ asset, process, isOpen }) {
  const [loading, setLoading] = useState(true);
  const [hierarchy, setHierarchy] = useState(null);
  const [cyKey, setCyKey] = useState(1);

  useEffect(() => {
    if (isOpen) {
      setCyKey((key) => key + 1);
    }
  }, [isOpen]);

  useEffect(() => {
    getProcessDependencyApi(asset.id, process.pid).then((data) => {
      setHierarchy(data);
      setLoading(false);
    });
  }, [asset.id, process.pid]);

  return loading ? (
    <Spin spinning={true} />
  ) : (
    <Row className="border-border rounded border-solid border-x border-y bg-lightest">
      <Col span={24} style={{ height: '350px' }}>
        <CytoscapeGraph
          hierarchy={hierarchy}
          key={cyKey}
          dataLabelFn={(data) => `<div class="text-center max-w-[95px]" title="${data.label}">
                <p class="text-xs mb-0 text-white text-ellipsis whitespace-pre">
                ${data.label}
                </p>
                <p  class="mb-0 text-white text-ellipsis">
                ${data.pid}</p></div>`}
        />
      </Col>
    </Row>
  );
}
