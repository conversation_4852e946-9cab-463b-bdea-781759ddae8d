import Icon from '@/src/components/Icon';
import { Row, Col, Progress } from 'antd';

export default function Summary({ detail }) {
  const diskPercent = Math.round(
    (parseFloat(detail.disk.used) * 100) / parseFloat(detail.disk.total)
  );
  return (
    <Row gutter={32}>
      <Col span={7}>
        <div className="flex items-center">
          <span className="text-primary">
            <Icon name="cpu" style={{ fontSize: '1.3rem' }} />
          </span>
          <h4 className="mb-0 ml-2">CPU</h4>
        </div>
        <div className="flex items-center text-xs mt-2">
          <span className="text-label flex-1">Physical Core</span>
          <span className="font-bold text-right">{detail.cpu.physical}</span>
        </div>
        <div className="flex items-center text-xs mt-2">
          <span className="text-label flex-1">Logical Core</span>
          <span className="font-bold text-right">{detail.cpu.logical}</span>
        </div>
      </Col>
      <Col span={7}>
        <div className="flex items-center">
          <span className="text-primary">
            <Icon name="memory" style={{ fontSize: '1.3rem' }} />
          </span>
          <h4 className="mb-0 ml-2">Memory</h4>
        </div>
        <div className="flex items-center text-xs mt-2">
          <span className="text-label flex-1">Total</span>
          <span className="font-bold text-right">{detail.ram.total}</span>
        </div>
      </Col>
      <Col span={10}>
        <div className="flex items-center">
          <span className="text-primary">
            <Icon name="disk" style={{ fontSize: '1.3rem' }} />
          </span>
          <h4 className="mb-0 ml-2">Disk</h4>
        </div>
        <div className="flex flex-col items-center text-xs mt-2">
          <Progress
            strokeColor={diskPercent > 80 ? '#EB5758' : diskPercent > 60 ? '#F2994B' : '#89C540'}
            percent={diskPercent}
            size="small"
          />
          <div className="flex items-center justify-between w-full">
            <div className="text-label" style={{ fontSize: '8px' }}>
              Total: {detail.disk.total}
            </div>
            <div className="text-label" style={{ fontSize: '8px' }}>
              Used: {detail.disk.used}
            </div>
          </div>
        </div>
      </Col>
    </Row>
  );
}
