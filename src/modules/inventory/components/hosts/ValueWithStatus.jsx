import Icon from '@/src/components/Icon';

export default function ValueWithStatus({ value, icon, label, colorEvaluator }) {
  let color = 'text-success';
  if (colorEvaluator) {
    color = colorEvaluator(value);
  } else {
    if (value > 70) {
      color = 'text-danger';
    } else if (value > 50) {
      color = 'text-warning';
    }
  }
  return (
    <div className={`flex flex-1 min-w-0 items-center justify-center ${color}`}>
      <div className="flex flex-col flex-1 items-center justify-center">
        <Icon name={icon} style={{ fontSize: '2rem' }} />
        <span className="text-label mt-1">{label}</span>
        <h1 className="text-4xl mt-2 text-center mb-0">{value}</h1>
      </div>
    </div>
  );
}
