import { <PERSON><PERSON>, <PERSON><PERSON>, Divider, Select } from 'antd';
import { useEffect, useState, useMemo } from 'react';
import QueryEditor from '@/src/components/pickers/QueryEditor';
import SocketEventHandler from '@/src/components/SocketEventHandler';
import InvestigateResult from '@/src/modules/investigate/components/InvestigateResult';
import { getAllInvestigateForAssetApi } from '../../api/hosts';

export default function InvestigateDrawer(props) {
  const [open, setOpen] = useState(true);
  const [query, setQuery] = useState(undefined);
  const [selectedQuery, setSelectedQuery] = useState(undefined);
  const [queryOptions, setQueryOptions] = useState([]);
  const [resultKey, setResultKey] = useState(0);
  const { asset } = props;

  const socketContext = useMemo(
    () => ({
      scope: 2,
      assets: [parseInt(asset.id)],
      query: query
    }),
    [asset.id, query]
  );

  function onClose() {
    setOpen(false);
    setTimeout(() => {
      props.onClose();
    }, 400);
  }

  useEffect(() => {
    getAllInvestigateForAssetApi(props.asset.id, 0, 1000).then((data) => setQueryOptions(data));
  }, [props.asset.id]);

  useEffect(() => {
    if (selectedQuery) {
      const o = queryOptions.find((q) => q.value === selectedQuery);
      if (o) {
        setQuery(o.query);
      }
    }
  }, [selectedQuery, queryOptions]);

  function handleRunQuery() {
    if (query) {
      setResultKey((resultKey) => resultKey + 1);
    }
  }

  function handleReset() {
    setSelectedQuery(undefined);
    setQuery(undefined);
  }

  function handleQuerySelected(value) {
    setSelectedQuery(value);
    setResultKey(0);
  }

  return (
    <Drawer
      height={520}
      title={`Investigating ${props.asset.hostname}`}
      width="60%"
      onClose={onClose}
      open={open}>
      <div className="flex flex-col min-h-0 h-full">
        <div className="flex items-center mb-2">
          <div className="mr-4 flex-1">
            <Select
              options={queryOptions}
              value={selectedQuery}
              className="w-full"
              placeholder="Select Investigate"
              onChange={handleQuerySelected}
            />
          </div>
          <div className="flex items-center">
            <Button htmlType="button" type="primary" onClick={handleRunQuery}>
              Run
            </Button>
            <Button className="ml-2" htmlType="button" type="primary" onClick={handleReset}>
              Reset
            </Button>
          </div>
        </div>
        <div className="flex-shrink-0 flex flex-col h-1/3">
          <div className="h-full w-full">
            <QueryEditor
              key={selectedQuery}
              className="full-height-editor"
              value={query}
              onChange={(value) => setQuery(value)}
            />
          </div>
        </div>
        <Divider>Output</Divider>
        <div className="flex flex-col min-h-0 h-2/3">
          {resultKey > 0 ? (
            <SocketEventHandler
              event="live_query"
              key={resultKey}
              mergePayload
              context={socketContext}>
              {(payload) => <InvestigateResult asset={asset} data={payload} />}
            </SocketEventHandler>
          ) : null}
        </div>
      </div>
    </Drawer>
  );
}
