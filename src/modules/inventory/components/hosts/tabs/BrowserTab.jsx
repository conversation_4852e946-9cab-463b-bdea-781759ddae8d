import { useEffect, useState } from 'react';
import {
  Tabs
  // Switch,
  //  Button
} from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  getBrowserCategoriesApi,
  getBrowsersForAssetApi,
  updateInventoryBrowserApi
} from '../../../api/hosts';
import VulnerabilityDrawer from '../../VulnerabilityDrawer';
// import VulnerabilityDot from '@/src/components/VulnerabilityDot';

export default function BrowserTab({ asset }) {
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedPackage, setVulnerabilityView] = useState(null);

  const columns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   key: 'id',
    //   width: 100
    // },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version'
    }
    // {
    //   title: 'Release Date',
    //   dataIndex: 'releaseDate',
    //   key: 'releaseDate',
    //   sortable: false
    // },
    // {
    //   title: 'EOS',
    //   dataIndex: 'eosDate',
    //   key: 'eosDate',
    //   sortable: false
    // },
    // {
    //   title: 'EOL',
    //   dataIndex: 'eolDate',
    //   key: 'eolDate',
    //   sortable: false
    // },
    // {
    //   title: 'Vulnerability',
    //   dataIndex: 'vulnerability',
    //   key: 'vulnerability',
    //   sortable: true,
    //   render({ record, update }) {
    //     return (
    //       <Button type="link" onClick={() => setVulnerabilityView(record)}>
    //         {record.vulnerability}
    //       </Button>
    //     );
    //   }
    // },
    // {
    //   title: 'Blacklisted',
    //   dataIndex: 'blacklist',
    //   key: 'blacklist',
    //   sortable: false,
    //   render({ record, update }) {
    //     return (
    //       <Switch
    //         checked={record.blacklist}
    //         onChange={(blacklist) => update({ ...record, blacklist })}
    //       />
    //     );
    //   }
    // }
  ];

  useEffect(() => {
    getBrowserCategoriesApi(asset.id).then((categories) => setCategories(categories));
  }, [asset]);

  return (
    <div className="flex flex-col min-h-0 overflow-auto">
      <div className="flex min-h-0">
        <div className="flex flex-col">
          <Tabs
            activeKey={selectedCategory}
            tabPosition="left"
            onChange={(key) => setSelectedCategory(key)}
            style={{ height: 220 }}
            items={Object.keys(categories).map((category) => ({
              label: `${category} (${categories[category]})`,
              key: category
            }))}
          />
        </div>
        <div className="flex flex-1 min-h-0 flex-col min-w-0">
          <CrudProvider
            columns={columns}
            defaultPageSize={20}
            beforeCreateSlot={() => (
              <span className="mr-2">{`Showing results for ${asset.lastSeen}`}</span>
            )}
            // prependColumns={[
            //   {
            //     title: '',
            //     dataIndex: 'dot_id',
            //     key: 'dot_id',
            //     sortable: false,
            //     width: 50,
            //     render({ record }) {
            //       return (
            //         <VulnerabilityDot
            //           eolDate={record.eolDate}
            //           vulnerability={record.vulnerability}
            //         />
            //       );
            //     }
            //   }
            // ]}
            resourceTitle="Browser"
            hasSearch
            key={selectedCategory}
            updateFn={updateInventoryBrowserApi}
            fetchFn={(...args) =>
              getBrowsersForAssetApi(
                asset.id,
                selectedCategory === 'All' ? null : selectedCategory,
                ...args
              )
            }
          />
          {selectedPackage ? (
            <VulnerabilityDrawer
              software={selectedPackage}
              closeDrawer={() => setVulnerabilityView(null)}
            />
          ) : null}
        </div>
      </div>
    </div>
  );
}
