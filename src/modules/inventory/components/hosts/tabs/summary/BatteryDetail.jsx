/* eslint-disable */
import Icon from '@/src/components/Icon';
import { ReactComponent as Battery } from '@/src/icons/general/battery.svg';
import { getBatteryForAssetApi } from '@/src/modules/inventory/api/hosts';
import duration from '@/src/utils/duration';
import { useEffect, useState } from 'react';

export default function BatteryDetail({ asset }) {
  const [battery, setBattery] = useState({});
  useEffect(() => {
    getBatteryForAssetApi(asset.id).then((data) => {
      setBattery(data[0] || {});
    });
  }, [asset.id]);

  let batteryClass = '';

  if (battery.percentage < 20) {
    batteryClass = 'text-danger';
  } else if (battery.percentage < 50) {
    batteryClass = 'text-warning';
  } else {
    batteryClass = 'text-success';
  }
  return battery.id ? (
    <div className="flex justify-center items-center flex-1 h-full flex-col">
      <h3 className="m-0 text-sm font-bold ">System Battery</h3>
      <div className="flex justify-center items-center flex-1 h-full">
        <div className="w-2/4 h-full mr-1 text-center">
          <Battery className={`w-full h-full ${batteryClass} max-w-[75%]`} />
        </div>
        <div className="h-full w-2/4 flex flex-col justify-center text-2xl ml-1">
          <span className="text-2xl">{parseInt(battery.percentage)}%</span>
          {/* <span className="text-neutral-light text-xs">
            {battery.time_to_empty
              ? `${duration(battery.time_to_empty, undefined, false, ['d', 'h', 'm'])} to Discharge`
              : battery.time_to_full
              ? `${duration(battery.time_to_full, undefined, false, [
                  'd',
                  'h',
                  'm'
                ])} to Full Charge`
              : ''}
          </span> */}
        </div>
      </div>
    </div>
  ) : (
    <div className="text-neutral-light flex items-center justify-center flex-1">
      No Battery found
    </div>
  );
}
