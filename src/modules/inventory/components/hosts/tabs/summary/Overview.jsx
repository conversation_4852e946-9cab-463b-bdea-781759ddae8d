import { Row, Col, Divider } from 'antd';
import Moment from 'moment';
import Icon from '@/src/components/Icon';
import { User } from '@components/pickers/UserPicker';
import { Department } from '@components/pickers/DepartmentPicker';
import { Location } from '@components/pickers/LocationPicker';
import SeverityDot from '@/src/components/SeverityDot';
import LiveStatus from '@/src/components/LiveStatus';
// import duration from '@/src/utils/duration';
import Chart from '@/src/components/widget/views/chart/Chart';
import SystemLogo from '@/src/components/SystemLogo';
import ScoreGauge from '@/src/components/ScoreGauge';
import { severityColors } from '@/src/design/theme';
// import QRCodeGenerator from '@components/common/QRCodeGenerator';
import BatteryDetail from './BatteryDetail';

export default function Overview({ asset, onChange, onCountClick }) {
  const pieWidget = {
    type: 'PieChart',
    widgetProperties: {
      pieInnerSize: 30,
      opacity: 0.7
    }
  };

  const vulnerabilityData = {
    'x-axis': 'category',
    'y-axis': 'count',
    result: ['Critical', 'High', 'Medium', 'Low'].map((key) => ({
      category: key,
      count: asset.vulnerability[key] || 0,
      color: severityColors[key.toLowerCase()]
    }))
  };

  return (
    <Row gutter={8} className="pb-2 h-full">
      <Col span={8} className="h-full flex-1">
        <div className="flex flex-1 flex-col p-2 bg-seperator rounded-lg h-full">
          <div className="px-2 mb-1 flex justify-between">
            <div className="w-16">
              <SystemLogo name={asset.vendor} className="w-full" type="vendor" />
            </div>
            <div className="inline-flex items-center justify-center">
              <LiveStatus fillBg status={asset.currentStatus} />
            </div>
          </div>
          <div className="px-2 text-sm font-bold">{asset.hardwareModel}</div>
          <div className="px-2 text-xs inline-flex items-center">UUID: {asset.systemInfo.uuid}</div>
          <Divider className="my-2" />
          <div className="flex flex-1 flex-col justify-evenly">
            <Row gutter={8} className="flex-1">
              <Col span={8}>
                <div className="flex justify-center flex-col h-full">
                  <div className="text-xs inline-flex items-center text-neutral-light">
                    <Icon name="department" className="mr-1" />
                    Department
                  </div>
                  <div className="font-bold text-base">
                    <Department.Picker
                      value={asset.department}
                      onChange={(id) => onChange({ ...asset, department: id })}
                    />
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className="flex justify-center flex-col h-full">
                  <div className="text-xs inline-flex items-center text-neutral-light">
                    <Icon name="user" className="mr-1" />
                    User
                  </div>
                  <div className="font-bold text-base">
                    <User.Picker
                      value={asset.owner}
                      onChange={(id) => {
                        onChange({ ...asset, owner: id });
                      }}
                    />
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className="flex justify-center flex-col h-full">
                  <div className="text-xs inline-flex items-center text-neutral-light">
                    <Icon name="location" className="mr-1" />
                    Location
                  </div>
                  <div className="font-bold text-base">
                    <Location.Picker
                      value={asset.location}
                      onChange={(id) => onChange({ ...asset, location: id })}
                    />
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className="h-full flex flex-col justify-end">
                  <div className="text-xs inline-flex items-center text-neutral-light">
                    <Icon name="agent_version" className="mr-1" />
                    Agent Version
                  </div>
                  <div className="font-bold text-base">{asset.agentVersion}</div>
                </div>
              </Col>
              <Col span={16}>
                <div className="h-full flex flex-col justify-end">
                  <div className="text-xs inline-flex items-center text-neutral-light">
                    <Icon name="last_seen" className="mr-1" />
                    Last Seen
                  </div>
                  <div className="font-bold text-base">{asset.lastSeen}</div>
                </div>
              </Col>
            </Row>
          </div>
        </div>
      </Col>
      <Col span={8} className="h-full flex-1">
        <div className="flex flex-col flex-1 h-full">
          <div className="p-2 bg-seperator rounded-lg h-1/2 flex flex-col">
            <div className="font-semibold text-xs mb-1">Vulnerabilities</div>
            <div
              className="flex justify-between items-center min-h-0"
              style={
                Object.keys(asset.vulnerability).length === 0
                  ? {
                      filter: `grayscale(1)`
                    }
                  : {}
              }>
              <div style={{ height: '100px', width: '100px' }}>
                <Chart
                  widget={pieWidget}
                  data={vulnerabilityData}
                  style={{ height: '100px', width: '100px' }}
                />
              </div>
              <div
                className="flex-1 flex flex-wrap justify-items-stretch items-center"
                style={{ height: '100px' }}>
                {['Critical', 'High', 'Medium', 'Low'].map((key) => (
                  <div key={key} className={`flex flex-col w-1/2 text-center`}>
                    <div className="flex items-center justify-center">
                      <div className={`mr-1`}>
                        <SeverityDot severity={key} />
                      </div>
                      <div className="text-xs">{key}</div>
                    </div>
                    <div
                      className={`font-semibold text-lg text ${key.toLowerCase()} cursor-pointer`}
                      onClick={() => {
                        if (asset.vulnerability[key]) {
                          onCountClick({
                            tab: 'vulnerabilities',
                            subTab: key
                          });
                        }
                      }}>
                      {asset.vulnerability[key] || 0}
                    </div>
                  </div>
                ))}
              </div>

              <div
                className="flex flex-col cursor-pointer text-center border-left px-4 h-full items-center justify-around"
                onClick={() =>
                  onCountClick({
                    tab: 'vulnerabilities',
                    subTab: 'exploit'
                  })
                }>
                Exploitable CVE
                <div className="text-5xl mb-2 text critical">
                  {asset.vulnerability.exploit_count}
                </div>
              </div>
            </div>
          </div>
          <div className="p-2 mt-2 bg-seperator rounded-lg h-1/2">
            <div className="flex flex-col justify-between flex-1 h-full">
              <div>
                <div className="flex justify-between items-center">
                  <div className=" text-sm text-neutral-light flex items-center">
                    <Icon name="eol" className="mr-1 text-lg" /> EOL Status
                  </div>
                  <div>
                    <LiveStatus
                      status={
                        asset.eolDate
                          ? Moment(asset.eolDate).diff(Moment()) >= 0
                            ? 'Active'
                            : 'Inactive'
                          : 'Active'
                      }
                      fillBg
                    />
                  </div>
                </div>
                <div className="flex mt-2 items-center">
                  <div className="w-6">
                    <SystemLogo
                      name={
                        asset.systemInfo.os.toLowerCase().indexOf('windows') > 0
                          ? 'windows'
                          : asset.systemInfo.os.toLowerCase()
                      }
                      className="w-full"
                      type="os"
                    />
                  </div>
                  <span className="font-semibold ml-2">
                    {asset.osName} {asset.systemInfo.osVersion}
                  </span>
                </div>
              </div>
              <Row gutter={8}>
                <Col span={8} className="h-full flex flex-col justify-end">
                  <div className="text-xs text-neutral-light">Release Date</div>
                  <div className="font-bold text-base">{asset.releaseDate || '-'}</div>
                </Col>
                <Col span={8} className="">
                  <div className="text-xs text-neutral-light">End Of Life </div>
                  <div className="font-bold text-base">{asset.eolDate || '-'}</div>
                </Col>
                <Col span={8} className="">
                  <div className="text-xs text-neutral-light">End Of Support </div>
                  <div className="font-bold text-base">{asset.eosDate || '-'}</div>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </Col>
      <Col span={5} className="h-full flex-1">
        <div className="flex flex-col flex-1 h-full">
          <div className="p-2 bg-seperator rounded-lg h-1/2 flex flex-col min-h-0">
            <div className="flex flex-col flex-1 items-center justify-center">
              <Row className="w-full flex-1">
                <Col span={12} className="h-full flex flex-col items-center justify-center">
                  <ScoreGauge value={asset.riskScore} useReverseColor />
                </Col>
                <Col span={12} className="flex flex-col justify-center">
                  <h2 className="m-0">EndPoint</h2>
                  <h1 className="m-0">Risk Score</h1>
                </Col>
              </Row>
            </div>
          </div>
          <div className="p-2 mt-2 bg-seperator rounded-lg h-1/2">
            <Row className="w-full h-full">
              <Col span={12} className="flex flex-col items-center justify-between">
                <h3 className="m-0 text-sm font-bold ">Patch Status</h3>

                <div className="flex flex-col w-full itmes-center justify-center">
                  <div className="flex mb-1 w-full itmes-center justify-center">Installed</div>
                  <div
                    className="flex cursor-pointer mb-6  w-full itmes-center justify-center"
                    onClick={() => onCountClick({ tab: 'patch', subTab: 'installed' })}>
                    {asset.patchCounts.installed}
                  </div>
                  <div className="flex mb-1  w-full itmes-center justify-center">Missings</div>
                  <div
                    className="flex text-lg text critical cursor-pointer mb-2  w-full itmes-center justify-center"
                    onClick={() => onCountClick({ tab: 'patch', subTab: 'missing' })}>
                    {asset.patchCounts.missing}
                  </div>
                </div>
              </Col>
              <Col span={12} className="flex flex-col items-center justify-between">
                <h3 className="m-0 text-sm font-bold ">Compliance Status</h3>

                <div className="flex flex-col w-full itmes-center justify-center">
                  <div className="flex mb-1 w-full itmes-center justify-center "> Pass </div>
                  <div
                    className="flex cursor-pointer mb-6  w-full itmes-center justify-center"
                    onClick={() => onCountClick({ tab: 'compliance' })}>
                    {asset.compliance.Pass}
                  </div>
                  <div className="flex mb-1  w-full itmes-center justify-center"> Fail </div>
                  <div
                    className="flex text-lg text critical cursor-pointer mb-2  w-full itmes-center justify-center"
                    onClick={() => onCountClick({ tab: 'compliance' })}>
                    {asset.compliance.Fail}
                  </div>
                </div>
              </Col>
            </Row>
          </div>
        </div>
      </Col>
      <Col span={3} className="h-full flex-1">
        <div className="flex flex-col flex-1 h-full">
          <div className="p-2 h-1/2 flex flex-col min-h-0 bg-seperator rounded-lg">
            <BatteryDetail asset={asset} />
            {/* <QRCodeGenerator
              text={`${window.location.protocol}//${window.location.hostname}${window.location.pathname}${window.location.search}${window.location.hash}`}
            /> */}
          </div>
          <div className="p-2 mt-2 bg-seperator rounded-lg h-1/2">
            <Row className="w-full h-full">
              <Col span={24} className="flex flex-col items-center justify-between w-full">
                <h3 className="flex justify-center text-center font-bold">Malicious Process</h3>
                <div className="text-7xl mb-2 text critical">
                  {asset?.security?.maliciousProcesses}
                </div>
              </Col>
            </Row>
          </div>
        </div>
      </Col>

      {/* <div className="flex flex-col items-center justify-center" style={{ width: '15%' }}>
        <Icon
          name={
            asset.systemInfo.os.toLowerCase().indexOf('windows') > 0
              ? 'windows'
              : asset.systemInfo.os.toLowerCase()
          }
          style={{ fontSize: '5rem' }}
        />
        <div className="mt-2">
          <LiveStatus status={asset.currentStatus} />
        </div>
        <div className="mt-2">{asset.hardwareModel}</div>
      </div>
      <div className="flex-1 flex flex-col items-center justify-center border-solid border-border border-l border-r border-y-0 px-2">
        <Description>
          <Description.Item label="Hostname">{asset.hostname}</Description.Item>
          <br />
          <Description.Item label="Owner">
            <User.Picker value={asset.owner} onChange={(id) => onChange({ ...asset, owner: id })} />
          </Description.Item>
          <br />
          <Description.Item label="Department">
            <Department.Picker
              value={asset.department}
              onChange={(id) => onChange({ ...asset, department: id })}
            />
          </Description.Item>
          <br />
          <Description.Item label="Location">
            <Location.Picker
              value={asset.location}
              onChange={(id) => onChange({ ...asset, location: id })}
            />
          </Description.Item>
        </Description>
      </div>
      <div className="flex-1 flex flex-col items-center justify-center border-solid border-border border-r border-y-0 border-l-0 px-2">
        <Description>
          <Description.Item label="Agent Version">{asset.agentVersion}</Description.Item>
          <br />
          <Description.Item label="Last Assessment">{asset.lastAssessment}</Description.Item>
          <br />
          <Description.Item label="Last Seen">{asset.lastSeen}</Description.Item>
          <br />
          <Description.Item label="Created">{asset.createdAt}</Description.Item>
        </Description>
      </div>
      <div className="flex-1 flex flex-col items-center justify-center px-2">
        <div className="flex flex-1 min-w-0 w-full">
          <Row className="flex-1 flex flex-col items-center justify-center border-solid border-border border-r border-y-0 border-l-0 px-2">
            {/* <ValueWithStatus
              value={asset.vulnerability}
              label="Vulnerability"
              icon="vulnerability"
            />
          </div>
          <div className="flex-1 flex flex-col items-center justify-center border-solid border-border border-r border-y-0 border-l-0 px-2">
            <ValueWithStatus value={asset.compliance} label="Compliance" icon="compliance" />
          </div>
          <div className="flex-1 flex flex-col items-center justify-center px-2">
            <ValueWithStatus value={asset.riskScore} label="Risk Score" icon="risk" />
          </div>
        </div>
      </div> */}
    </Row>
  );
}
