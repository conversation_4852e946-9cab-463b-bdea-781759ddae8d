import { useEffect, useState } from 'react';
import Chart from '@/src/components/widget/views/chart/Chart';
import { getAuthEventsChartApi } from '@/src/modules/inventory/api/hosts';

export default function LoginCountChart({ asset, onPointClick }) {
  const [chartData, setChartData] = useState(null);

  useEffect(() => {
    getAuthEventsChartApi(asset.id).then((data) => {
      setChartData(data);
    });
  }, [asset.id]);

  return (
    <div className="pb-2 h-[200px]">
      <div className="px-4 flex flex-col bg-seperator py-2 rounded h-full">
        <div className="flex justify-between">
          <h3 className="">Authentication Trends</h3>
          <div
            className="text-primary cursor-pointer"
            onClick={() =>
              onPointClick({
                action: null,
                eventDate: null
              })
            }>
            View All Authentication Events
          </div>
        </div>
        <div className="flex-1 min-h-0 flex flex-col">
          {chartData && (
            <Chart
              widget={{
                type: 'BarChart',
                'x-axis': 'Event Date, Platform',
                'y-axis': 'count',
                widgetProperties: {
                  legendEnabled: true
                }
              }}
              data={{
                'x-axis': 'Event Date',
                'y-axis': 'count'
              }}
              result={chartData}
              onEvents={{
                click(event) {
                  if (onPointClick) {
                    onPointClick({
                      action: event.seriesName,
                      eventDate: event.name
                    });
                  }
                }
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
}
