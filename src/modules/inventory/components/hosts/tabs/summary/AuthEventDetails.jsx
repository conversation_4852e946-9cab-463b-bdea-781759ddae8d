import ThreatContext from '@/src/components/ThreatContext';
import { CrudProvider } from '@/src/hooks/crud';
import ThreatDetailDrawer from '@/src/modules/alerts/components/ThreatDetailDrawer';
import { getAuthEventsSearchApi } from '@/src/modules/inventory/api/hosts';
import { Drawer } from 'antd';
import { useState } from 'react';

export default function AuthEventDetails({ asset, point, onClose }) {
  const [showThreatContextFor, setThreatContextFor] = useState(null);

  const columns = [
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username'
    },
    {
      title: 'IP Address',
      dataIndex: 'ip_address',
      key: 'ip_address'
    },
    ...(!point.action
      ? [
          {
            title: 'Action',
            dataIndex: 'action',
            key: 'action'
          }
        ]
      : []),
    {
      title: 'Event Time',
      dataIndex: 'event_time',
      key: 'event_time',
      type: 'datetime'
    }
  ];
  return (
    <Drawer
      title={
        <div className="flex justify-between items-center">
          {point.action ? (
            <div className="flex-1 min-w-0 text-ellipsis">
              {['Login', 'Logout'].includes(point.action)
                ? `Successful ${point.action}`
                : point.action}{' '}
              Events on {point.eventDate} for {asset.hostname}
            </div>
          ) : (
            <div className="flex-1 min-w-0 text-ellipsis">
              Authentication Events on {point.eventDate} for {asset.hostname}
            </div>
          )}
        </div>
      }
      placement={'right'}
      width={'70%'}
      onClose={onClose}
      destroyOnClose
      maskClosable={false}
      open={true}>
      <div className="flex flex-col min-h-0 flex-1 h-full">
        <CrudProvider
          hasSearch
          columns={columns}
          resourceTitle="Auth Events"
          prependColumns={[
            {
              title: ' ',
              resizable: false,
              width: 20,
              key: 'threat_context',
              dataIndex: 'threat_context',
              order: -1,
              sortable: false,
              selectable: false,
              render({ record }) {
                return <ThreatContext record={record} onClick={setThreatContextFor} />;
              }
            }
          ]}
          fetchFn={(...args) => getAuthEventsSearchApi(asset.id, point, ...args)}
        />
        {showThreatContextFor ? (
          <ThreatDetailDrawer
            alert={showThreatContextFor}
            onClose={() => setThreatContextFor(null)}
          />
        ) : null}
      </div>
    </Drawer>
  );
}
