import Icon from '@/src/components/Icon';
import { CrudProvider } from '@/src/hooks/crud';
import { Row, Col, Drawer, Divider } from 'antd';
import { useState } from 'react';
import ChartRender from '@/src/components/widget/views/chart/ChartRender';
import processSparklineOptions from '@/src/components/widget/views/chart/sparkline-option-process';
// import processSparklineOptions from '@/src/components/widget/views/chart/sparkline-option-process';
// import ChartRender from '@/src/components/widget/views/chart/ChartRender';
// import UniqBy from 'lodash/uniqBy';
// import SortBy from 'lodash/sortBy';

import Moment from 'moment';

const AreaChart = ({ label, data, dataKey }) => {
  const chartSeries = [
    {
      name: label || 'Utilisation',
      color: '#099dd9',
      data: data.map((i) => [i.event_time, i[dataKey]])
    }
  ];
  const chartOptions = processSparklineOptions(
    chartSeries,
    { type: 'AreaChart', widgetProperties: { opacity: 0 } },
    { dateTime: true, unit: '%', timezone: Moment.tz.guess() }
  );
  return (
    <div style={{ height: '50px' }}>
      <ChartRender style={{ height: '50px' }} options={chartOptions} />
    </div>
  );
};

export default function HardwareDetails({ asset }) {
  const [showPartitions, setShowPartitions] = useState(false);

  const partitionColumns = [
    {
      key: 'path',
      title: 'Path',
      dataIndex: 'path',
      sortable: false
    },
    {
      key: 'total',
      title: 'Total Space',
      dataIndex: 'total',
      sortable: false
    },
    {
      key: 'used',
      title: 'Used Space',
      dataIndex: 'used',
      sortable: false
    },
    {
      key: 'free',
      title: 'Free Space',
      dataIndex: 'free',
      sortable: false
    }
  ];

  function provideDiskPartitionDetails() {
    return Promise.resolve({
      totalCount: asset.systemInfo.diskPartitions.length,
      result: asset.systemInfo.diskPartitions
    });
  }

  return (
    <Row gutter={8} className="mb-2" type="flex">
      <Col span={24}>
        <div className="rounded p-4 h-full bg-seperator">
          {/* <h3 className="my-2">Hardware Details</h3> */}
          <Row gutter={32} className="mt-4">
            <Col span={8} className="flex flex-col justify-around">
              <div className="flex items-center font-semibold text-base">
                <Icon name="cpu" className="mr-2 text-lg" />
                <span>CPU</span>
              </div>
              <Divider className="my-2 mb-4" />
              <div className="flex flex-col flex-1">
                <div className="flex justify-between items-start">
                  <div className="flex flex-col flex-shrink-0 flex-1">
                    <div className="text-neutral-light mb-2">Cores</div>
                    <h3>{asset.systemInfo.cpuCore}</h3>
                  </div>
                  <div className="flex flex-col flex-shrink-0 flex-1">
                    <div className="text-neutral-light mb-2">CPU Brand</div>
                    <h3>{asset.systemInfo.cpu}</h3>
                  </div>
                  {/* <div className="flex flex-col">
                    <div className="text-neutral-light mb-2">Types</div>
                    <h3>{asset.systemInfo.cpuType}</h3>
                  </div> */}
                </div>
                <div className="flex flex-col my-2 flex-1 justify-end">
                  <div className="text-neutral-light mb-2">CPU Utilization</div>
                  {/* <Progress
                    percent={asset.systemInfo.cpuUtilization}
                    className="gradient-progress"
                  /> */}
                  <AreaChart
                    label="CPU"
                    data={asset.systemInfo.cpuUtilization}
                    dataKey="cpu_utilization"
                  />
                </div>
              </div>
            </Col>
            <Col
              span={8}
              className="border-solid border-border border-l border-r border-t-0 border-b-0 flex flex-col justify-around">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center font-semibold text-base">
                  <Icon name="memory" className="mr-1 text-lg" />
                  <span>MEMORY</span>
                </div>
              </div>
              <Divider className="my-2 mb-4" />
              <div className="flex flex-col flex-1">
                <div className="flex justify-between items-center">
                  <div className="flex flex-col">
                    <div className="text-neutral-light mb-2">Total Memory</div>
                    <h3>{asset.systemInfo.memory}</h3>
                  </div>
                  <div className="flex flex-col">
                    <div className="text-neutral-light mb-2">Free Memory</div>
                    <h3>{asset.systemInfo.freeMemory}</h3>
                  </div>
                </div>
                <div className="flex flex-col my-2 flex-1 justify-end">
                  <div className="text-neutral-light mb-2">RAM Utilization</div>
                  {/* <Progress
                    percent={asset.systemInfo.memoryUtilization}
                    className="gradient-progress"
                  /> */}
                  <AreaChart
                    label="Memory"
                    data={asset.systemInfo.memoryUtilization}
                    dataKey="memory_utilization"
                  />
                </div>
                {/* <div className="flex items-center mb-2">
                <Icon name="memory" className="mr-1" />
                <span>RAM Details</span>
              </div>
              <div className="flex flex-col">
                <div className="flex justify-between items-center">
                  <div className="flex flex-col">
                    <div className="text-neutral-light mb-2">Total Memory</div>
                    <h3>{asset.systemInfo.memory}</h3>
                  </div>
                  <div className="flex flex-col">
                    <div className="text-neutral-light mb-2">Free Memory</div>
                    <h3>{asset.systemInfo.freeMemory}</h3>
                  </div>
                </div>
                <div className="flex flex-col my-2">
                  <div className="text-neutral-light mb-2">RAM Utilization</div>
                  <Progress
                    percent={asset.systemInfo.memoryUtilization}
                    className="gradient-progress"
                  />
                </div> */}
                {/* <div className="flex justify-between items-center">
                  <div className="flex flex-col my-2">
                    <div className="text-neutral-light mb-2">SWAP Total</div>
                    <h3>{asset.systemInfo.totalSwapMemory}</h3>
                  </div>
                  <div className="flex flex-col my-2">
                    <div className="text-neutral-light mb-2">SWAP Free</div>
                    <h3>{asset.systemInfo.freeSwapMemory}</h3>
                  </div>
                </div> */}
              </div>
            </Col>
            <Col span={8} className="flex flex-col justify-around">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center font-semibold text-base">
                  <Icon name="disk" className="mr-1 text-lg" />
                  <span>DISK</span>
                </div>
                <div
                  className="text-primary cursor-pointer"
                  onClick={() => setShowPartitions(true)}>
                  View All Disk Partitions
                </div>
              </div>
              <Divider className="my-2 mb-4" />
              <div className="flex flex-col flex-1">
                <div className="flex justify-between items-center">
                  <div className="flex flex-col">
                    <div className="text-neutral-light mb-2">Total Disk Space</div>
                    <h3>{asset.systemInfo.disk}</h3>
                  </div>
                  <div className="flex flex-col">
                    <div className="text-neutral-light mb-2">Free Disk Space</div>
                    <h3>{asset.systemInfo.freeDiskSpace}</h3>
                  </div>
                </div>
                <div className="flex flex-col my-2 flex-1 justify-end">
                  <div className="text-neutral-light mb-2">Disk Utilization</div>
                  {/* <Progress
                    percent={asset.systemInfo.diskUtilization}
                    className="gradient-progress"
                  /> */}
                  <AreaChart
                    label="Disk"
                    data={asset.systemInfo.diskUtilization}
                    dataKey="disk_utilization"
                  />
                </div>
                {/* <div className="flex flex-col my-2">
                  <div className="text-neutral-light mb-2">Partition Details</div>
                  <Row gutter={16}>
                    <Col span={24}>
                      <Button type="link" onClick={() => setShowPartitions(true)}>
                        View All Disk Partitions
                      </Button>
                    </Col>
                    {/* {asset.systemInfo.diskPartitions.slice(0, 1).map((item) => (
                      <Col span={8} key={item.id}>
                        <span className="text-neutral-light mb-2">{item.path}</span>
                        <h3>
                          {item.used_space_gb}/{item.total_space_gb}
                        </h3>
                      </Col>
                    ))}
                  </Row>
                </div> */}
              </div>
            </Col>
          </Row>
          <Drawer
            title={`Disk Partitions of ${asset.hostname}`}
            placement="right"
            width="50%"
            onClose={() => setShowPartitions(false)}
            destroyOnClose
            open={showPartitions}>
            <CrudProvider
              resourceTitle="Partition Details"
              hasSearch={false}
              disableColumnSelection
              defaultPageSize={asset.systemInfo.diskPartitions.length}
              fetchFn={(...args) => provideDiskPartitionDetails(...args)}
              columns={partitionColumns}
            />
          </Drawer>
        </div>
      </Col>
    </Row>
  );
}
