import Icon from '@/src/components/Icon';

const SeverityCount = ({ type = 'all', count, onClick }) => {
  return (
    <div
      className={`px-1 py-1 rounded-lg bg ${type.toLowerCase()} shadow ${
        count > 0 ? 'cursor-pointer' : ''
      } flex items-center`}
      {...(count > 0 ? { onClick } : {})}>
      <span className="text-base px-2 font-semibold">{type.toUpperCase()}</span>
      <span className={`text-lg font-semibold ${type.toLowerCase()} px-2  rounded-lg`}>
        {count}
      </span>
    </div>
  );
};

export default function AlertBar({ alert, onClick }) {
  return (
    <div className="pb-2">
      <div className="flex bg-seperator px-4 py-2 rounded-lg items-center">
        <div className="text-lg font-bold w-1/4">
          <Icon name="alert" className="mr-3 text-2xl relative top-[2px]" />
          <span>Alert</span>
        </div>
        <div className="flex items-center w-3/4 justify-between">
          {Object.keys(alert).map((i) => (
            <SeverityCount onClick={() => onClick(i)} key={i} type={i} count={alert[i]} />
          ))}
        </div>
      </div>
    </div>
  );
}
