import { Row, Col, Button } from 'antd';

export default function ApplicationDetails({ asset, handleChangeTab }) {
  return (
    <Row className="h-full">
      <Col span={24} className="mb-2">
        <div className="rounded p-4 h-full bg-seperator">
          <Row gutter={32} className="items-center h-full">
            <Col span={8} className="text-center">
              <div className="flex flex-col justify-center items-center">
                <div className="w-14 h-14">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" fill="none">
                    <defs>
                      <linearGradient
                        id="application_svg_gradient"
                        x1="213.031"
                        y1="251.599"
                        x2="10.9047"
                        y2="2.86734"
                        gradientUnits="userSpaceOnUse">
                        <stop stopColor="#AA4559" />
                        <stop offset="1" stopColor="#3584DC" />
                      </linearGradient>
                    </defs>
                    <path
                      fillRule="evenodd"
                      opacity="0.7"
                      clipRule="evenodd"
                      d="M6.4 23.6067C7.04 23.6067 7.65333 23.3401 8.10667 22.9001C8.56 22.4467 8.8 21.8467 8.8 21.1934V12.8201H17.0667C17.7067 12.8201 18.32 12.5534 18.7733 12.1134C19.2267 11.6601 19.4667 11.0601 19.4667 10.4067C19.4667 9.75341 19.2133 9.15341 18.7733 8.71341C18.3333 8.26007 17.7067 7.99341 17.0667 7.99341H6.4C5.76 7.99341 5.14667 8.24674 4.69333 8.70007C4.24 9.15341 4 9.75341 4 10.4067V21.2067C4 21.8467 4.25333 22.4601 4.69333 22.9134C5.14667 23.3667 5.74667 23.6201 6.4 23.6201V23.6067ZM48.8533 41.7934C49.1733 41.2201 49.3467 40.5801 49.3333 39.9267V24.0467C49.3333 23.3934 49.1733 22.7534 48.8533 22.1801C48.5333 21.6067 48.0667 21.1267 47.4933 20.7934L33.8 12.7934C32.7067 12.1534 31.2933 12.1534 30.2 12.7934L16.5067 20.8067C15.9467 21.1401 15.48 21.6201 15.16 22.1934C14.84 22.7667 14.6667 23.4067 14.68 24.0601V39.9401C14.68 40.5934 14.84 41.2334 15.16 41.8067C15.48 42.3801 15.9467 42.8601 16.52 43.1934L30.2133 51.1934C30.76 51.5134 31.3867 51.6734 32.0133 51.6734C32.64 51.6734 33.2667 51.5001 33.8133 51.1801L47.5067 43.1667C48.0667 42.8334 48.5333 42.3534 48.8533 41.7801V41.7934ZM29.6 33.5667V45.2601L19.4667 39.3267V27.6334L29.6 33.5534V33.5667ZM44.5333 27.6467V39.3267L34.4 45.2467V33.5534L44.5333 27.6334V27.6467ZM32 29.3801L21.68 23.3534L32 17.3267L42.32 23.3534L32 29.3801ZM8.8 51.1667H17.0667L17.08 51.18C17.72 51.18 18.3467 51.4467 18.7867 51.9C19.2267 52.34 19.48 52.94 19.48 53.5934C19.48 54.2467 19.2267 54.8467 18.7733 55.3C18.3333 55.74 17.72 56.0067 17.08 56.0067H6.41334C5.76 56.0067 5.16 55.7534 4.70667 55.3C4.26667 54.8467 4.01334 54.2334 4.01334 53.5934V42.7934C4.01334 42.14 4.25334 41.54 4.70667 41.0867C5.6 40.1667 7.21334 40.18 8.10667 41.0867C8.54667 41.5267 8.8 42.14 8.8 42.78V51.1667ZM59.3067 8.70007C58.8533 8.24674 58.2533 7.99341 57.6 7.99341H46.9333C46.2933 7.99341 45.68 8.26007 45.2267 8.70007C44.7733 9.15341 44.5333 9.75341 44.5333 10.4067C44.5333 11.0601 44.7867 11.6601 45.2267 12.1001C45.6667 12.5534 46.2933 12.8201 46.9333 12.8201H55.2V21.2067C55.2 21.8467 55.4533 22.4601 55.8933 22.9001C56.3333 23.3534 56.96 23.6201 57.6 23.6201C58.24 23.6201 58.8533 23.3667 59.3067 22.9134C59.76 22.4601 60 21.8601 60 21.2067V10.4067C60 9.76674 59.7467 9.15341 59.3067 8.70007ZM55.2 42.7934C55.2 42.14 55.44 41.54 55.8933 41.0867H55.9067C56.8 40.18 58.4 40.1667 59.3067 41.0867C59.7467 41.54 60 42.1534 60 42.7934V53.5934C60 54.2467 59.7467 54.8467 59.2933 55.3C58.8533 55.7534 58.24 56.0067 57.6 56.0067H46.9333C46.2933 56.0067 45.6667 55.74 45.2267 55.2867C44.7867 54.8467 44.5333 54.2467 44.5333 53.5934C44.5333 52.94 44.7733 52.34 45.2267 51.8867C45.68 51.4467 46.2933 51.18 46.9333 51.18H55.2V42.7934Z"
                      fill="url(#application_svg_gradient)"
                    />
                  </svg>
                </div>
                <div className="my-2">
                  <h3>Applications</h3>
                </div>
              </div>
            </Col>
            <Col span={16}>
              <div className="flex justify-between my-2">
                <div className="text-neutral-light">Installed Softwares</div>
                <Button type="link" onClick={() => handleChangeTab('sbom')}>
                  <h3 className="text-color">{asset.osAndSoftwares.packages}</h3>
                </Button>
              </div>
              {/* <div className="flex justify-between my-2">
                <div className="text-neutral-light">Blacklisted Softwares</div>
                <h3>{asset.osAndSoftwares.blacklisted}</h3>
              </div> */}
              <div className="flex justify-between my-2">
                <div className="text-neutral-light">EOL Softwares</div>
                <Button type="link" onClick={() => handleChangeTab('sbom')}>
                  <h3 className="text-color">{asset.osAndSoftwares.eol}</h3>
                </Button>
              </div>
            </Col>
          </Row>
        </div>
      </Col>
      <Col span={24} className="mb-2">
        <div className="rounded p-4 h-full bg-seperator">
          <Row gutter={32} className="items-center h-full">
            <Col span={8} className="text-center">
              <div className="flex flex-col justify-center items-center">
                <div className="w-14 h-14">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 65 64" fill="none">
                    <defs>
                      <linearGradient
                        id="process_svg_gradient"
                        x1="213.031"
                        y1="251.599"
                        x2="10.9047"
                        y2="2.86734"
                        gradientUnits="userSpaceOnUse">
                        <stop stopColor="#AA4559" />
                        <stop offset="1" stopColor="#3584DC" />
                      </linearGradient>
                    </defs>
                    <path
                      opacity="0.7"
                      d="M53.428 41.3599H41.348C40.6413 41.3599 39.9613 41.6399 39.468 42.1466C38.9746 42.6533 38.6813 43.3199 38.6813 44.0266C38.6813 44.7333 38.9613 45.4133 39.468 45.9066C39.9746 46.3999 40.6413 46.6933 41.348 46.6933H47.748C44.8013 49.7733 41.0146 51.8933 36.8546 52.7866C32.6946 53.6933 28.3613 53.3199 24.4146 51.7466C20.468 50.1733 17.0813 47.4399 14.6813 43.9199C12.2946 40.3999 11.0146 36.2399 11.0013 31.9866C11.0013 31.2799 10.7213 30.5999 10.2146 30.1066C9.70797 29.5999 9.0413 29.3199 8.33464 29.3199C7.62797 29.3199 6.94797 29.5999 6.45464 30.1066C5.94797 30.6133 5.66797 31.2799 5.66797 31.9866C5.6813 37.1999 7.21464 42.2799 10.0946 46.6266C12.9613 50.9733 17.0546 54.3733 21.8413 56.4266C26.628 58.4799 31.908 59.0799 37.0413 58.1599C42.1613 57.2399 46.908 54.8399 50.6946 51.2666V55.9866C50.6946 56.6933 50.9746 57.3733 51.4813 57.8666C51.988 58.3599 52.6546 58.6533 53.3613 58.6533C54.068 58.6533 54.748 58.3733 55.2413 57.8666C55.7346 57.3599 56.028 56.6933 56.028 55.9866V43.9866C56.028 43.2933 55.748 42.6399 55.268 42.1466C54.788 41.6533 54.1346 41.3733 53.4413 41.3466L53.428 41.3599ZM40.3346 31.9999C40.3346 30.4133 39.868 28.8666 38.988 27.5599C38.108 26.2399 36.8546 25.2133 35.4013 24.6133C33.9346 24.0133 32.3346 23.8533 30.7746 24.1599C29.228 24.4666 27.8013 25.2266 26.6813 26.3466C25.5613 27.4666 24.8013 28.8933 24.4946 30.4399C24.188 31.9866 24.348 33.5999 24.948 35.0666C25.548 36.5333 26.5746 37.7733 27.8946 38.6533C29.2146 39.5333 30.7613 39.9999 32.3346 39.9999C34.4546 39.9999 36.4946 39.1599 37.988 37.6533C39.4813 36.1466 40.3346 34.1199 40.3346 31.9999ZM29.668 31.9999C29.668 31.4666 29.828 30.9599 30.1213 30.5199C30.4146 30.0799 30.828 29.7333 31.3213 29.5333C31.8146 29.3333 32.348 29.2799 32.868 29.3866C33.388 29.4933 33.8546 29.7466 34.228 30.1199C34.6013 30.4933 34.8546 30.9733 34.9613 31.4799C35.068 31.9866 35.0146 32.5333 34.8146 33.0266C34.6146 33.5199 34.268 33.9333 33.828 34.2266C33.388 34.5199 32.868 34.6799 32.348 34.6799C31.6413 34.6799 30.9613 34.3999 30.468 33.8933C29.9746 33.3866 29.6813 32.7199 29.6813 32.0133L29.668 31.9999ZM32.3346 5.33325C25.4946 5.34659 18.9346 7.99992 13.988 12.7199V7.99992C13.988 7.29325 13.708 6.61325 13.2013 6.11992C12.6946 5.61325 12.028 5.33325 11.3213 5.33325C10.6146 5.33325 9.93464 5.61325 9.4413 6.11992C8.93464 6.62659 8.65464 7.29325 8.65464 7.99992V19.9999C8.65464 20.7066 8.93464 21.3866 9.4413 21.8799C9.94797 22.3866 10.6146 22.6666 11.3213 22.6666H23.3213C24.028 22.6666 24.708 22.3866 25.2013 21.8799C25.708 21.3733 25.988 20.7066 25.988 19.9999C25.988 19.2933 25.708 18.6133 25.2013 18.1199C24.6946 17.6133 24.028 17.3333 23.3213 17.3333H16.9213C19.8546 14.2666 23.6546 12.1333 27.8013 11.2399C31.9613 10.3333 36.2813 10.6933 40.2413 12.2799C44.188 13.8533 47.5746 16.5733 49.9746 20.0933C52.3613 23.6133 53.6546 27.7599 53.668 32.0133C53.668 32.7199 53.948 33.3999 54.4546 33.8933C54.9613 34.3866 55.628 34.6799 56.3346 34.6799C57.0413 34.6799 57.7213 34.3999 58.2146 33.8933C58.708 33.3866 59.0013 32.7199 59.0013 32.0133C59.0013 28.5066 58.308 25.0399 56.9746 21.8133C55.628 18.5733 53.668 15.6399 51.188 13.1599C48.708 10.6799 45.7746 8.71992 42.5346 7.37325C39.2946 6.02659 35.828 5.34659 32.3346 5.34659V5.33325Z"
                      fill="url(#process_svg_gradient)"
                    />
                  </svg>
                </div>
                <div className="my-2">
                  <h3>Process</h3>
                </div>
              </div>
            </Col>
            <Col span={16}>
              <div className="flex justify-between my-2">
                <div className="text-neutral-light">Total Running Process</div>
                <Button type="link" onClick={() => handleChangeTab('process')}>
                  <h3 className="text-color">{asset.osAndSoftwares.processes}</h3>
                </Button>
              </div>
              <div className="flex justify-between my-2">
                <div className="text-neutral-light">Malicious Process</div>
                <Button type="link" onClick={() => handleChangeTab('process')}>
                  <h3 className="text-color">{asset.security.maliciousProcesses}</h3>
                </Button>
              </div>
            </Col>
          </Row>
        </div>
      </Col>
      <Col span={24}>
        <div className="rounded p-4 h-full bg-seperator">
          <Row gutter={32} className="items-center h-full">
            <Col span={8} className="text-center">
              <div className="flex flex-col justify-center items-center">
                <div className="w-14 h-14">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 65 64" fill="none">
                    <defs>
                      <linearGradient
                        id="certificate_svg_gradient"
                        x1="213.031"
                        y1="251.599"
                        x2="10.9047"
                        y2="2.86734"
                        gradientUnits="userSpaceOnUse">
                        <stop stopColor="#AA4559" />
                        <stop offset="1" stopColor="#3584DC" />
                      </linearGradient>
                    </defs>
                    <path
                      opacity="0.7"
                      d="M22.8 42.4H42.2667C43.6 42.4 44.6667 41.3334 44.6667 40C44.6667 38.6667 43.6 37.6 42.2667 37.6H22.8C21.4667 37.6 20.4 38.6667 20.4 40C20.4 41.3334 21.4667 42.4 22.8 42.4ZM33.0667 33.7333C39.3333 33.7333 44.5333 28.5334 44.5333 22.2667C44.5333 16 44.4 20.1334 44.1333 19.0667C44 18.5334 43.6 18 42.9333 17.7334C42.4 17.4667 41.7333 17.3334 41.2 17.6L38.2667 18.5333L39.8667 15.8667C40.5333 14.8 40.1333 13.3334 39.0667 12.6667C37.3333 11.6 35.2 11.0667 33.2 11.0667C26.9333 11.0667 21.7333 16.2667 21.7333 22.5333C21.7333 28.8 26.9333 34 33.2 34L33.0667 33.7333ZM33.0667 15.4667C33.4667 15.4667 34 15.4667 34.4 15.4667L31.0667 21.0667C30.5333 21.8667 30.6667 22.9333 31.2 23.7333C31.7333 24.5333 32.8 24.8 33.7333 24.5333L39.8667 22.6667C39.6 26.1334 36.6667 28.9333 33.0667 28.9333C29.4667 28.9333 26.2667 25.8667 26.2667 22.1334C26.2667 18.4 29.3333 15.3334 33.0667 15.3334V15.4667ZM22.8 51.2H42.2667C43.6 51.2 44.6667 50.1333 44.6667 48.8C44.6667 47.4667 43.6 46.4 42.2667 46.4H22.8C21.4667 46.4 20.4 47.4667 20.4 48.8C20.4 50.1333 21.4667 51.2 22.8 51.2ZM50.9333 2.93335H14.4C13.0667 2.93335 12 4.00002 12 5.33335V58.6667C12 60 13.0667 61.0667 14.4 61.0667H50.9333C52.2667 61.0667 53.3333 60 53.3333 58.6667V5.33335C53.3333 4.00002 52.2667 2.93335 50.9333 2.93335ZM48.5333 56.2667H16.6667V7.73335H48.5333V56.2667Z"
                      fill="url(#certificate_svg_gradient)"
                    />
                  </svg>
                </div>
                <div className="my-2">
                  <h3>Certificates</h3>
                </div>
              </div>
            </Col>
            <Col span={16}>
              <div className="flex justify-between my-2">
                <div className="text-neutral-light">Installed Certificates</div>
                <Button type="link" onClick={() => handleChangeTab('certificates')}>
                  <h3 className="text-color">{asset.osAndSoftwares.certificates}</h3>
                </Button>
              </div>
              <div className="flex justify-between my-2">
                <div className="text-neutral-light">Expired Certificates</div>
                <Button type="link" onClick={() => handleChangeTab('certificates')}>
                  <h3 className="text-color">{asset.security.expiredCertificates}</h3>
                </Button>
              </div>
            </Col>
          </Row>
        </div>
      </Col>
    </Row>
  );
}
