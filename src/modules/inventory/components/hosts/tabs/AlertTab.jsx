import { CrudProvider } from '@/src/hooks/crud';
import { Tabs, Divider, Row, Col, Segmented, Button } from 'antd';
import { useEffect, useState } from 'react';
import { AppstoreOutlined, BarsOutlined } from '@ant-design/icons';
import { Severity as SeverityPicker } from '@/src/components/pickers/SeverityPicker';
import Severity from '@/src/components/Severity';
import {
  getAllAlertsApi,
  getModuleWiseCountApi,
  getSeverityWiseCountApi
} from '@/src/modules/alerts/api/alert-list';
import { AlertCard } from '@/src/modules/alerts/components/AlertCard';
import ThreatDetailDrawer from '@/src/modules/alerts/components/ThreatDetailDrawer';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';
import ThreatContext from '@/src/components/ThreatContext';
import { AssetPolicyAttributes } from '@/src/components/pickers/AssetPolicyAttributePicker';
import { VulnerabilityPolicyAttributes } from '@/src/components/pickers/VulnerabilityAttributePicker';
import { FimPolicyAttributes } from '@/src/components/pickers/FimPolicyAttributePicker';
import { IntegrationAction } from '@/src/components/pickers/IntegrationActionPicker';
import { Configuration } from '@/src/components/pickers/ConfigurationPicker';
import { Asset } from '@/src/components/pickers/AssetPicker';
import PolicyForm from '@/src/modules/settings/components/policy-management/PolicyForm';
import { createPolicyApi } from '@/src/modules/settings/api/policy-management/policies';

export default function AlertTab({ asset, defaultSelectedSeverity }) {
  const [severityCounts, setSeverityCounts] = useState([]);
  const [moduleCounts, setModuleCounts] = useState([]);
  const [selectedSeverity, setSelectedSeverity] = useState(defaultSelectedSeverity || null);
  const [selectedModule, setSelectedModule] = useState(null);
  const [viewType, setViewType] = useState('list');
  const [showThreatContextFor, setThreatContextFor] = useState(null);
  const [timeline, setTimeline] = useState({});

  const columns = [
    {
      title: 'Alert',
      key: 'alert',
      dataIndex: 'alert'
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      render({ record }) {
        return <Severity severity={record.severity} useTag />;
      }
    },
    {
      title: 'Module',
      dataIndex: 'module',
      key: 'module'
    },
    {
      title: 'Attribute',
      dataIndex: 'attribute',
      key: 'attribute',
      ellipsis: true
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value'
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
      sortable: false
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    }
  ];

  useEffect(() => {
    getSeverityWiseCountApi(asset.id, { timeline }).then((categories) =>
      setSeverityCounts(categories)
    );
    getModuleWiseCountApi(asset.id, { timeline }).then((categories) => setModuleCounts(categories));
  }, [asset, timeline]);

  return (
    <div className="flex flex-col min-h-0 h-full">
      <div className="flex min-h-0 flex-1 overflow-auto">
        <Row gutter={16} className="w-full">
          <Col span={4} className="min-h-0 overflow-auto h-full mb-4">
            <h4 className="font-semibold">Modules</h4>
            <Tabs
              activeKey={selectedModule}
              tabPosition="left"
              className="w-full"
              onChange={(key) => setSelectedModule(key)}
              items={moduleCounts.map((moduleObj) => ({
                label: `${moduleObj.moduleName} (${moduleObj.count})`,
                key: moduleObj.moduleName
              }))}
            />
            <Divider />
            <h4 className="font-semibold">Severity</h4>
            <Tabs
              activeKey={selectedSeverity}
              tabPosition="left"
              className="w-full"
              onChange={(key) => setSelectedSeverity(key)}
              items={severityCounts.map((severity) => ({
                label: `${severity.severity} (${severity.count})`,
                key: severity.severity
              }))}
            />
          </Col>
          <SeverityPicker.Provider>
            <AssetPolicyAttributes.Provider>
              <VulnerabilityPolicyAttributes.Provider>
                <FimPolicyAttributes.Provider>
                  <Asset.Provider>
                    <IntegrationAction.Provider>
                      <Configuration.Provider>
                        <Col
                          span={20}
                          className="min-h-0 overflow-auto h-full flex flex-col min-w-0">
                          <CrudProvider
                            columns={columns}
                            defaultPageSize={30}
                            beforeCreateSlot={() => (
                              <div className="mr-2">
                                <TimelinePicker value={timeline} onChange={setTimeline} />
                              </div>
                            )}
                            key={`${selectedSeverity}-${selectedModule}-${JSON.stringify(
                              timeline
                            )}`}
                            resourceTitle="Alerts"
                            hasSearch
                            defaultFormItem={{
                              module: 'Endpoint',
                              status: true,
                              context: [
                                {
                                  join: 'and'
                                }
                              ]
                            }}
                            prependColumns={[
                              {
                                title: ' ',
                                resizable: false,
                                width: 20,
                                key: 'threat_context',
                                dataIndex: 'threat_context',
                                order: -1,
                                sortable: false,
                                selectable: false,
                                render({ record }) {
                                  return (
                                    <ThreatContext record={record} onClick={setThreatContextFor} />
                                  );
                                }
                              }
                            ]}
                            fetchFn={(...args) =>
                              getAllAlertsApi(...args, {
                                severity: selectedSeverity,
                                moduleName: selectedModule,
                                assetId: asset.id,
                                timeline
                              })
                            }
                            drawerTitle={() => `Create Alert Configuration`}
                            createFn={createPolicyApi}
                            formFields={(item, update) => {
                              return <PolicyForm item={item} update={update} />;
                            }}
                            createSlot={(create, { fetchData }) => (
                              <div className="inline-flex items-center">
                                <Button
                                  className="mr-2"
                                  type="primary"
                                  ghost
                                  onClick={() => create()}>
                                  Configure Alert
                                </Button>
                                <Segmented
                                  value={viewType}
                                  onChange={(e) => setViewType(e)}
                                  options={[
                                    { value: 'list', icon: <BarsOutlined /> },
                                    { value: 'card', icon: <AppstoreOutlined /> }
                                  ]}
                                />
                              </div>
                            )}
                            tableSlot={
                              viewType === 'card'
                                ? ({ data }) => (
                                    <div className="px-2">
                                      <Row gutter={16}>
                                        {data.map((item) => (
                                          <AlertCard key={item.id} alert={item} span={8} />
                                        ))}
                                      </Row>
                                    </div>
                                  )
                                : null
                            }
                          />
                        </Col>
                      </Configuration.Provider>
                    </IntegrationAction.Provider>
                  </Asset.Provider>
                </FimPolicyAttributes.Provider>
              </VulnerabilityPolicyAttributes.Provider>
            </AssetPolicyAttributes.Provider>
          </SeverityPicker.Provider>
        </Row>
      </div>
      {showThreatContextFor ? (
        <ThreatDetailDrawer
          alert={showThreatContextFor}
          onClose={() => setThreatContextFor(null)}
        />
      ) : null}
    </div>
  );
}
