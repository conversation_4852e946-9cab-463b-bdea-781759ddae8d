import { <PERSON>, Ta<PERSON>, But<PERSON>, Modal } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  getListeningPortsForAssetApi,
  getNetworkForAssetApi,
  executeActionApi
} from '../../../api/hosts';
import { useState } from 'react';
import { useLayout } from '@/src/layouts/Layout';
import Permission<PERSON>hecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import Icon from '@/src/components/Icon';

// import Icon from '@/src/components/Icon';

export default function NetworkTab({ asset }) {
  const isOnline = (asset.currentStatus || '').toLowerCase() === 'online';
  const [selectedCategory, setSelectedCategory] = useState('interface');
  const [currentCount, setCurrentCount] = useState({});
  const { message } = useLayout();
  const [modal, contextHolder] = Modal.useModal();

  const columns = [
    {
      title: 'Interface',
      dataIndex: 'interface',
      key: 'interface'
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address'
    },
    {
      title: 'MAC',
      dataIndex: 'mac',
      key: 'mac'
    },
    {
      title: 'Mask',
      dataIndex: 'mask',
      key: 'mask'
    },
    // {
    //   title: 'Broadcast',
    //   dataIndex: 'broadcast',
    //   key: 'broadcast'
    // },
    // {
    //   title: 'Point To Point',
    //   dataIndex: 'point_to_point',
    //   key: 'point_to_point'
    // },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type'
    },
    {
      title: 'Friendly Name',
      dataIndex: 'friendly_name',
      key: 'friendly_name',
      render({ record }) {
        let typeIcon =
          (record.type || '').toLowerCase().indexOf('wireless') >= 0 ||
          (record.type || '').toLowerCase().indexOf('ieee') >= 0 ||
          (record.type || '').toLowerCase().indexOf('wifi') >= 0
            ? 'wifi'
            : (record.type || '').toLowerCase().indexOf('loopback') >= 0
            ? 'loopback'
            : 'ethernet';
        return (
          <div className="flex items-center">
            <Icon name={typeIcon} className={`mr-2 text-lg text-neutral-light`} />
            {record.friendly_name}
          </div>
        );
      }
    },
    // {
    //   title: 'Manufacturer',
    //   dataIndex: 'manufacturer',
    //   key: 'manufacturer'
    // },
    // {
    //   title: 'Enabled',
    //   dataIndex: 'enabled',
    //   key: 'enabled'
    // },
    {
      title: 'Gateway',
      dataIndex: 'gateway',
      key: 'gateway'
    },
    // {
    //   title: 'Physical Adapter',
    //   dataIndex: 'physicalAdapter',
    //   key: 'physicalAdapter',
    //   hidden: true
    // },
    {
      title: 'DHCP Enabled',
      dataIndex: 'dhcpEnabled',
      key: 'dhcpEnabled',
      hidden: true
    }
    // {
    //   title: 'DHCP Server',
    //   dataIndex: 'dhcpServer',
    //   key: 'dhcpServer',
    //   hidden: true
    // },
    // {
    //   title: 'DNS Host',
    //   dataIndex: 'dnsHostName',
    //   key: 'dnsHostName',
    //   hidden: true
    // }
  ];

  const portColumns = [
    {
      title: 'PID',
      dataIndex: 'pid',
      key: 'pid'
    },
    {
      title: 'Port',
      dataIndex: 'port',
      key: 'port'
    },
    {
      title: 'Protocol',
      dataIndex: 'protocol',
      key: 'protocol',
      render({ record }) {
        return record.protocol.toUpperCase();
      }
    },
    {
      title: 'Blacklisted',
      dataIndex: 'is_blacklisted',
      key: 'is_blacklisted',
      align: 'center',
      render({ record }) {
        return record.is_blacklisted ? <Tag color="error">Yes</Tag> : <Tag color="success">No</Tag>;
      }
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address'
    }
    // {
    //   title: 'FD',
    //   dataIndex: 'fd',
    //   key: 'fd'
    // },
    // {
    //   title: 'Socket',
    //   dataIndex: 'socket',
    //   key: 'socket'
    // },
    // {
    //   title: 'Net Namespace',
    //   dataIndex: 'net_namespace',
    //   key: 'net_namespace'
    // },
    // {
    //   title: 'Path',
    //   dataIndex: 'path',
    //   key: 'path'
    // }
  ];

  // const networkQuickActions = [
  //   {
  //     key: 'block_port',
  //     label: (
  //       <span className="inline-flex items-center">
  //         <Icon name="remediation" className="mr-1 text-lg" />
  //         <span>Block Port</span>
  //       </span>
  //     )
  //   }
  // ];

  function onNetworkQuickMenuClick({ key }, record) {
    const keyDisplayMap = {
      block_port: 'Block Port'
    };
    modal.confirm({
      title: 'Confirm',
      content: `Are you sure you want to ${keyDisplayMap[key] || key}`,
      okText: 'Yes',
      cancelText: 'Cancel',
      centered: true,
      confirmLoading: true,
      destroyOnClose: true,
      maskClosable: false,
      okType: 'default',
      zIndex: 99,

      onOk() {
        return executeActionApi(key, record, asset).then(() => {
          if (key === 'block_port') {
            return message.success(`Port will be blocked`);
            // return message.success(`${record.port} will be blocked`);
          }
        });
      }
    });
  }

  return (
    <div className="flex flex-col min-h-0 overflow-auto">
      <div className="flex min-h-0">
        <div className="flex flex-col">
          <Tabs
            activeKey={selectedCategory}
            tabPosition="left"
            onChange={(key) => setSelectedCategory(key)}
            style={{ height: 220 }}
            items={[
              {
                label: `Interfaces ${
                  currentCount.interface !== undefined ? `(${currentCount.interface})` : ''
                }`,
                key: 'interface'
              },
              {
                label: `Listening Ports ${
                  currentCount.ports !== undefined ? `(${currentCount.ports})` : ''
                }`,
                key: 'ports'
              }
            ]}
          />
        </div>
        <div className="flex flex-1 min-h-0 flex-col min-w-0">
          {selectedCategory === 'interface' ? (
            <CrudProvider
              columns={columns}
              key="interface"
              resourceTitle="Interface"
              beforeCreateSlot={() => (
                <span className="mr-2">{`Showing results for ${asset.lastSeen}`}</span>
              )}
              hasSearch
              fetchFn={(...args) => {
                return getNetworkForAssetApi(asset.id, ...args).then((data) => {
                  setCurrentCount((count) => ({ ...count, interface: data.totalCount }));
                  return data;
                });
              }}
            />
          ) : (
            <CrudProvider
              columns={portColumns}
              appendColumns={[
                {
                  title: '',
                  dataIndex: 'actions',
                  key: 'actions',
                  render({ record }) {
                    return (
                      <div className="flex ">
                        <PermissionChecker permission={constants.Block_Port}>
                          <Button
                            type="primary"
                            disabled={!isOnline}
                            danger
                            size="small"
                            onClick={() => onNetworkQuickMenuClick({ key: 'block_port' }, record)}>
                            <span className="inline-flex items-center">Block Port</span>
                          </Button>
                        </PermissionChecker>
                      </div>
                    );
                  }
                }
              ]}
              key="port"
              beforeCreateSlot={() => (
                <span className="mr-2">{`Showing results for ${asset.lastSeen}`}</span>
              )}
              resourceTitle="Listening Port"
              hasSearch
              fetchFn={(...args) => {
                return getListeningPortsForAssetApi(asset.id, ...args).then((data) => {
                  setCurrentCount((count) => ({ ...count, ports: data.totalCount }));
                  return data;
                });
              }}
            />
          )}
        </div>
      </div>
      {contextHolder}
    </div>
  );
}
