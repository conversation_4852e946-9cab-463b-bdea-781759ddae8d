import { useEffect, useState } from 'react';
import { Tabs, Tag } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { getCertificateCountsApi, getCertificatesForAssetApi } from '../../../api/hosts';
import Status from '@/src/components/Status';
import { useAuth } from '@/src/hooks/auth';

export default function CertificateTab({ asset }) {
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const { formatDateTime } = useAuth();

  const columns = [
    {
      title: 'Common Name',
      dataIndex: 'commonName',
      key: 'commonName'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      sortable: false,
      render({ record }) {
        return <Status status={record.status} useTag />;
      }
    },
    {
      title: 'CA',
      dataIndex: 'ca',
      key: 'ca'
    },
    {
      title: 'Self Signed',
      dataIndex: 'self_signed',
      key: 'self_signed',
      render({ record }) {
        return (
          <Tag color={record.self_signed ? 'error' : 'processing'}>
            {record.self_signed ? 'Yes' : 'No'}
          </Tag>
        );
      }
    },
    {
      title: 'Issuer',
      dataIndex: 'issuer',
      key: 'issuer',
      hidden: true
    },
    {
      title: 'Not Valid Before',
      dataIndex: 'notValidBefore',
      key: 'notValidBefore',
      // type: 'datetime',
      hidden: true,
      render({ record }) {
        return <span> {formatDateTime(record?.notValidBefore * 1000 || '')}</span>;
      }
    },
    {
      title: 'Not Valid After',
      dataIndex: 'notValidAfter',
      key: 'notValidAfter',
      // type: 'datetime'
      render({ record }) {
        return <span> {formatDateTime(record?.notValidAfter * 1000 || '')}</span>;
      }
    },
    {
      title: 'Signing Algorithm',
      dataIndex: 'signingAlgorithm',
      key: 'signingAlgorithm',
      hidden: true
    },
    {
      title: 'Key Algorithm',
      dataIndex: 'keyAlgorithm',
      key: 'keyAlgorithm',
      hidden: true
    }
  ];

  useEffect(() => {
    getCertificateCountsApi(asset.id).then((categories) => setCategories(categories));
  }, [asset]);

  return (
    <div className="flex flex-col min-h-0 overflow-auto">
      <div className="flex min-h-0">
        <div className="flex flex-col">
          <Tabs
            activeKey={selectedCategory}
            tabPosition="left"
            onChange={(key) => setSelectedCategory(key)}
            style={{ height: 220 }}
            items={Object.keys(categories).map((category) => ({
              label: `${category} (${categories[category]})`,
              key: category
            }))}
          />
        </div>
        <div className="flex flex-1 min-h-0 flex-col min-w-0">
          <CrudProvider
            defaultPageSize={20}
            key={selectedCategory}
            columns={columns}
            beforeCreateSlot={() => (
              <span className="mr-2">{`Showing results for ${asset.lastSeen}`}</span>
            )}
            resourceTitle="Certificate"
            hasSearch
            fetchFn={(...args) =>
              getCertificatesForAssetApi(
                asset.id,
                selectedCategory === 'All' ? null : selectedCategory,
                ...args
              )
            }
          />
        </div>
      </div>
    </div>
  );
}
