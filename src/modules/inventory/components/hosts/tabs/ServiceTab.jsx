import { But<PERSON>, Modal } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { executeActionApi, getServicesForAssetApi } from '../../../api/hosts';
import Status from '@/src/components/Status';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { useLayout } from '@/src/layouts/Layout';

// import Icon from '@/src/components/Icon';

export default function ServiceTab({ asset }) {
  const isOnline = (asset.currentStatus || '').toLowerCase() === 'online';
  const [modal, contextHolder] = Modal.useModal();
  const { message } = useLayout();
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sortable: true
    },
    {
      title: 'Command Line',
      dataIndex: 'cmdline',
      key: 'cmdline',
      sortable: true,
      ellipsis: true
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      sortable: true
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      sortable: true,
      render({ record }) {
        return <Status useTag status={record.status} />;
      }
    },
    {
      title: 'Start Type',
      dataIndex: 'start_type',
      key: 'start_type',
      sortable: true,
      render({ record }) {
        return <Status useTag status={record.start_type} />;
      }
    },
    {
      title: 'Actiions',
      dataIndex: 'actions',
      key: 'actions',
      buttons: [],
      prependAction({ record }) {
        const is_running = ['running', 'active'].includes((record.status || '').toLowerCase());
        return is_running ? (
          <PermissionChecker permission={constants.Disable_Service}>
            <Button
              disabled={!isOnline}
              onClick={() => onServiceActioClick({ key: 'disable_service' }, record)}
              danger>
              Stop Service
            </Button>
          </PermissionChecker>
        ) : (
          <PermissionChecker permission={constants.Enable_Service}>
            <Button
              type="primary"
              disabled={!isOnline}
              onClick={() => onServiceActioClick({ key: 'enable_service' }, record)}>
              Start Service
            </Button>
          </PermissionChecker>
        );
      }
    }
  ];

  function onServiceActioClick({ key }, record) {
    const keyDisplayMap = {
      disable_service: 'Disable Service',
      enable_service: 'Enable Service'
    };
    modal.confirm({
      title: 'Confirm',
      content: `Are you sure you want to ${keyDisplayMap[key] || key} ${record.name}?`,
      okText: 'Yes',
      cancelText: 'Cancel',
      centered: true,
      confirmLoading: true,
      destroyOnClose: true,
      maskClosable: false,
      okType: 'default',
      zIndex: 99,

      onOk() {
        return executeActionApi(key, record, asset).then(() => {
          if (key === 'enable_service') {
            return message.success(`${record.name} will be started`);
          }
          if (key === 'disable_service') {
            return message.success(`${record.name} will be stopped`);
          }
        });
      }
    });
  }

  return (
    <div className="flex flex-col min-h-0 overflow-auto">
      <div className="flex min-h-0">
        <div className="flex flex-1 min-h-0 flex-col min-w-0">
          <CrudProvider
            columns={columns}
            key="service"
            resourceTitle="Service"
            beforeCreateSlot={() => (
              <span className="mr-2">{`Showing results for ${asset.lastSeen}`}</span>
            )}
            hasSearch
            fetchFn={(...args) => {
              return getServicesForAssetApi(asset.id, ...args);
            }}
          />
        </div>
      </div>
      {contextHolder}
    </div>
  );
}
