import { Row, Col } from 'antd';
import { ReactComponent as Logo } from '@/src/assets/logo.svg';
import { HeatDot } from '@/src/components/Heatmap';
import PackageLogo from '@/src/modules/device-automation/components/PackageLogo';

const colorMap = {
  unknown: 'var(--neutral-regular)',
  success: 'var(--secondary-green)',
  failed: 'var(--secondary-red)'
};

const statusMap = {
  success: 'Passed',
  failed: 'Failed'
};

export default function ComplianceHeatMap({ data, onView }) {
  return (
    <div className="overflow-x-hidden">
      <Row gutter={16}>
        {data.map((item) => (
          <Col key={item.id} span={8}>
            <div
              className="bg-seperator rounded-lg shadow flex flex-col px-4 py-2 relative cursor-pointer"
              onClick={() => onView(item)}>
              <h3 className="text-lg font-semibold">
                <PackageLogo disabled package={item} style={{ width: '50px' }} className="mr-2" />
                {item.name}
              </h3>
              <div className="flex flex-wrap">
                {item.rules.map((rule) => (
                  <HeatDot
                    styleColor={colorMap[rule.status || 'unknwon']}
                    key={rule.id}
                    tooltip={decodeURI(
                      `${rule.name}%0A${encodeURI(`bindings:${rule.bindings}`)}%0A${
                        statusMap[rule.status]
                      }`
                    )}
                  />
                ))}
              </div>
              <div className="w-1/2 absolute right-0 top-0 bottom-0 py-2">
                <Logo className="w-full h-full" />
              </div>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
}
