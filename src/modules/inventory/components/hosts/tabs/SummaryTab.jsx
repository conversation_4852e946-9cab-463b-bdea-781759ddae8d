import { Row, Col, Input } from 'antd';
import Status from '@/src/components/Status';
import {
  getLoggedInUsersForHostApi,
  getStartupItemsForHostApi,
  getUsersForHostApi
} from '../../../api/hosts';
import { useState } from 'react';
import Overview from './summary/Overview';
import AlertBar from './summary/AlertBar';
import HardwareDetails from './summary/HardwareDetails';
import ApplicationDetails from './summary/ApplicationDetails';
import GridCard from './summary/GridCard';
import Icon from '@/src/components/Icon';
import LoginCountChart from './summary/LoginCountChart';
import AuthEventDetails from './summary/AuthEventDetails';
// import { useLayout } from '@/src/layouts/Layout';

export default function SummaryTab({
  asset,
  onChange,
  onSeverityClick,
  onCountClick,
  handleChangeTab
}) {
  // eslint-disable-next-line
  const [totalUsers, setTotalUsers] = useState(0);
  // eslint-disable-next-line
  const [totalStartupItems, setTotalStartupItems] = useState(0);
  // eslint-disable-next-line
  const [totalLoggedInUsers, setTotalLoggedInUsers] = useState(0);
  const [selectedAuthPoint, setAuthSelectedPoint] = useState(null);
  // const { message } = useLayout();

  const startupColumns = [
    {
      key: 'name',
      dataIndex: 'name',
      title: 'Name',
      width: '50%',
      ellipsis: true
    },
    {
      key: 'status',
      dataIndex: 'status',
      width: '30%',
      title: 'Status',
      render({ record }) {
        return <Status status={record.status} useIcon />;
      }
    }
    // {
    //   key: 'action',
    //   dataIndex: 'action',
    //   title: '',
    //   render({ record }) {
    //     return (
    //       <Button
    //         onClick={() =>
    //           message.success(
    //             `${record.name}  will be ${
    //               record.status === 'inactive' || record.status === 'disabled'
    //                 ? 'enabled'
    //                 : 'disabled'
    //             } `
    //           )
    //         }>
    //         {['inactive', 'disable'].includes(record.status.toLowerCase()) ? 'Enable' : 'Disable'}
    //       </Button>
    //     );
    //   }
    // }
  ];

  const usersColumns = [
    {
      key: 'uid',
      dataIndex: 'uid',
      title: 'UID'
    },
    {
      key: 'username',
      dataIndex: 'username',
      title: 'Username',
      ellipsis: true
    },
    {
      key: 'description',
      dataIndex: 'description',
      title: 'Description',
      ellipsis: true
    },
    {
      key: 'directory',
      dataIndex: 'directory',
      title: 'Directory',
      ellipsis: true
    },
    {
      key: 'shell',
      dataIndex: 'shell',
      title: 'Shell',
      ellipsis: true
    }
  ];

  const loggedInUsersColumns = [
    {
      key: 'user',
      dataIndex: 'user',
      title: 'User',
      ellipsis: true
    },
    {
      key: 'host',
      dataIndex: 'host',
      title: 'Host',
      ellipsis: true
    },
    {
      key: 'pid',
      dataIndex: 'pid',
      title: 'PID',
      ellipsis: true
    },
    {
      key: 'sid',
      dataIndex: 'sid',
      title: 'SID',
      ellipsis: true
    },
    {
      key: 'tty',
      dataIndex: 'tty',
      title: 'TTY',
      ellipsis: true
    },
    {
      key: 'type',
      dataIndex: 'type',
      title: 'Type',
      ellipsis: true
    }
  ];

  return (
    <Row>
      <Col span={24}>
        <Row>
          <Col span={24}>
            <Overview asset={asset} onChange={onChange} onCountClick={onCountClick} />
          </Col>
          <Col span={24}>
            <AlertBar alert={asset.alert} onClick={onSeverityClick} />
          </Col>
          <Col span={24}>
            <LoginCountChart asset={asset} onPointClick={setAuthSelectedPoint} />
            {selectedAuthPoint ? (
              <AuthEventDetails
                onClose={() => setAuthSelectedPoint(null)}
                asset={asset}
                point={selectedAuthPoint}
              />
            ) : null}
          </Col>
        </Row>
        <HardwareDetails asset={asset} />
        <Row gutter={8} className="mb-2">
          <Col span={8}>
            <ApplicationDetails asset={asset} handleChangeTab={(tab) => handleChangeTab(tab)} />
          </Col>
          <Col span={16}>
            <GridCard
              columns={startupColumns}
              resourceTitle="Start-Up Item"
              tableClassNames="transparent"
              headingSlot={() => (
                <div className="flex items-center">
                  <Icon name="startup_items" className="mr-1 text-lg" />
                  <span>Startup Items</span>
                </div>
              )}
              createSlot={(_, { setSearchTerm }) => (
                <Input.Search
                  placeholder="Search..."
                  className="transparent"
                  onSearch={setSearchTerm}
                  style={{ width: 200 }}
                />
              )}
              fetchFn={(...args) =>
                getStartupItemsForHostApi(asset.id, ...args).then((data) => {
                  setTotalStartupItems(data.totalCount);
                  return data;
                })
              }
            />
          </Col>
        </Row>
        <Row gutter={8}>
          <Col span={12}>
            <GridCard
              columns={loggedInUsersColumns}
              resourceTitle="Logged In Users"
              fetchFn={(...args) =>
                getLoggedInUsersForHostApi(asset.id, ...args).then((data) => {
                  setTotalLoggedInUsers(data.totalCount);
                  return data;
                })
              }
              tableClassNames="transparent"
              headingSlot={() => (
                <div className="flex items-center">
                  <Icon name="loggedin_user" className="mr-1 text-lg" />
                  <span>Logged in Users</span>
                </div>
              )}
              createSlot={(_, { setSearchTerm }) => (
                <Input.Search
                  placeholder="Search..."
                  className="transparent"
                  onSearch={setSearchTerm}
                  style={{ width: 200 }}
                />
              )}
            />
          </Col>
          <Col span={12}>
            <GridCard
              columns={usersColumns}
              resourceTitle="System Users"
              fetchFn={(...args) =>
                getUsersForHostApi(asset.id, ...args).then((data) => {
                  setTotalUsers(data.totalCount);
                  return data;
                })
              }
              tableClassNames="transparent"
              headingSlot={() => (
                <div className="flex items-center">
                  <Icon name="system_user" className="mr-1 text-lg" />
                  <span>System Users</span>
                </div>
              )}
              createSlot={(_, { setSearchTerm }) => (
                <Input.Search
                  placeholder="Search..."
                  className="transparent"
                  onSearch={setSearchTerm}
                  style={{ width: 200 }}
                />
              )}
            />
          </Col>
        </Row>
      </Col>
    </Row>
  );
}
