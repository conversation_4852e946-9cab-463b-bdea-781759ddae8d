import Status from '@/src/components/Status';
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import {
  getOpenSocketsForAssetApi,
  getProcessesForAssetApi,
  executeActionApi
} from '../../../api/hosts';
import ProcessExpandedRow from '../expanded-row/ProcessExpandedRow';
import { useState } from 'react';
import { VirusTotalLink } from '@/src/components/VirusTotalLink';
import ThreatDetailDrawer from '@/src/modules/alerts/components/ThreatDetailDrawer';
import ThreatContext from '@/src/components/ThreatContext';
import { useLayout } from '@/src/layouts/Layout';
import Icon from '@/src/components/Icon';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import duration from '@/src/utils/duration';
import { bytesToSize } from '@/src/utils/bytes';

export default function ProcessTab({ asset }) {
  const isOnline = (asset.currentStatus || '').toLowerCase() === 'online';
  const [selectedCategory, setSelectedCategory] = useState('processes');
  const [showThreatContextFor, setThreatContextFor] = useState(null);
  const [currentCount, setCurrentCount] = useState({});
  const { message } = useLayout();
  const [modal, contextHolder] = Modal.useModal();

  const processColumns = [
    {
      title: 'PID',
      dataIndex: 'pid',
      key: 'pid',
      width: 100,
      align: 'center',
      render({ record }) {
        return (
          <div className="flex items-center flex-wrap">
            <div className="flex-1">{record.pid}</div>
          </div>
        );
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render({ record }) {
        return <Status status={record.status} useTag />;
      },
      width: 100,
      hidden: true
    },
    {
      title: 'Malicious',
      dataIndex: 'suspicious',
      key: 'suspicious',
      align: 'center',
      sortable: false,
      render({ record }) {
        return !record.is_malicious ? (
          <div className="text-center text-danger text-lg">
            <Status status="No" color="success" useTag />
          </div>
        ) : (
          <div className="text-center text-success text-lg">
            <Status status="Yes" color="error" useTag />
          </div>
        );
      },
      width: 100
    },
    {
      title: 'Process Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Memory',
      dataIndex: 'total_size',
      key: 'total_size',
      sortable: true,
      render({ record }) {
        return `${bytesToSize(record.total_size)}`;
      }
    },
    {
      title: 'CPU(%)',
      dataIndex: 'cpu_utilization',
      key: 'cpu_utilization',
      sortable: true,
      render({ record }) {
        return `${record.cpu_utilization}%`;
      }
    },
    {
      title: 'Command Line',
      dataIndex: 'commandline',
      key: 'commandline',
      type: 'commandline',
      ellipsis: true
    },
    {
      title: 'Path',
      dataIndex: 'path',
      key: 'path',
      type: 'path',
      ellipsis: true
    },
    {
      title: 'SHA256',
      dataIndex: 'sha256',
      key: 'sha256',
      ellipsis: true,
      hidden: true,
      render({ record }) {
        return <VirusTotalLink value={record.sha256} />;
      }
    },
    {
      title: 'MD5',
      dataIndex: 'md5',
      key: 'md5',
      ellipsis: true,
      hidden: true,
      render({ record }) {
        return <VirusTotalLink value={record.md5} />;
      }
    },
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
      ellipsis: true
    },
    {
      title: 'UID',
      dataIndex: 'uid',
      key: 'uid',
      ellipsis: true,
      hidden: true
    },
    {
      title: 'Created On',
      dataIndex: 'created_time',
      key: 'created_time',
      hidden: true,
      type: 'datetime'
    },
    {
      title: 'Execution Time',
      dataIndex: 'execution_time',
      key: 'execution_time',
      ellipsis: true,
      hidden: true,
      render({ record }) {
        return record.execution_time ? duration(record.execution_time) : '';
      }
    }
  ];

  const openSocketColumns = [
    {
      title: 'PID',
      dataIndex: 'pid',
      key: 'pid',
      width: 100
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Blacklisted',
      dataIndex: 'blacklisted',
      key: 'blacklisted',
      align: 'center',
      sortable: false,
      render({ record }) {
        return !record.is_blacklisted ? (
          <div className="text-center text-danger text-lg">
            <Status status="No" color="success" useTag />
          </div>
        ) : (
          <div className="text-center text-success text-lg">
            <Status status="Yes" color="error" useTag />
          </div>
        );
      },
      width: 100
    },
    {
      title: 'Protocol',
      dataIndex: 'protocol',
      key: 'protocol',
      render({ record }) {
        return record.protocol.toUpperCase();
      }
    },
    {
      title: 'Port',
      dataIndex: 'local_port',
      key: 'local_port'
    },
    {
      title: 'Local Address',
      dataIndex: 'local_address',
      key: 'local_address'
    },
    {
      title: 'Remote Port',
      dataIndex: 'remote_port',
      key: 'remote_port'
    },
    {
      title: 'Remote Address',
      dataIndex: 'remote_address',
      key: 'remote_address'
    },
    {
      title: 'State',
      dataIndex: 'state',
      key: 'state'
    },
    {
      title: 'Created On',
      dataIndex: 'created_time',
      key: 'created_time',
      hidden: true,
      type: 'datetime'
    }
  ];

  function onProcessQuickMenuClick({ key }, record) {
    const keyDisplayMap = {
      kill_process: 'Kill Process',
      block_ip: 'Block IP',
      block_port: 'Block Port'
    };
    modal.confirm({
      title: 'Confirm',
      content: `Are you sure you want to ${keyDisplayMap[key] || key} ${
        key === 'kill_process' ? record.name : key === 'block_ip' ? record.remote_address : ''
      }?`,
      okText: 'Yes',
      cancelText: 'Cancel',
      centered: true,
      confirmLoading: true,
      destroyOnClose: true,
      maskClosable: false,
      okType: 'default',
      zIndex: 99,

      onOk() {
        return executeActionApi(key, record, asset).then(() => {
          if (key === 'kill_process') {
            return message.success(`${record.name} will be killed`);
          }
          if (key === 'block_ip') {
            return message.success(`${record.remote_address} IP will be blocked`);
          }
          if (key === 'block_port') {
            return message.success(`port will be blocked`);
          }
        });
      }
    });
  }

  return (
    <div className="flex flex-col min-h-0 overflow-auto">
      <div className="flex min-h-0">
        <div className="flex flex-col">
          <Tabs
            activeKey={selectedCategory}
            tabPosition="left"
            onChange={(key) => setSelectedCategory(key)}
            style={{ height: 220 }}
            items={[
              {
                label: `Processes ${
                  currentCount.processes !== undefined ? `(${currentCount.processes})` : ''
                }`,
                key: 'processes'
              },
              {
                label: `Remote Connections ${
                  currentCount.open_sockets !== undefined ? `(${currentCount.open_sockets})` : ''
                }`,
                key: 'open_sockets'
              }
            ]}
          />
        </div>
        <div className="flex flex-1 min-h-0 flex-col min-w-0">
          {selectedCategory === 'processes' ? (
            <CrudProvider
              columns={processColumns}
              appendColumns={[
                {
                  title: '',
                  dataIndex: 'actions',
                  key: 'actions',
                  render({ record }) {
                    return (
                      <PermissionChecker permission={constants.Kill_Process}>
                        <Button
                          type="primary"
                          disabled={!isOnline}
                          danger
                          size="small"
                          className="ml-2"
                          onClick={() => onProcessQuickMenuClick({ key: 'kill_process' }, record)}>
                          <div className="inline-flex items-center">
                            <Icon name="kill-process" className="mr-1" />
                            <span>Kill Process</span>
                          </div>
                        </Button>
                      </PermissionChecker>
                    );
                    // return (
                    //   <Dropdown
                    //     menu={{
                    //       items: processQuickActions,
                    //       onClick: (item) => onProcessQuickMenuClick(item, record)
                    //     }}
                    //     trigger="click"
                    //     placement="bottomRight">
                    //     <Button
                    //       type="danger"
                    //       className="flex-shrink-0"
                    //       onClick={(e) => e.preventDefault()}>
                    //       <span className="inline-flex items-center text-danger">
                    //         <Icon name="settings_dropdown" className="mr-1 text-2xl" />
                    //         <Icon name="chevron-down" />
                    //       </span>
                    //     </Button>
                    //   </Dropdown>
                    // <Button
                    //   type="primary"
                    //   size="small"
                    //   onClick={() => message.success(`${record.name}  will be killed`)}>
                    //   Kill Process
                    // </Button>
                    // );
                  }
                }
              ]}
              defaultPageSize={20}
              resourceTitle="Process"
              hasSearch
              beforeCreateSlot={() => (
                <span className="mr-2">{`Showing results for ${asset.lastSeen}`}</span>
              )}
              prependColumns={[
                {
                  title: ' ',
                  resizable: false,
                  width: 20,
                  key: 'threat_context',
                  dataIndex: 'threat_context',
                  order: -1,
                  sortable: false,
                  selectable: false,
                  render({ record }) {
                    return <ThreatContext record={record} onClick={setThreatContextFor} />;
                  }
                }
              ]}
              expandable={{
                expandedRowRender: (record, index, indent, expanded) => (
                  <ProcessExpandedRow isOpen={expanded} asset={asset} process={record} />
                ),
                rowExpandable: (record) => true,
                expandIcon: ({ expanded, onExpand, record }) => (
                  <span className="text-label">
                    {expanded ? (
                      <DownOutlined className="text-label" onClick={(e) => onExpand(record, e)} />
                    ) : (
                      <RightOutlined className="text-label" onClick={(e) => onExpand(record, e)} />
                    )}
                  </span>
                )
              }}
              fetchFn={(...args) => {
                return getProcessesForAssetApi(asset.id, ...args).then((data) => {
                  setCurrentCount((count) => ({ ...count, processes: data.totalCount }));
                  return data;
                });
              }}
            />
          ) : (
            <CrudProvider
              columns={openSocketColumns}
              key="open_sockets"
              beforeCreateSlot={() => (
                <span className="mr-2">{`Showing results for ${asset.lastSeen}`}</span>
              )}
              resourceTitle="open_sockets"
              hasSearch
              appendColumns={[
                {
                  title: '',
                  dataIndex: 'actions',
                  key: 'actions',
                  render({ record }) {
                    return (
                      <div className="flex ">
                        <PermissionChecker permission={constants.Block_IP_Address}>
                          <Button
                            disabled={!isOnline}
                            type="primary"
                            danger
                            size="small"
                            className="ml-2"
                            onClick={() => onProcessQuickMenuClick({ key: 'block_ip' }, record)}>
                            <span className="inline-flex items-center ">Block IP</span>
                          </Button>
                        </PermissionChecker>
                      </div>
                    );
                  }
                }
              ]}
              prependColumns={[
                {
                  title: ' ',
                  resizable: false,
                  width: 20,
                  key: 'threat_context',
                  dataIndex: 'threat_context',
                  order: -1,
                  sortable: false,
                  selectable: false,
                  render({ record }) {
                    return <ThreatContext record={record} onClick={setThreatContextFor} />;
                  }
                }
              ]}
              fetchFn={(...args) => {
                return getOpenSocketsForAssetApi(asset.id, ...args).then((data) => {
                  setCurrentCount((count) => ({ ...count, open_sockets: data.totalCount }));
                  return data;
                });
              }}
            />
          )}
        </div>
      </div>
      {showThreatContextFor ? (
        <ThreatDetailDrawer
          alert={showThreatContextFor}
          onClose={() => setThreatContextFor(null)}
        />
      ) : null}
      {contextHolder}
    </div>
  );
}
