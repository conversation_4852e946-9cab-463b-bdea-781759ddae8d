import { useEffect, useState } from 'react';
import { Ta<PERSON>, Button } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  getPackagesCategoryApi,
  getPackagesForAssetApi,
  updateInventoryPackageApi
} from '../../../api/hosts';
import VulnerabilityDrawer from '../../VulnerabilityDrawer';
import VulnerabilityDot from '@/src/components/VulnerabilityDot';

export default function PackageTab({ asset }) {
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedPackage, setVulnerabilityView] = useState(null);

  const columns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   key: 'id',
    //   width: 100
    // },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version'
    },
    {
      title: 'Release',
      dataIndex: 'release',
      key: 'release'
    },
    {
      title: 'Release Date',
      dataIndex: 'releaseDate',
      key: 'releaseDate',
      sortable: false
    },
    {
      title: 'EOS',
      dataIndex: 'eosDate',
      key: 'eosDate',
      sortable: false
    },
    {
      title: 'EOL',
      dataIndex: 'eolDate',
      key: 'eolDate',
      sortable: false
    },
    {
      title: 'Vulnerability',
      dataIndex: 'vulnerability',
      key: 'vulnerability',
      sortable: false,
      render({ record, update }) {
        return (
          <Button type="link" onClick={() => setVulnerabilityView(record)}>
            {record.vulnerability}
          </Button>
        );
      }
    }
    // {
    //   title: 'Blacklisted',
    //   dataIndex: 'blacklist',
    //   key: 'blacklist',
    //   sortable: false,
    //   render({ record, update }) {
    //     return (
    //       <Switch
    //         checked={record.blacklist}
    //         onChange={(blacklist) => update({ ...record, blacklist })}
    //       />
    //     );
    //   }
    // }
  ];

  useEffect(() => {
    getPackagesCategoryApi(asset.id).then((categories) => setCategories(categories));
  }, [asset]);

  return (
    <div className="flex flex-col min-h-0 overflow-auto">
      <div className="flex min-h-0">
        <div className="flex flex-col">
          <Tabs
            activeKey={selectedCategory}
            tabPosition="left"
            onChange={(key) => setSelectedCategory(key)}
            style={{ height: 220 }}
            items={Object.keys(categories).map((category) => ({
              label: `${category} (${categories[category]})`,
              key: category
            }))}
          />
        </div>
        <div className="flex flex-1 min-h-0 flex-col min-w-0">
          <CrudProvider
            defaultPageSize={20}
            key={selectedCategory}
            columns={columns}
            prependColumns={[
              {
                title: '',
                dataIndex: 'dot_id',
                key: 'dot_id',
                sortable: false,
                width: 50,
                render({ record }) {
                  return (
                    <VulnerabilityDot
                      eolDate={record.eolDate}
                      vulnerability={record.vulnerability}
                    />
                  );
                }
              }
            ]}
            beforeCreateSlot={() => (
              <span className="mr-2">{`Showing results for ${asset.lastSeen}`}</span>
            )}
            resourceTitle="Package"
            updateFn={updateInventoryPackageApi}
            hasSearch
            fetchFn={(...args) =>
              getPackagesForAssetApi(
                asset.id,
                selectedCategory === 'All' ? null : selectedCategory,
                ...args
              )
            }
          />
          {selectedPackage ? (
            <VulnerabilityDrawer
              software={selectedPackage}
              closeDrawer={() => setVulnerabilityView(null)}
              assetId={+asset.id}
            />
          ) : null}
        </div>
      </div>
    </div>
  );
}
