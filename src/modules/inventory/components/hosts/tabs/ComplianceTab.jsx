import { Drawer, Spin, Progress } from 'antd';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { CrudProvider } from '@/src/hooks/crud';
import { getComplianceForAssetApi } from '../../../api/hosts';
import ComplianceDetailDrawer from '../../ComplianceDetailDrawer';
import PackageLogo from '@/src/modules/device-automation/components/PackageLogo';

export default function ComplianceTab({ asset, deploymentId }) {
  const [complianceItem, setViewComplianceItem] = useState(null);

  const columns = [
    {
      title: 'Framework',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link',
      render({ record }) {
        return (
          <div className="flex items-center">
            <PackageLogo disabled package={record} style={{ width: '30px' }} className="mr-2" />
            {/* eslint-disable-next-line */}
            <a
              className="cursor-pointer"
              // eslint-disable-next-line
              href="javascript:;"
              onClick={() => setViewComplianceItem(record)}>
              {record.name}
            </a>
          </div>
        );
      }
    },
    {
      title: 'Deployment',
      dataIndex: 'deployment_name',
      key: 'deployment_name'
    },
    {
      title: 'Result',
      dataIndex: 'result',
      key: 'result',
      align: 'center',
      exportFormatter(record) {
        return `${record.passedRules} / ${record.testedRules}`;
      },
      render({ record }) {
        let progress = (record.passedRules * 100) / record.testedRules;
        return (
          <div className="flex items-center">
            <Progress
              size={{ height: 10 }}
              percent={progress === 0 ? 100 : progress}
              showInfo={false}
              strokeColor={
                progress === 0
                  ? '#c84235'
                  : {
                      '0%': '#f04e3e',
                      '30%': '#f58518',
                      '60%': '#f5bc18',
                      '100%': '#89c540'
                    }
              }
            />
            <span className="flex-shrink-0 ml-2 w-[4rem]">
              {record.passedRules} / {record.testedRules}
            </span>
          </div>
        );
      }
    },
    {
      title: deploymentId ? 'Executed At' : 'Last Executed At',
      dataIndex: 'lastExecutionTime',
      key: 'lastExecutionTime',
      type: 'datetime'
    }
  ];

  const [data, setData] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    getComplianceForAssetApi(asset.id, deploymentId).then((data) => {
      setData(data);
      setLoading(false);
    });
  }, [asset.id, deploymentId]);

  return (
    <>
      {loading ? (
        <div className="flex flex-1 items-center justify-center">
          <Spin spinning={true} />
        </div>
      ) : (
        <>
          <CrudProvider
            columns={columns}
            resourceTitle="Compliance"
            hasSearch={false}
            disablePagination={Boolean(deploymentId)}
            disableColumnSelection={Boolean(deploymentId)}
            disableRefresh
            formDrawerWidth="70%"
            onView={setViewComplianceItem}
            expandable={{
              expandedRowRender: (record, index, indent, expanded) => (
                <ComplianceTab asset={asset} deploymentId={record.deploymentId} />
              ),
              rowExpandable: (record) => Boolean(record.recurring),
              expandIcon: ({ expanded, onExpand, record }) =>
                record.recurring ? (
                  <span className="text-label">
                    {expanded ? (
                      <DownOutlined className="text-label" onClick={(e) => onExpand(record, e)} />
                    ) : (
                      <RightOutlined className="text-label" onClick={(e) => onExpand(record, e)} />
                    )}
                  </span>
                ) : null
            }}
            fetchFn={(offset, size) => {
              return Promise.resolve({
                totalCount: data.length,
                result: data.slice(offset, offset + size)
              });
            }}
          />
          <Drawer
            title={
              complianceItem ? (
                <div className="flex items-center justify-between">
                  <div>{complianceItem.name}</div>
                </div>
              ) : (
                ``
              )
            }
            placement={'right'}
            width={'70%'}
            onClose={() => setViewComplianceItem(null)}
            destroyOnClose
            open={Boolean(complianceItem) || Boolean(complianceItem)}>
            {complianceItem ? (
              <ComplianceDetailDrawer asset={asset} assesment={complianceItem} />
            ) : null}
          </Drawer>
        </>
      )}
    </>
  );
}
