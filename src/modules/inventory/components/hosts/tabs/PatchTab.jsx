import { useEffect } from 'react';
// import Status from '@/src/components/Status';
import Capitalize from 'lodash/capitalize';

import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import Icon from '@/src/components/Icon';
import Severity from '@/src/components/Severity';
import { useAuth } from '@/src/hooks/auth';

import {
  // cleanAllPatchesApi,
  getPatchForInventoryDrilldownApi,
  patchScanApi,
  getPatchScanHistoryApi
} from '../../../api/hosts';

import { addPatchExceptionApi } from '@modules/patch/api/patch-list';
import { useState } from 'react';
import { useLayout } from '@/src/layouts/Layout';

import PatchDeploymentForm from '@modules/patch/components/PatchDeploymentForm';

import { createPatchDeploymentApi } from '@modules/patch/api/patch-deployement';
import { DeploymentPolicy } from '@/src/components/pickers/DeploymentPolicyPicker';
import { User } from '@/src/components/pickers/UserPicker';
import ExternalLink from '@components/common/ExternalLink';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import TaskHistory from '@modules/patch/components/TaskHistory';

import ExceptionModel from '@modules/vulnerability/components/ExceptionModel';

export default function PatchTab({ asset, defaultSelectedCategory }) {
  const { formatDateTime } = useAuth();
  const { message } = useLayout();
  const [openScanHistory, setopenScanHistory] = useState(false);

  const [selectedCategory, setSelectedCategory] = useState(defaultSelectedCategory || 'missing');
  // const [showThreatContextFor, setThreatContextFor] = useState(null);
  const [currentCount, setCurrentCount] = useState({});
  const [patchIdMap, setpatchIdMap] = useState({});
  const [refreshKey, setRrefreshKey] = useState(0);
  const [updateKey, setUpdateKey] = useState(0); // Key to force re-renders

  const [selectedItems, setSelectedItems] = useState([]);
  const [isModalopenFor, setisModalopenFor] = useState(null);

  const [patchScanHistoryContext, setPatchScanHistoryDetail] = useState({});
  useEffect(() => {
    let timeout;
    const getApiFn = () =>
      getPatchScanHistoryApi(asset.id).then((data) => {
        setPatchScanHistoryDetail(data);
        if (timeout) {
          clearTimeout(timeout);
        }
        timeout = setTimeout(getApiFn, 20000);
      });

    timeout = setTimeout(getApiFn, 20000);

    return () => {
      clearTimeout(timeout);
    };
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    getPatchScanHistoryApi(asset.id).then((data) => {
      setPatchScanHistoryDetail(data);
    });
    // eslint-disable-next-line
  }, []);

  function createPatchDeployment(formData) {
    return createPatchDeploymentApi(formData).then((data) => {
      setUpdateKey((prevKey) => prevKey + 1);
      setSelectedItems([]);
      return data;
    });
  }

  function getColumn(selectedCategory) {
    return [
      {
        title: 'ID',
        key: 'name',
        dataIndex: 'name',
        exportFormatter(record) {
          return record?.patch.name;
        },
        render({ record }) {
          return (
            <ExternalLink to={`/patch/patches/${record?.patch.id}`}>
              {record?.patch?.name}
            </ExternalLink>
          );
        }
      },
      {
        title: 'UUID',
        key: 'uuid',
        dataIndex: 'uuid',
        hidden: true,
        exportFormatter(record) {
          return record?.patch.uuid;
        },
        render({ record }) {
          return record?.patch.uuid;
        }
      },
      {
        title: 'Title',
        key: 'title',
        dataIndex: 'title',
        exportFormatter(record) {
          return record?.patch.title;
        },
        render({ record }) {
          return <span> {record?.patch.title}</span>;
        }
      },
      {
        title: 'Severity',
        dataIndex: 'patchSeverity',
        key: 'patchSeverity',
        exportFormatter(record) {
          return record?.patch.patchSeverity;
        },
        render({ record }) {
          return <Severity severity={record?.patch.patchSeverity} useTag />;
        }
      },
      {
        title: 'Platform',
        dataIndex: 'platform',
        key: 'platform',
        sortable: false,
        exportFormatter(record) {
          return record?.patch.osPlatform;
        },
        render({ record }) {
          return (
            <div className="flex items-center">
              <Icon
                name={`platform_${record?.patch?.osPlatform.toLowerCase()}`}
                title={record?.patch?.osPlatform}
                className="text-lg mr-2"
              />
              {record?.patch?.osPlatform}
            </div>
          );
        }
      },

      {
        title: 'Release Date',
        dataIndex: 'releaseDate',
        key: 'releaseDate',
        sortable: false,
        exportFormatter(record) {
          return formatDateTime(record?.patch?.releaseDate || '');
        },
        render({ record }) {
          return <span> {formatDateTime(record?.patch?.releaseDate || '')}</span>;
        }
      },
      {
        title: 'Category',
        dataIndex: 'patchUpdateCategory',
        key: 'patchUpdateCategory',
        sortable: false,
        exportFormatter(record) {
          return record?.patch.patchUpdateCategory;
        },
        render({ record }) {
          return <span> {Capitalize(record?.patch?.patchUpdateCategory || '')}</span>;
        }
      },

      {
        title: 'KBID',
        dataIndex: 'kbId',
        key: 'kbId',
        sortable: false,
        exportFormatter(record) {
          return record?.patch.kbId;
        },
        render({ record }) {
          return <ExternalLink url={record?.patch.supportUrl}>{record?.patch.kbId}</ExternalLink>;
        }
      },
      {
        title: 'Patch Approval Status',
        dataIndex: 'patchApprovalStatus',
        key: 'patchApprovalStatus',
        hidden: true,
        sortable: false,
        render({ record }) {
          return Capitalize(record?.patch?.patchApprovalStatus || '');
        }
      },
      {
        title: 'Patch Test Status',
        dataIndex: 'patchTestStatus',
        key: 'patchTestStatus',
        hidden: true,
        sortable: false,
        render({ record }) {
          return Capitalize((record?.patch?.patchTestStatus || '').replace('_', ' '));
        }
      },
      // {
      //   title: 'Architecture',
      //   dataIndex: 'osArch',
      //   key: 'osArch',
      //   sortable: false,
      //   render({ record }) {
      //     return <span> {record?.patch.osArch}</span>;
      //   }
      // },
      // {
      //   title: 'Bulletin ID',
      //   dataIndex: 'bulletinId',
      //   key: 'bulletinId',
      //   sortable: false,
      //   render({ record }) {
      //     return <span> {record?.patch.bulletinId}</span>;
      //   }
      // },
      // {
      //   title: 'Reboot Required?',
      //   dataIndex: 'rebootBehaviour',
      //   key: 'rebootBehaviour',
      //   render({ record }) {
      //     return rebootMap[record?.patch.rebootBehaviour];
      //   }
      // },
      // {
      //   title: 'Created On',
      //   dataIndex: 'createdTime',
      //   key: 'createdTime',
      //   render({ record }) {
      //     return <span> {formatDateTime(record?.patch.createdTime) || '---'}</span>;
      //   }
      // }

      ...(selectedCategory === 'ignored'
        ? [
            {
              title: 'Exception Reason',
              dataIndex: 'exceptionReason',
              key: 'exceptionReason',
              hidden: true,
              sortable: false,
              render({ record }) {
                return record?.patch?.exceptionReason || '';
              }
            }
          ]
        : selectedCategory !== 'missing'
        ? []
        : [
            {
              title: '',
              dataIndex: 'actions',
              key: 'actions',
              width: 100,

              editPermissions: [constants.Update_Inventory],
              canHaveButtons(record) {
                return [];
              },
              prependAction({ record, edit }) {
                return (
                  <Button
                    // onClick={() => getAndSet(record, edit, true)}
                    onClick={() => {
                      setisModalopenFor(record.patch.id);
                    }}
                    className="mr-2"
                    title="Add Exceptions">
                    Add Exceptions
                  </Button>
                );
              }
            }
          ])
    ];
  }

  // function cleanAllPatches() {
  //   cleanAllPatchesApi().then(() => alert('clean'));
  // }

  function triggerPatchScan() {
    return patchScanApi(asset.id).then((res) => {
      setRrefreshKey((prevKey) => prevKey + 1);
      return message.success(`Patch Scan has been initiated.`);
    });
  }

  function onExceptionAdd(formData) {
    return addPatchExceptionApi(isModalopenFor, {
      ...formData,
      scope: { assetFilter: 2, assets: [+asset.id] }
    }).then((res) => {
      setisModalopenFor(null);
      return message.success(`Exception Added.`);
    });
  }
  return (
    <div className="flex flex-col min-h-0 overflow-auto">
      <div className="flex min-h-0">
        <div className="flex flex-col">
          <Tabs
            activeKey={selectedCategory}
            tabPosition="left"
            onChange={(key) => setSelectedCategory(key)}
            style={{ height: 220 }}
            items={[
              {
                label: `Missing ${
                  currentCount.missing !== undefined ? `(${currentCount.missing})` : ''
                }`,
                key: 'missing'
              },
              {
                label: `Installed ${
                  currentCount.installed !== undefined ? `(${currentCount.installed})` : ''
                }`,
                key: 'installed'
              },
              {
                label: `Exception ${
                  currentCount.ignored !== undefined ? `(${currentCount.ignored})` : ''
                }`,
                key: 'ignored'
              }
            ]}
          />
        </div>
        <User.Provider>
          <DeploymentPolicy.Provider>
            <div className="flex flex-1 min-h-0 flex-col min-w-0">
              {selectedCategory === 'missing' ? (
                <CrudProvider
                  key={updateKey}
                  columns={getColumn(selectedCategory)}
                  defaultPageSize={20}
                  resourceTitle="Patch Deployment"
                  hasSearch
                  onChange={setSelectedItems}
                  selectedItems={selectedItems}
                  allowSelection
                  allowSelectAll
                  beforeCreateSlot={(create, { selectedItems }) => (
                    <>
                      {patchScanHistoryContext.lastScanTime &&
                      !patchScanHistoryContext.isScanRunning ? (
                        <>
                          <span className="mr-2">
                            Last scan at : {formatDateTime(patchScanHistoryContext.lastScanTime)}
                          </span>
                        </>
                      ) : null}
                      {selectedItems?.length ? (
                        <PermissionChecker permission={constants.Create_Patch}>
                          <Button type="primary" className="mr-2" onClick={create}>
                            Install
                          </Button>
                        </PermissionChecker>
                      ) : null}
                      <Button
                        type="primary"
                        className="mr-2"
                        iconPosition={'end'}
                        onClick={() => setopenScanHistory(true)}>
                        Scan History
                      </Button>

                      <PermissionChecker permission={constants.Create_Patch}>
                        <Button
                          key={`${refreshKey}`}
                          type="primary"
                          className="mr-2"
                          onClick={triggerPatchScan}
                          iconPosition={'end'}
                          loading={patchScanHistoryContext.isScanRunning}>
                          {!patchScanHistoryContext.isScanRunning ? 'Scan Now' : 'Scanning'}
                        </Button>
                      </PermissionChecker>
                    </>
                  )}
                  fetchFn={(...args) => {
                    return getPatchForInventoryDrilldownApi(
                      asset.id,
                      selectedCategory,
                      ...args
                    ).then((data) => {
                      setpatchIdMap(() =>
                        (data.result || []).reduce(
                          (acc, patch) => ({
                            ...acc,
                            [patch.id]: patch.patchId
                          }),
                          {}
                        )
                      );

                      setCurrentCount((count) => ({ ...count, missing: data.totalCount }));
                      return data;
                    });
                  }}
                  createFn={createPatchDeployment}
                  defaultFormItem={{
                    deploymentType: 'install',
                    deploymentStage: 'draft',
                    deploymentCategory: 'Patch',
                    refIds: selectedItems.map((id) => patchIdMap[id]).filter(Boolean),
                    scope: { assetFilter: 2, assets: [+asset.id] }
                  }}
                  formFields={(item, _, { disabled }) => (
                    <PatchDeploymentForm item={item} disabled={disabled} />
                  )}
                  formActions={({ formItem, resetForm, processingForm, submitForm }) => (
                    <>
                      <Button
                        type="primary"
                        loading={processingForm}
                        className="mr-2"
                        onClick={() => {
                          submitForm({
                            deploymentStage:
                              formItem.deploymentStage === 'draft'
                                ? 'initiated'
                                : formItem.deploymentStage || 'initiated'
                          });
                        }}>
                        Publish
                      </Button>
                      {formItem.deploymentStage === 'draft' ? (
                        <Button
                          type="primary"
                          loading={processingForm}
                          className="mr-2"
                          onClick={() => {
                            submitForm({
                              deploymentStage: 'draft'
                            });
                          }}>
                          Save As Draft
                        </Button>
                      ) : null}
                      <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
                        Reset
                      </Button>
                    </>
                  )}
                />
              ) : selectedCategory === 'ignored' ? (
                <CrudProvider
                  columns={getColumn(getColumn)}
                  key="ignored"
                  resourceTitle="ignored"
                  hasSearch
                  beforeCreateSlot={() => (
                    <>
                      {/* <Button type="primary" className="mr-2" onClick={cleanAllPatches}>
                        Clean
                      </Button> */}
                      {patchScanHistoryContext.lastScanTime &&
                      !patchScanHistoryContext.isScanRunning ? (
                        <>
                          <span className="mr-2">
                            Last scan at : {formatDateTime(patchScanHistoryContext.lastScanTime)}
                          </span>
                        </>
                      ) : null}
                      <PermissionChecker permission={constants.Create_Patch}>
                        <Button
                          key={`${refreshKey}`}
                          type="primary"
                          className="mr-2"
                          onClick={triggerPatchScan}
                          iconPosition={'end'}
                          loading={patchScanHistoryContext.isScanRunning}>
                          {!patchScanHistoryContext.isScanRunning ? 'Scan Now' : 'Scanning'}
                        </Button>
                      </PermissionChecker>
                    </>
                  )}
                  fetchFn={(...args) => {
                    return getPatchForInventoryDrilldownApi(
                      asset.id,
                      selectedCategory,
                      ...args
                    ).then((data) => {
                      setCurrentCount((count) => ({ ...count, ignored: data.totalCount }));
                      return data;
                    });
                  }}
                />
              ) : (
                <CrudProvider
                  columns={getColumn(selectedCategory)}
                  key="installed"
                  resourceTitle="installed"
                  hasSearch
                  beforeCreateSlot={() => (
                    <>
                      {/* <Button type="primary" className="mr-2" onClick={cleanAllPatches}>
                        Clean
                      </Button> */}
                      {patchScanHistoryContext.lastScanTime &&
                      !patchScanHistoryContext.isScanRunning ? (
                        <>
                          <span className="mr-2">
                            Last scan at : {formatDateTime(patchScanHistoryContext.lastScanTime)}
                          </span>
                        </>
                      ) : null}
                      <PermissionChecker permission={constants.Create_Patch}>
                        <Button
                          key={`${refreshKey}`}
                          type="primary"
                          className="mr-2"
                          onClick={triggerPatchScan}
                          iconPosition={'end'}
                          loading={patchScanHistoryContext.isScanRunning}>
                          {!patchScanHistoryContext.isScanRunning ? 'Scan Now' : 'Scanning'}
                        </Button>
                      </PermissionChecker>
                    </>
                  )}
                  fetchFn={(...args) => {
                    return getPatchForInventoryDrilldownApi(
                      asset.id,
                      selectedCategory,
                      ...args
                    ).then((data) => {
                      setCurrentCount((count) => ({ ...count, installed: data.totalCount }));
                      return data;
                    });
                  }}
                />
              )}
              <Drawer
                title={`
        Scan History`}
                placement="right"
                width="60%"
                onClose={() => setopenScanHistory(false)}
                destroyOnClose
                open={openScanHistory}>
                {openScanHistory && <TaskHistory config={{ assetId: asset.id, type: '2' }} />}
              </Drawer>
            </div>
          </DeploymentPolicy.Provider>
        </User.Provider>
      </div>
      <ExceptionModel
        forPatch
        open={isModalopenFor !== null}
        onAdd={onExceptionAdd}
        onCancel={() => {
          setisModalopenFor(null);
        }}
      />
    </div>
  );
}
