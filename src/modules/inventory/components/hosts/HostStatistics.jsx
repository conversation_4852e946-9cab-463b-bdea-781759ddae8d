import { Row, Col, Spin } from 'antd';
import UniqBy from 'lodash/uniqBy';
import SortBy from 'lodash/sortBy';
import Moment from 'moment';
import { useEffect, useState } from 'react';
import { ReactComponent as Logo } from '@/src/assets/logo.svg';
import Icon from '@/src/components/Icon';
import { getInventoryHostsStatisticsApi } from '../../api/hosts';
import processSparklineOptions from '@/src/components/widget/views/chart/sparkline-option-process';
import ChartRender from '@/src/components/widget/views/chart/ChartRender';

const HostStats = ({ type, count }) => {
  const [display, setDisplay] = useState(false);
  useEffect(() => {
    setTimeout(() => setDisplay(true), 700);
  }, []);
  const chartSeries = [
    {
      name: type,
      color: '#099dd9',
      data: SortBy(
        UniqBy(
          count.chart.map((item) => [Object.keys(item)[0], +Object.values(item)[0]]),
          (i) => i[0]
        ).map((i) => {
          return [+Moment(i[0], 'DD-MM-YYYY'), i[1]];
        }),
        (i) => i[0]
      )
    }
  ];
  const chartOptions = processSparklineOptions(
    chartSeries,
    { type: 'AreaChart', widgetProperties: { opacity: 0 } },
    { dateTime: true }
  );
  return (
    <div className="bg-seperator rounded relative">
      <div className="p-4">
        <div className="text-base mb-4 font-bold">{type}</div>
        <div className="flex justify-between">
          <div className="flex flex-1 items-center">
            <div className="text-5xl font-bold">{count.total}</div>
            <div className="ml-2">
              <div className="text-success flex items-center my-1">
                <Icon name="arrow-up" className="text-lg" /> {count.online} Online
              </div>
              <div className="flex items-center text-danger my-1">
                <Icon name="arrow-down" className="text-lg" /> {count.offline} Offline
              </div>
            </div>
          </div>
          <Icon
            name={`platform_${type.indexOf('All') >= 0 ? 'all' : type.toLowerCase()}`}
            className="text-4xl"
          />
        </div>
      </div>
      <div style={{ height: '40px' }}>
        {display && <ChartRender style={{ height: '40px' }} options={chartOptions} />}
      </div>
      <div className="w-1/2 absolute right-0 top-0 bottom-0 py-2">
        <Logo className="w-full h-full" />
      </div>
    </div>
  );
};

export default function HostStatistics() {
  const [statistics, setStatistics] = useState({});

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    getInventoryHostsStatisticsApi().then((data) => {
      setStatistics(data);
      setLoading(false);
    });
  }, []);

  return (
    <Row gutter={16} className="mb-4 mt-2">
      {loading ? (
        <div className="flex items-center justify-center w-full" style={{ height: '160px' }}>
          <Spin spinning />
        </div>
      ) : (
        Object.keys(statistics).map((type) => (
          <Col span={6} key={type}>
            <HostStats type={type} count={statistics[type]} />
          </Col>
        ))
      )}
    </Row>
  );
}
