import { useState } from 'react';
import Capitalize from 'lodash/capitalize';
import { Tabs, Row, Col, Dropdown, Button, Modal, Drawer, Tag } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { CrudProvider } from '@/src/hooks/crud';
import SummaryTab from './tabs/SummaryTab';
import ProcessTab from './tabs/ProcessTab';
import PackageTab from './tabs/PackageTab';
import CertificateTab from './tabs/CertificateTab';
import VulnerabilityTab from './tabs/VulnerabilityTab';
import ComplianceTab from './tabs/ComplianceTab';
import BrowserTab from './tabs/BrowserTab';
import NetworkTab from './tabs/NetworkTab';
import AlertTab from './tabs/AlertTab';
import PatchTab from './tabs/PatchTab';
import Icon from '@/src/components/Icon';
import AssetInfoRibbon from './AssetInfoRibbon';
import {
  getFileExplorerUrlForAssetApi,
  getRdpUrlForAssetApi,
  getTerminalUrlForAssetApi
} from '../../api/hosts';
import SoftwareMeteringSummary from '../SoftwareMeteringSummary';
import { useLayout } from '@/src/layouts/Layout';
import { executeActionApi, fetchActionHistoryApi } from '../../api/hosts';
import { User } from '@/src/components/pickers/UserPicker';
import { Permissions } from '@/src/components/Permissions';
import constants from '@/src/constants/index';
import ServiceTab from './tabs/ServiceTab';
import { License } from '@/src/components/LicenseProvider';

export default function HostDetail({ asset, onChange, onInvestigate, onDelete }) {
  const { hasPermission } = Permissions.usePermission();
  const { isEndpointOps } = License.useLicense();

  const [activeKey, setActiveKey] = useState('summary');
  const [selectedSeverity, setSelectedSeverity] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [useExploit, setUseExploit] = useState(false);

  const [modal, contextHolder] = Modal.useModal();
  const { message } = useLayout();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const isOnline = (asset.currentStatus || '').toLowerCase() === 'online';

  const tagColorMap = {
    waiting: 'warning',
    initiated: 'processing',
    in_progress: 'processing',
    failed: 'error',
    cancelled: 'error',
    success: 'success',
    reboot_required: 'warning'
  };

  const actions = [
    ...(hasPermission(constants.Remote_Desktop)
      ? [
          {
            key: 'rdp',
            disabled: !isOnline,
            label: (
              <span className="inline-flex items-center">
                <Icon name="rdp" className="mr-1 text-lg" />
                <span>Remote Desktop</span>
              </span>
            )
          }
        ]
      : []),
    ...(hasPermission(constants.Wake_On_LAN)
      ? [
          {
            key: 'WakeOnLan',
            label: (
              <span className="inline-flex items-center">
                <Icon name="restart" className="mr-1 text-lg" />
                <span>Wake On LAN</span>
              </span>
            )
          }
        ]
      : []),
    ...(hasPermission(constants.Terminal)
      ? [
          {
            key: 'terminal',
            disabled: !isOnline,
            label: (
              <span className="inline-flex items-center">
                <Icon name="terminal" className="mr-1 text-lg" />
                <span>Terminal</span>
              </span>
            )
          }
        ]
      : []),
    ...(hasPermission(constants.File_Explorer)
      ? [
          {
            key: 'fileexplorer',
            disabled: !isOnline,
            label: (
              <span className="inline-flex items-center">
                <Icon name="settings" className="mr-1 text-lg" />
                <span>File Explorer</span>
              </span>
            )
          }
        ]
      : []),
    // {
    //   key: 'investigate',
    //   label: (
    //     <span className="inline-flex items-center">
    //       <Icon name="remediation" className="mr-1 text-lg" />
    //       <span>Investigate</span>
    //     </span>
    //   )
    // },
    // {
    //   key: 'wake_on_lan',
    //   label: (
    //     <span className="inline-flex items-center">
    //       <Icon name="restart" className="mr-1 text-lg" />
    //       <span>Wake On LAN</span>
    //     </span>
    //   )
    // },
    ...(hasPermission(constants.Reboot)
      ? [
          {
            key: 'reboot',
            disabled: !isOnline,
            label: (
              <span className="inline-flex items-center">
                <Icon name="restart" className="mr-1 text-lg" />
                <span>Reboot</span>
              </span>
            )
          }
        ]
      : []),
    ...(hasPermission(constants.Shutdown)
      ? [
          {
            key: 'shutdown',
            disabled: !isOnline,
            label: (
              <span className="inline-flex items-center">
                <Icon name="shutdown" className="mr-1 text-lg" />
                <span>Shutdown</span>
              </span>
            )
          }
        ]
      : []),
    ...(hasPermission(constants.Remote_Wipe_Out)
      ? [
          // {
          //   key: 'remote_wipeout',
          //   disabled: !isOnline,
          //   label: (
          //     <span className="inline-flex items-center">
          //       <Icon name="remote-wipeout" className="mr-1 text-lg" />
          //       <span>Remote Wipeout</span>
          //     </span>
          //   )
          // }
        ]
      : []),
    {
      key: 'action_history',
      label: (
        <span className="inline-flex items-center">
          <Icon name="action-history" className="mr-1 text-lg" />
          <span>Action History</span>
        </span>
      )
    },
    ...(hasPermission(constants.Delete_Inventory)
      ? [
          {
            key: 'delete',
            label: (
              <span className="inline-flex items-center">
                <Icon name="delete" className="mr-1 text-lg" />
                <span>Delete</span>
              </span>
            ),
            danger: true
          }
        ]
      : [])
  ];

  const tabs = [
    {
      label: (
        <span className="flex items-center">
          <Icon name="settings" className="text-lg" />
          Summary
        </span>
      ),
      key: 'summary',
      component: (
        <SummaryTab
          asset={asset}
          onChange={onChange}
          onSeverityClick={(severity) => {
            setSelectedSeverity(severity);
            setActiveKey('alert');
          }}
          handleChangeTab={(tab) => setActiveKey(tab)}
          onCountClick={(tab) => {
            if (tab.subTab === 'exploit') {
              setUseExploit(true);
              setSelectedCategory('All');
            } else {
              setUseExploit(false);
              setSelectedCategory(tab.subTab);
            }
            setActiveKey(tab.tab);
          }}
        />
      )
    },
    ...(isEndpointOps()
      ? [
          {
            label: (
              <span className="flex items-center">
                <Icon name="process" className="text-lg" />
                Services
              </span>
            ),
            key: 'service',
            component: <ServiceTab asset={asset} />
          },
          {
            label: (
              <span className="flex items-center">
                <Icon name="process" className="text-lg" />
                Process
              </span>
            ),
            key: 'process',
            component: <ProcessTab asset={asset} />
          },
          {
            label: (
              <span className="flex items-center">
                <Icon name="settings" className="text-lg" />
                Network
              </span>
            ),
            key: 'network',
            component: <NetworkTab asset={asset} />
          }
        ]
      : []),
    {
      label: (
        <span className="flex items-center">
          <Icon name="inventory" className="text-lg" />
          SBOM
        </span>
      ),
      key: 'sbom',
      component: <PackageTab asset={asset} />
    },
    ...(isEndpointOps()
      ? [
          {
            label: (
              <span className="flex items-center">
                <Icon name="inventory" className="text-lg" />
                SW Metering
              </span>
            ),
            key: 'sw-metering',
            component: (
              <div className="flex flex-1 min-h-0 flex-col min-w-0">
                <SoftwareMeteringSummary assetId={asset.id} />
              </div>
            )
          },
          {
            label: (
              <span className="flex items-center">
                <Icon name="certificate" className="text-lg" />
                Certificates
              </span>
            ),
            key: 'certificates',
            component: <CertificateTab asset={asset} />
          }
        ]
      : []),
    {
      label: (
        <span className="flex items-center">
          <Icon name="vulnerability" className="text-lg" />
          Vulnerabilities
        </span>
      ),
      key: 'vulnerabilities',
      component: (
        <VulnerabilityTab
          asset={asset}
          useExploit={useExploit}
          defaultCategory={selectedCategory}
        />
      )
    },
    ...(isEndpointOps()
      ? [
          {
            label: (
              <span className="flex items-center">
                <Icon name="compliance" className="text-lg" />
                Compliance
              </span>
            ),
            key: 'compliance',
            component: <ComplianceTab asset={asset} />
          }
        ]
      : []),
    {
      label: (
        <span className="flex items-center">
          <Icon name="reload" className="text-lg" />
          Browser
        </span>
      ),
      key: 'browser',
      component: <BrowserTab asset={asset} />
    },
    {
      label: (
        <span className="flex items-center">
          <Icon name="alert" className="text-lg" />
          Alert
        </span>
      ),
      key: 'alert',
      component: <AlertTab asset={asset} defaultSelectedSeverity={selectedSeverity} />
    },

    ...(hasPermission(constants.View_Patch)
      ? [
          {
            label: (
              <span className="flex items-center">
                <Icon name="patch" className="text-lg" />
                Patch
              </span>
            ),
            key: 'patch',
            component: <PatchTab asset={asset} defaultSelectedCategory={selectedCategory} />
          }
        ]
      : [])

    // {
    //   label: 'FIM',
    //   key: 'fim'
    // }
  ];

  const actionhistoryColumns = [
    {
      title: 'Id',
      dataIndex: 'id',
      key: 'id',
      sortable: false
    },
    {
      title: 'Action',
      dataIndex: 'displayName',
      key: 'displayName',
      ellipsis: true,
      width: '30%',
      render({ record, view }) {
        const hasLink = ['success', 'failed', 'reboot_required'].includes(record.taskStatus);
        return (
          <div className="flex items-center">
            <div
              className={hasLink ? 'cursor-pointer text-primary' : ''}
              onClick={() => (hasLink ? view(record) : null)}>
              {record.displayName || 'dummy'}
            </div>
          </div>
        );
      }
    },
    // {
    //   title: 'Action',
    //   dataIndex: 'actionType',
    //   key: 'actionType'
    // },

    {
      title: 'Status',
      key: 'taskStatus',
      dataIndex: 'taskStatus',
      width: 80,
      align: 'center',
      sortable: false,
      render({ record }) {
        return (
          <Tag
            color={tagColorMap[record.taskStatus.toLowerCase()]}
            className="inline-flex items-center justify-center"
            style={{
              textAlign: 'center',
              textTransform: 'uppercase',
              minWidth: '80px'
            }}>
            {Capitalize((record.taskStatus || '').toLowerCase()).replaceAll('_', ' ')}
          </Tag>
        );
      }
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      sortable: false,
      render({ record }) {
        return <User.Picker textOnly value={record.createdBy} disabled />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    }
  ];

  function onMenuClick({ key }) {
    if (['wake_on_lan'].includes(key)) {
      return message.success(`Wake on LAN command has been sent successfully.`);
    }
    if (['reboot', 'shutdown', 'remote_wipeout', 'WakeOnLan'].includes(key)) {
      const actionName = {
        reboot: 'Reboot',
        shutdown: 'Shutdown',
        remote_wipeout: 'be wiped out',
        WakeOnLan: 'WakeOnLan'
      };

      modal.confirm({
        title: 'Confirm',
        // icon: <ExclamationCircleOutlined />,
        content: `Are you sure you want to ${actionName[key] || key} ${asset.hostname}?`,
        okText: 'Yes',
        cancelText: 'Cancel',
        centered: true,
        confirmLoading: true,
        destroyOnClose: true,
        maskClosable: false,
        okType: 'default',
        zIndex: 99,

        onOk() {
          return executeActionApi(key, undefined, asset).then(() => {
            return message.success(`Endpoint will ${actionName[key]}`);
          });
        }
      });
    }
    if (key === 'delete') {
      return modal.confirm({
        title: 'Confirm',
        icon: <ExclamationCircleOutlined />,
        content: `Are you sure you want to delete ${asset.hostname}?`,
        okText: 'Yes',
        cancelText: 'Cancel',
        centered: true,
        confirmLoading: true,
        destroyOnClose: true,
        maskClosable: false,
        okType: 'danger',
        onOk() {
          return onDelete();
        }
      });
    }
    if (key === 'investigate') {
      onInvestigate();
    } else if (['rdp', 'terminal', 'fileexplorer'].includes(key)) {
      let fn =
        key === 'rdp'
          ? getRdpUrlForAssetApi
          : key === 'terminal'
          ? getTerminalUrlForAssetApi
          : getFileExplorerUrlForAssetApi;
      fn(asset.id).then((url) => {
        window.open(url, '_blank');
      });
    }
    if (key === 'action_history') {
      setIsDrawerOpen(true);
    }
  }

  return (
    <div className="flex flex-col min-h-0 flex-1">
      <Row gutter={16} className="h-full flex-1 min-h-0">
        <Col span={24} className="h-full flex flex-col my-2">
          <Row gutter={16}>
            <Col span={24}>
              <div className="pr-2">
                <div className="bg-seperator py-2 pl-2 rounded-lg mb-2 flex justify-between min-w-0">
                  <div className="flex-1 min-w-0">
                    <Tabs
                      activeKey={activeKey}
                      onChange={(key) => {
                        setActiveKey(key);
                        setSelectedCategory(null);
                      }}
                      items={tabs}
                      className="sticky-tabs transparent no-border no-margin"
                    />
                  </div>
                  <Dropdown
                    menu={{ items: actions, onClick: onMenuClick }}
                    trigger="click"
                    placement="bottomRight">
                    <Button
                      type="link"
                      className="flex-shrink-0"
                      onClick={(e) => e.preventDefault()}>
                      <span className="inline-flex items-center text-color">
                        <Icon name="settings_dropdown" className="mr-1 text-2xl" />
                        <Icon name="chevron-down" />
                      </span>
                    </Button>
                  </Dropdown>
                </div>
              </div>
            </Col>
          </Row>
          <div className="flex-1 flex flex-col min-h-0 overflow-y-auto pr-2">
            {activeKey === 'summary' ? null : <AssetInfoRibbon asset={asset} onChange={onChange} />}
            {tabs.find((tab) => tab.key === activeKey).component}
          </div>
        </Col>
      </Row>
      {contextHolder}

      <Drawer
        title="Action History"
        placement={'right'}
        onClose={() => {
          setIsDrawerOpen(false);
        }}
        destroyOnClose
        width="70%"
        maskClosable={false}
        open={isDrawerOpen}>
        <div className="flex flex-col min-h-0 flex-1 h-full">
          <CrudProvider
            disableExport
            disableColumnSelection
            columns={actionhistoryColumns}
            resourceTitle="Task"
            hasSearch
            fetchFn={(...args) => fetchActionHistoryApi(asset, ...args)}
            formFields={(item) => (
              <div className="py-4">
                <div style={{ whiteSpace: 'pre-line' }}>{item.result}</div>
              </div>
            )}
          />
        </div>
      </Drawer>
    </div>
  );
}
