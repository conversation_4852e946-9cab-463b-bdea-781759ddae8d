import Icon from '@/src/components/Icon';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import { Location } from '@/src/components/pickers/LocationPicker';
import { User } from '@/src/components/pickers/UserPicker';
import { Row, Col } from 'antd';

export default function AssetInfoRibbon({ asset, onChange }) {
  return (
    <Row gutter={16} className="mb-4">
      <Col span={24}>
        <Row gutter={16}>
          <Col
            span={12}
            className="flex flex-col justify-around border-border border-solid border-r border-l-0 border-t-0 border-b-0">
            <div className="bg-seperator rounded px-4 py-2 h-full flex flex-col justify-around">
              <Row gutter={16} className="mb-2">
                <Col span={12}>
                  <Icon name="settings" className="mr-2" />
                  Hardware Vendor
                </Col>
                <Col span={12} className="font-semibold">
                  {asset.hardwareModel}
                </Col>
              </Row>
              <Row gutter={16} className="mb-2">
                <Col span={12}>
                  <Icon name="os" className="mr-2" />
                  OS
                </Col>
                <Col span={12} className="font-semibold">
                  {asset.osName} {asset.systemInfo.osVersion}
                </Col>
              </Row>
              <Row gutter={16} className="mb-2">
                <Col span={12}>
                  <Icon name="agent_version" className="mr-2" />
                  Agent Version
                </Col>
                <Col span={12} className="font-semibold">
                  {asset.agentVersion}
                </Col>
              </Row>
              <Row gutter={16} className="mb-2">
                <Col span={12}>
                  <Icon name="last_seen" className="mr-2" />
                  Last Seen
                </Col>
                <Col span={12} className="font-semibold">
                  {asset.lastSeen}
                </Col>
              </Row>
            </div>
          </Col>
          <Col span={12}>
            <div className="bg-seperator rounded px-4 py-2 h-full flex flex-col justify-around">
              <Row gutter={16} className="mb-2">
                <Col span={12}>
                  <Icon name="host" className="mr-2" />
                  Host Name
                </Col>
                <Col span={12} className="font-semibold">
                  {asset.hostname}
                </Col>
              </Row>
              <Row gutter={16} className="mb-2 flex items-center">
                <Col span={12}>
                  <Icon name="user" className="mr-2" />
                  User
                </Col>
                <Col span={12} className="font-semibold">
                  <User.Picker
                    value={asset.owner}
                    onChange={(id) => onChange({ ...asset, owner: id })}
                    disabled
                    textOnly
                  />
                </Col>
              </Row>
              <Row gutter={16} className="mb-2 flex items-center">
                <Col span={12}>
                  <Icon name="department" className="mr-2" />
                  Department
                </Col>
                <Col span={12} className="font-semibold">
                  <Department.Picker
                    value={asset.department}
                    onChange={(id) => onChange({ ...asset, department: id })}
                    disabled
                    textOnly
                  />
                </Col>
              </Row>
              <Row gutter={16} className="mb-2 flex items-center">
                <Col span={12}>
                  <Icon name="location" className="mr-2" />
                  Location
                </Col>
                <Col span={12} className="font-semibold">
                  <Location.Picker
                    value={asset.location}
                    onChange={(id) => onChange({ ...asset, location: id })}
                    disabled
                    textOnly
                  />
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Col>
    </Row>
  );
}
