import { <PERSON><PERSON>Provider } from '@/src/hooks/crud';
import { Drawer, Tag } from 'antd';
import { Link } from 'react-router-dom';
import { getDiscoveredEndpointsForRuleApi } from '../api/software-metering';
import { useState } from 'react';
import SoftwareMeteringUsageDuration from './SoftwareMeteringUsageDuration';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';

export default function SoftwareMeteringDiscoveredInfo({ rule, onClose, timeline }) {
  const [activeItem, setActiveItem] = useState(null);
  const [appliedTimeline, setTimeline] = useState(timeline);
  const columns = [
    {
      title: 'Endpoint',
      dataIndex: 'asset_id',
      key: 'asset_id',
      render({ record }) {
        return <Link to={`/inventory/endpoints/${record.asset_id}`}>{record.asset}</Link>;
      }
    },
    {
      title: 'File Name',
      dataIndex: 'file_name',
      key: 'file_name'
    },
    {
      title: 'Used Count',
      dataIndex: 'usage_count',
      key: 'usage_count',
      align: 'center',
      render({ record }) {
        return (
          <Tag className="cursor-pointer" onClick={() => setActiveItem(record)}>
            {record.usage_count}
          </Tag>
        );
      }
    },
    {
      title: 'Duration',
      dataIndex: 'usage_duration',
      key: 'usage_duration',
      align: 'center',
      render({ record }) {
        return <Tag>{record.usage_duration}</Tag>;
      }
    }
  ];

  return (
    <Drawer
      title={
        <div className="flex justify-between items-center">
          <div className="flex-1 min-w-0 text-ellipsis">{rule.rule_name} Discovered</div>
        </div>
      }
      placement={'right'}
      width={'60%'}
      onClose={onClose}
      destroyOnClose
      maskClosable={false}
      open={true}>
      <CrudProvider
        columns={columns}
        key={`${JSON.stringify(appliedTimeline)}`}
        beforeCreateSlot={() => (
          <div className="mr-2">
            <TimelinePicker value={appliedTimeline} onChange={setTimeline} />
          </div>
        )}
        resourceTitle="Software Metering Summary Discovered"
        fetchFn={(...args) => getDiscoveredEndpointsForRuleApi(rule.rule_id, ...args, { timeline })}
      />
      {activeItem ? (
        <SoftwareMeteringUsageDuration
          timeline={appliedTimeline}
          rule={{ ...activeItem, rule_name: rule.rule_name }}
          onClose={() => setActiveItem(null)}
        />
      ) : null}
    </Drawer>
  );
}
