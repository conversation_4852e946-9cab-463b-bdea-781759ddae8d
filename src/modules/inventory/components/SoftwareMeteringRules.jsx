import { Switch, Button } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import constants from '@/src/constants/index';
import {
  createSoftwareMeteringRuleApi,
  deleteSoftwareMeteringRuleApi,
  getAllSoftwareMeteringRulesApi,
  updateSoftwareMeteringRuleApi
} from '../api/software-metering';
import Permission<PERSON>hecker from '@/src/components/PermissionChecker';
import SoftwareMeteringRuleForm from './SoftwareMeteringRuleForm';

export default function SoftwareMeteringRules() {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'rule_name',
      key: 'rule_name',
      type: 'view_link'
    },
    {
      title: 'File Name',
      dataIndex: 'file_name',
      key: 'file_name'
    },
    {
      title: 'Product Name',
      dataIndex: 'product_name',
      key: 'product_name'
    },
    {
      title: 'Comments',
      dataIndex: 'comments',
      key: 'comments',
      ellipsis: true
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render({ record, update }) {
        return (
          <Switch
            checked={record.status}
            onChange={(e) => {
              update({ ...record, status: e });
            }}
          />
        );
      },
      sortable: false
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Inventory],
      deletePermissions: [constants.Delete_Inventory]
    }
  ];

  return (
    <CrudProvider
      columns={columns}
      resourceTitle="Software Metering Rule"
      hasSearch
      defaultFormItem={{
        status: true
      }}
      createSlot={(createFn) => (
        <PermissionChecker permission={constants.Create_Inventory}>
          <Button type="primary" onClick={createFn}>
            Create
          </Button>
        </PermissionChecker>
      )}
      fetchFn={getAllSoftwareMeteringRulesApi}
      updateFn={updateSoftwareMeteringRuleApi}
      createFn={createSoftwareMeteringRuleApi}
      deleteFn={deleteSoftwareMeteringRuleApi}
      formFields={(item) => <SoftwareMeteringRuleForm item={item} />}
    />
  );
}
