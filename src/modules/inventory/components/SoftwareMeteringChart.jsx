import { Spin } from 'antd';
import { useEffect, useState } from 'react';
import { getTopTenUsageDurationApi, getUsageCountChartApi } from '../api/software-metering';
import Chart from '@/src/components/widget/views/chart/Chart';
import duration from '@/src/utils/duration';

export default function SoftwareMeteringChart({ assetId, ruleId, useCountChart, timeline }) {
  const [chart, setChart] = useState([]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    let p = useCountChart
      ? getUsageCountChartApi(assetId, ruleId, timeline)
      : getTopTenUsageDurationApi(assetId, timeline);
    p.then((result) => {
      setChart(result);
      setLoading(false);
    });
  }, [assetId, ruleId, timeline, useCountChart]);
  return (
    <div className="flex flex-col h-1/3 bg-seperator rounded">
      {loading ? (
        <div className="flex flex-col flex-1 justify-center items-center">
          <Spin spinning />
        </div>
      ) : (
        <Chart
          widget={{
            type: 'BarChart',
            'x-axis': useCountChart ? 'Date' : 'Rule',
            'y-axis': useCountChart ? 'Usage Count' : 'Usage',
            widgetProperties: {
              // colorPalette: 'palette-1',
              legendEnabled: true,
              ...(useCountChart
                ? {}
                : {
                    tooltipFormatter(value) {
                      return duration(value, undefined, true);
                    },
                    yAxisFormatter(value) {
                      return duration(
                        value,
                        undefined,
                        false,
                        value > 24 * 60 * 60 ? ['d'] : ['h', 'm']
                      );
                    }
                  })
            }
          }}
          data={{
            'x-axis': useCountChart ? 'Date' : 'Rule',
            'y-axis': useCountChart ? 'Usage Count' : 'Usage',
            result: chart
          }}
        />
      )}
    </div>
  );
}
