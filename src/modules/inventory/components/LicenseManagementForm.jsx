import { Form, Input, Col, Row, InputNumber, DatePicker } from 'antd';
import { LicenseEdition } from '@/src/components/pickers/LicenseEditionPicker';
import { Currency } from '@/src/components/pickers/CurrencyPicker';
import Uploader from '@/src/components/Uploader';
import { Asset } from '@/src/components/pickers/AssetPicker';
import { Software } from '@/src/components/pickers/SoftwarePicker';

export default function LicenseManagementForm({ item = {} }) {
  const form = Form.useFormInstance();
  const { allOptions } = Software.useSoftware();

  return (
    <>
      <Row gutter={32}>
        <Col span={12}>
          <Form.Item label="Software Name" name="software_id" rules={[{ required: true }]}>
            <Software.Picker
              placeholder="Select Software"
              onChange={(e) => {
                if (allOptions.has(e)) {
                  let selectedSoftware = allOptions.get(e);
                  form.setFieldValue('software_version', selectedSoftware.version);
                  form.setFieldValue('software_manufacturer', selectedSoftware.vendor);
                  form.setFieldValue('software_name', selectedSoftware.label);
                }
              }}
              disabled={item.id}
            />
          </Form.Item>
        </Col>
        {/* <Col span={12}>
          <Form.Item label="Software Id" name="software_id" >
            <InputNumber placeholder="Software Id" className="w-full" />
          </Form.Item>
        </Col> */}

        <Col span={12} style={{ visibility: 'hidden' }}>
          <Form.Item label="Software Name" name="software_name">
            <Input placeholder="Software Name" disabled={form.getFieldValue('software_name')} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Software Version" name="software_version">
            <Input
              placeholder="Software Version"
              disabled={form.getFieldValue('software_version')}
            />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item label="Software Manufacturer" name="software_manufacturer">
            <Input
              placeholder="Software Manufacturer"
              disabled={form.getFieldValue('software_manufacturer')}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="License Owner" name="license_owner">
            <Input placeholder="License Owner" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="License Edition" name="license_edition">
            <LicenseEdition.Picker
              value={item.edition_id}
              onChange={(edition) => {
                form.setFieldValue('edition_id', edition.value);
                form.setFieldValue('license_edition', edition.label);
              }}
              emitFullObject
            />
          </Form.Item>
        </Col>

        <Col span={0} style={{ visibility: 'hidden' }}>
          <Form.Item label="Software Name" name="edition_id">
            <Input placeholder="Software Name" disabled={form.getFieldValue('software_name')} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="License Name" name="license_name" rules={[{ required: true }]}>
            <Input placeholder="License Name" />
          </Form.Item>
        </Col>
        {/* <Col span={12}>
          <Form.Item label="Edition ID" name="edition_id">
            <InputNumber placeholder="Edition ID" className="w-full" />
          </Form.Item>
        </Col> */}
        <Col span={24}>
          <Form.Item label="License Key" name="license_key">
            <Input.TextArea placeholder="License Key" rows={5} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Cost" name="cost">
            <InputNumber placeholder="Cost" className="w-full" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Cost Currency" name="cost_currency">
            <Currency.Picker
              value={item.license_edition}
              onChange={(cost_currency) => form.setFieldValue(item, cost_currency)}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Purchase Date" name="purchase_date">
            <DatePicker placeholder="purchase Date" className="w-full" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Expiry Date" name="expiry_date">
            <DatePicker placeholder="Expiry Date" className="w-full" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Number Of License Purchased"
            name="no_of_license_purchased"
            rules={[{ required: true }]}>
            <InputNumber
              placeholder="Number Of License Purchased"
              precision={0}
              className="w-full"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Invoice Number" name="invoice_number">
            <Input placeholder="Invoice Number" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Po Number" name="po_number">
            <Input placeholder="Po Number" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="License Ref Number" name="license_ref_number">
            <Input placeholder="License Ref Number" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Vendor" name="vendor">
            <Input placeholder="Vendor" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Location" name="location">
            <Input placeholder="Location" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Licensed To" name="licensed_to">
            <Input placeholder="Licensed To" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Comments" name="comments">
            <Input.TextArea placeholder="Comments" rows={5} />
          </Form.Item>
        </Col>
        {/* {item.id ? null : (
          <> */}
        <Col span={24}>
          <Form.Item label="License File" name="license_file">
            <Uploader useDragger />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Invoice File" name="invoice_file">
            <Uploader useDragger accept="application/pdf" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Purchase Order" name="purchase_order_file">
            <Uploader useDragger accept="application/pdf" />
          </Form.Item>
        </Col>
        {/* </>
        )} */}
        <Col span={24}>
          <Form.Item name="assets" label="Endpoints">
            <Asset.Picker
              mode="multiple"
              value={item.assets}
              onChange={(assets) => form.setFieldValue(item, assets)}
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
}
