import { CrudProvider } from '@/src/hooks/crud';
import { Drawer } from 'antd';
import { getDiscoveredUsageForRuleApi } from '../api/software-metering';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';
import { useState } from 'react';
import SoftwareMeteringChart from './SoftwareMeteringChart';
import { useAuth } from '@/src/hooks/auth';

export default function SoftwareMeteringUsageDuration({ rule, timeline, onClose }) {
  const { formatDateTime } = useAuth();

  const [appliedTimeline, setTimeline] = useState(timeline);
  const columns = [
    {
      title: 'Software',
      dataIndex: 'software_name',
      key: 'software_name',
      sortable: false
    },
    {
      title: 'File Name',
      dataIndex: 'file_name',
      key: 'file_name',
      sortable: false
    },
    {
      title: 'PID',
      dataIndex: 'pid',
      key: 'pid',
      sortable: false
    },
    {
      title: 'Start Time',
      dataIndex: 'start_time',
      key: 'start_time',
      render({ record }) {
        return record.start_time ? formatDateTime(record.start_time * 1000) : '';
      }
    },
    {
      title: 'End Time',
      dataIndex: 'end_time',
      key: 'end_time',
      render({ record }) {
        return record.end_time ? formatDateTime(record.end_time * 1000) : '';
      }
    }
  ];

  return (
    <Drawer
      title={
        <div className="flex justify-between items-center">
          <div className="flex-1 min-w-0 text-ellipsis">
            {rule.rule_name} {rule.asset ? `Usage for ${rule.asset}` : ``}
          </div>
        </div>
      }
      placement={'right'}
      width={'70%'}
      onClose={onClose}
      destroyOnClose
      maskClosable={false}
      open={true}>
      <div className="flex flex-col min-h-0 flex-1 h-full">
        <SoftwareMeteringChart
          useCountChart
          ruleId={rule.rule_id}
          timeline={appliedTimeline}
          assetId={rule.asset_id}
        />
        <CrudProvider
          columns={columns}
          key={`${JSON.stringify(appliedTimeline)}`}
          beforeCreateSlot={() => (
            <div className="mr-2">
              <TimelinePicker value={appliedTimeline} onChange={setTimeline} />
            </div>
          )}
          resourceTitle="Software Metering Summary Discovered Usage"
          fetchFn={(...args) =>
            getDiscoveredUsageForRuleApi(rule.rule_id, rule.asset_id, ...args, {
              timeline: appliedTimeline
            })
          }
        />
      </div>
    </Drawer>
  );
}
