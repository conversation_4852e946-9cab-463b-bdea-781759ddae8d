import { Drawer } from 'antd';
import { useState } from 'react';
import { CrudProvider } from '@/src/hooks/crud';
import SeverityBorder from '@/src/components/SeverityBorder';
import VulnerabilityDetail from '@/src/modules/vulnerability/components/VulnerabilityDetail';
import { transform } from '@/src/modules/vulnerability/api/vulnerability-list';
import Severity from '@/src/components/Severity';
import { getVulnerabilitiesForSoftawreApi } from '../api/hosts';
import { VulnerabilityScore } from '@/src/components/Score';

export default function VulnerabilityDrawer({ software, closeDrawer, assetId }) {
  const [open, setOpen] = useState(true);
  const columns = [
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      render({ record }) {
        return <Severity severity={record.severity} useTag />;
      }
    },
    {
      title: 'CVE',
      dataIndex: 'cve',
      key: 'cve',
      type: 'view_link'
    },
    {
      title: 'Details',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      sortable: false
    },
    {
      title: 'Resolved Version',
      dataIndex: 'resolved_version',
      key: 'resolved_version',
      sortable: true,
      render({ record }) {
        return (
          <>
            {record.resolved_version && record.resolved_version !== 'null'
              ? record.resolved_version
              : ''}
          </>
        );
      }
    },

    {
      title: 'CVSS3 Score',
      dataIndex: 'cvss3_base_score',
      key: 'cvss3BaseScore',
      align: 'center',
      render({ record }) {
        return <VulnerabilityScore score={record.cvss3_base_score} />;
      }
    },
    {
      title: 'CVSS2 Score',
      dataIndex: 'cvss2_base_score',
      key: 'cvss2_base_score',
      align: 'center',
      render({ record }) {
        return <VulnerabilityScore score={record.cvss2_base_score} />;
      }
    },
    {
      title: 'CVSS3 Impact Score',
      dataIndex: 'cvss3_impact_score',
      key: 'cvss3_impact_score',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Attack Complexity',
      dataIndex: 'cvss3_attack_complexity',
      key: 'cvss3_attack_complexity',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Scope',
      dataIndex: 'cvss3_scope',
      key: 'cvss3_scope',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Confidentiality Impact',
      dataIndex: 'cvss3_confidentiality_impact',
      key: 'cvss3_confidentiality_impact',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Availability Impact',
      dataIndex: 'cvss3_availability_impact',
      key: 'cvss3_availability_impact',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Attack Vector',
      dataIndex: 'cvss3_attack_vector',
      key: 'cvss3_attack_vector',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Integrity Impact',
      dataIndex: 'cvss3_integrity_impact',
      key: 'cvss3_integrity_impact',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Privileges Required',
      dataIndex: 'cvss3_privileges_required',
      key: 'cvss3_privileges_required',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Exploitability Score',
      dataIndex: 'cvss3_exploitability_score',
      key: 'cvss3_exploitability_score',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Vector',
      dataIndex: 'cvss3_vector_string',
      key: 'cvss3_vector_string',
      hidden: true,
      sortable: false
    }
  ];
  return (
    <Drawer
      title={`Vulnerabilities for ${software.name}`}
      placement={'right'}
      width={'70%'}
      onClose={() => {
        setOpen(false);
        closeDrawer();
      }}
      destroyOnClose
      open={open}>
      <CrudProvider
        defaultPageSize={20}
        columns={columns}
        resourceTitle="Vulnerability"
        formDrawerWidth={'95%'}
        hasSearch
        fetchFn={(...args) => getVulnerabilitiesForSoftawreApi(software.id, ...args, assetId)}
        drawerTitle={(item) => (
          <SeverityBorder severity={item.cvss3_base_severity || item.severity}>
            <div className="flex justify-between">
              <div>{item.cve}</div>
            </div>
          </SeverityBorder>
        )}
        formFields={(item) => <VulnerabilityDetail vulnerability={transform(item)} />}
      />
    </Drawer>
  );
}
