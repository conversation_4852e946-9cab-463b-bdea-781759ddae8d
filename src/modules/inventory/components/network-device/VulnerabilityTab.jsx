import React from 'react';
import List from '@modules/vulnerability/views/List';

export default function VulnerabilityTab({ device, useExploit }) {
  return (
    <div className="flex flex-col flex-1 min-h-0 overflow-auto pt-2">
      <div className="flex min-h-0">
        <List
          parentPage={'network'}
          hideOverview={true}
          filters={{ id: device.id }}
          defaultExploit={useExploit}
        />
      </div>
    </div>
  );
}
