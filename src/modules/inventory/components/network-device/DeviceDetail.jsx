import Icon from '@/src/components/Icon';
import { Row, Col, Tabs } from 'antd';
import VulnerabilityTab from './VulnerabilityTab';
import { useState } from 'react';
import NetworkDeviceRibbon from './NetworkDeviceRibbon';
import InterfaceTab from './InterfaceTab';

export default function DeviceDetail({ device, onChange }) {
  const [activeKey, setActiveKey] = useState('vulnerabilities');
  const [defaultSelectedCategory, setDefaultSelectedCategory] = useState('All');
  const [useExploit, setUseExploit] = useState(false);
  const tabs = [
    {
      label: (
        <span className="flex items-center">
          <Icon name="vulnerability" className="text-lg" />
          Vulnerabilities
        </span>
      ),
      key: 'vulnerabilities',
      component: (
        <VulnerabilityTab
          key={`${useExploit}-${defaultSelectedCategory}`}
          useExploit={useExploit}
          device={device}
          defaultCategory={defaultSelectedCategory}
        />
      )
    },
    {
      label: (
        <span className="flex items-center">
          <Icon name="interface" className="text-lg" />
          Interfaces
        </span>
      ),
      key: 'interfaces',
      component: <InterfaceTab device={device} />
    }
  ];
  return (
    <div className="flex flex-col min-h-0 flex-1">
      <Row gutter={16} className="h-full flex-1 min-h-0">
        <Col span={24} className="h-full flex flex-col my-2">
          <Row gutter={16}>
            <Col span={24}>
              <div className="pr-2">
                <div className="bg-seperator py-2 pl-2 rounded-lg mb-2 flex justify-between min-w-0">
                  <div className="flex-1 min-w-0">
                    <Tabs
                      activeKey={activeKey}
                      onChange={(key) => {
                        setActiveKey(key);
                      }}
                      items={tabs}
                      className="sticky-tabs transparent no-border no-margin"
                    />
                  </div>
                </div>
              </div>
            </Col>
          </Row>
          <NetworkDeviceRibbon
            device={device}
            onChange={onChange}
            onCountClick={(tab) => {
              if (tab.subTab === 'exploit') {
                setUseExploit(true);
                setDefaultSelectedCategory('All');
              } else {
                setUseExploit(false);
                setDefaultSelectedCategory(tab.subTab);
              }
              setActiveKey(tab.tab);
            }}
          />
          <div className="flex-1 flex flex-col min-h-0 overflow-y-auto pr-2">
            {tabs.find((tab) => tab.key === activeKey).component}
          </div>
        </Col>
      </Row>
    </div>
  );
}
