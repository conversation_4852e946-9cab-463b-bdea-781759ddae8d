import { Crud<PERSON>rovider } from '@/src/hooks/crud';
import { getInterfacesForNetworkDeviceApi } from '../../api/network-devices';
import Status from '@/src/components/Status';

export default function InterfaceTab({ device }) {
  const columns = [
    {
      title: 'Interface Index',
      dataIndex: 'interface_index',
      key: 'interface_index'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Admin Status',
      dataIndex: 'admin_status',
      key: 'admin_status',
      align: 'center',
      render({ record }) {
        return <Status status={record.admin_status} useTag={true} />;
      }
    },
    {
      title: 'Operational State',
      dataIndex: 'operational_state',
      key: 'operational_state',
      align: 'center',
      render({ record }) {
        return <Status status={record.operational_state} useTag={true} />;
      }
    },
    {
      title: 'Type',
      dataIndex: 'interface_type',
      key: 'interface_type',
      align: 'center'
    },
    {
      title: '<PERSON><PERSON>',
      dataIndex: 'alias',
      key: 'alias',
      align: 'center'
    },
    {
      title: 'MAC Address',
      dataIndex: 'mac_address',
      key: 'mac_address',
      align: 'center'
    },
    {
      title: 'Speed',
      dataIndex: 'speed',
      key: 'speed',
      align: 'center',
      render({ record }) {
        return `${record.speed} Mbps`;
      }
    }
  ];

  return (
    <div className="flex flex-col min-h-0 flex-1 mt-2">
      <CrudProvider
        columns={columns}
        hasSearch
        resourceTitle="Interfaces"
        fetchFn={(...args) => getInterfacesForNetworkDeviceApi(...args, { id: device.id })}
      />
    </div>
  );
}
