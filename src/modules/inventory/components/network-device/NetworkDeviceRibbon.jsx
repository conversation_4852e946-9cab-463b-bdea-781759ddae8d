import React from 'react';
// import Moment from 'moment';

import { User } from '@components/pickers/UserPicker';
import { Department } from '@components/pickers/DepartmentPicker';
import { Location } from '@components/pickers/LocationPicker';

import { Row, Col, Divider } from 'antd';
import Icon from '@/src/components/Icon';
import SeverityDot from '@/src/components/SeverityDot';
import ScoreGauge from '@/src/components/ScoreGauge';

import LiveStatus from '@/src/components/LiveStatus';
import SystemLogo from '@/src/components/SystemLogo';
import Chart from '@/src/components/widget/views/chart/Chart';
import { severityColors } from '@/src/design/theme';
// import QRCodeGenerator from '@components/common/QRCodeGenerator';

function NetworkDeviceRibbon({ device = {}, onChange, onCountClick }) {
  const pieWidget = {
    type: 'PieChart',
    widgetProperties: {
      pieInnerSize: 30,
      opacity: 0.7
    }
  };

  const vulnerabilityData = {
    'x-axis': 'category',
    'y-axis': 'count',
    result: ['Critical', 'High', 'Medium', 'Low'].map((key) => ({
      category: key,
      count: device.vulnerability[key] || 0,
      color: severityColors[key.toLowerCase()]
    }))
  };

  return (
    <Row gutter={16} className="min-h-0">
      <Col span={24} className="h-full flex flex-col my-2">
        <div className="flex-1 flex flex-col min-h-0 overflow-y-auto pr-2">
          <Row gutter={8} className="pb-2 h-full">
            <Col span={10} className="h-full flex-1">
              <div className="flex flex-1 flex-col p-2 bg-seperator rounded-lg h-full">
                <div className="px-2 mb-1 flex justify-between">
                  <div className="w-16">
                    <SystemLogo name={device.vendor} className="w-full" type="vendor" />
                  </div>
                  <div className="inline-flex items-center justify-center">
                    <LiveStatus fillBg status={device.currentStatus} />
                  </div>
                </div>
                <div className="px-2 text-sm font-bold">{device.deviceModel}</div>
                {/* <div className="px-2 text-xs inline-flex items-center">UUID: {device.uuid}</div> */}
                <Divider className="my-2" />
                <div className="flex flex-1 flex-col justify-evenly">
                  <Row gutter={8} className="flex-1">
                    <Col span={8}>
                      <div className="flex justify-center flex-col h-full">
                        <div className="text-xs inline-flex items-center text-neutral-light">
                          Host
                        </div>
                        <div className="font-bold text-base">{device.hostname}</div>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div className="flex justify-center flex-col h-full">
                        <div className="text-xs inline-flex items-center text-neutral-light">
                          Firmware
                        </div>
                        <div
                          className="font-bold text-base text-ellipsis"
                          title={device.firmware_name || '---'}>
                          {device.firmware_name || '---'}
                        </div>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div className="flex justify-center flex-col h-full">
                        <div className="text-xs inline-flex items-center text-neutral-light">
                          Firmware Version
                        </div>
                        <div
                          className="font-bold text-base text-ellipsis"
                          title={device.firmware_version || '---'}>
                          {device.firmware_version || '---'}
                        </div>
                      </div>
                    </Col>
                  </Row>
                  <Row gutter={8} className="flex-1">
                    <Col span={8}>
                      <div className="flex justify-center flex-col h-full">
                        <div className="text-xs inline-flex items-center text-neutral-light">
                          <Icon name="department" className="mr-1" />
                          Department
                        </div>
                        <div className="font-bold text-base">
                          <Department.Picker
                            value={device.department}
                            onChange={(id) => onChange({ ...device, department: id })}
                          />
                        </div>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div className="flex justify-center flex-col h-full">
                        <div className="text-xs inline-flex items-center text-neutral-light">
                          <Icon name="user" className="mr-1" />
                          User
                        </div>
                        <div className="font-bold text-base">
                          <User.Picker
                            value={device.owner}
                            onChange={(id) => onChange({ ...device, owner: id })}
                          />
                        </div>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div className="flex justify-center flex-col h-full">
                        <div className="text-xs inline-flex items-center text-neutral-light">
                          <Icon name="location" className="mr-1" />
                          Location
                        </div>
                        <div className="font-bold text-base">
                          <Location.Picker
                            value={device.location}
                            onChange={(id) => onChange({ ...device, location: id })}
                          />
                        </div>
                      </div>
                    </Col>
                    {/* <Col span={8}>
                      <div className="h-full flex flex-col justify-end">
                        <div className="text-xs inline-flex items-center text-neutral-light">
                          <Icon name="agent_version" className="mr-1" />
                          Agent Version
                        </div>
                        <div className="font-bold text-base">{device.agentVersion}</div>
                      </div>
                    </Col>
                    <Col span={16}>
                      <div className="h-full flex flex-col justify-end">
                        <div className="text-xs inline-flex items-center text-neutral-light">
                          <Icon name="last_seen" className="mr-1" />
                          Last Seen
                        </div>
                        <div className="font-bold text-base">{device.lastSeen}</div>
                      </div>
                    </Col> */}
                  </Row>
                </div>
              </div>
            </Col>

            <Col span={9} className="h-full flex-1">
              <div className="flex flex-col flex-1 h-full">
                <div className="p-2 bg-seperator rounded-lg h-full flex flex-col">
                  <div className="font-semibold text-xs mb-1">Vulnerabilities</div>
                  <div
                    className="flex justify-between items-center min-h-0 flex-1"
                    style={
                      Object.keys(device.vulnerability).length === 0
                        ? {
                            filter: `grayscale(1)`
                          }
                        : {}
                    }>
                    <div style={{ height: '100px', width: '100px' }}>
                      <Chart
                        widget={pieWidget}
                        data={vulnerabilityData}
                        style={{ height: '100px', width: '100px' }}
                      />
                    </div>
                    <div
                      className="flex-1 flex flex-wrap justify-items-stretch items-center"
                      style={{ height: '100px' }}>
                      {['Critical', 'High', 'Medium', 'Low'].map((key) => (
                        <div key={key} className={`flex flex-col w-1/2 text-center`}>
                          <div className="flex items-center justify-center">
                            <div className={`mr-1`}>
                              <SeverityDot severity={key} />
                            </div>
                            <div className="text-xs">{key}</div>
                          </div>
                          <div
                            className={`font-semibold text-lg text ${key.toLowerCase()} cursor-pointer`}
                            onClick={() => {
                              if (device.vulnerability[key]) {
                                onCountClick({
                                  tab: 'vulnerabilities',
                                  subTab: key
                                });
                              }
                            }}>
                            {device.vulnerability[key] || 0}
                          </div>
                        </div>
                      ))}
                    </div>

                    <div
                      className="flex flex-col  text-center border-left px-4 h-full items-center justify-around cursor-pointer"
                      onClick={() =>
                        onCountClick({
                          tab: 'vulnerabilities',
                          subTab: 'exploit'
                        })
                      }>
                      Exploitable CVE
                      <div className="text-5xl mb-2 text critical">
                        {device.vulnerability.exploit_count}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Col>
            <Col span={5} className="h-full flex-1">
              <div className="p-2 bg-seperator rounded-lg h-full flex flex-col min-h-0">
                <div className="flex flex-col flex-1 items-center justify-center">
                  <Row className="w-full flex-1">
                    <Col span={12} className="h-full flex items-center">
                      <ScoreGauge value={device.riskScore} useReverseColor />
                    </Col>
                    <Col span={12} className="flex flex-col justify-center">
                      <h2 className="m-0">EndPoint</h2>
                      <h1 className="m-0">Risk Score</h1>
                    </Col>
                  </Row>
                </div>
              </div>
            </Col>

            {/* <Col span={3} className="h-full flex-1">
              <div className="flex flex-col flex-1 h-full">
                <div className="p-2 bg-seperator rounded-lg h-full flex flex-col min-h-0">
                  <QRCodeGenerator
                    text={`${window.location.protocol}//${window.location.hostname}${window.location.pathname}${window.location.search}${window.location.hash}`}
                  />
                </div>
              </div>
            </Col> */}
          </Row>
        </div>
      </Col>
    </Row>
  );
}

export default NetworkDeviceRibbon;
