import { useEffect, useState } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { Tree, Badge, Divider } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import {
  getHierarchyForInventoryApi,
  getLocationDepartmentHierarchyForInventoryApi
} from '../api/hosts';
import Icon from '@/src/components/Icon';

export default function InventoryHierarchy({ onSelect, selectedPlatform }) {
  const [hierarchy, setHierarchy] = useState([]);
  const [departmentHierarchy, setDepartmentHierarchy] = useState([]);
  const [locationHierarchy, setLocationHierarchy] = useState([]);
  const route = useLocation();
  const navigate = useNavigate();
  const [queryParams] = useSearchParams();
  const pathParts = route.pathname.replace(/\d+$/, '').split('/').filter(Boolean);
  const isAsset = pathParts[pathParts.length - 1] === 'endpoints';
  const isNetworkDevice = pathParts[pathParts.length - 1] === 'network-devices';
  const categoryMap = {
    endpoints: 'asset',
    softwares: 'package/packages',
    browsers: 'package/browsers'
  };
  useEffect(() => {
    const pathParts = route.pathname.replace(/\d+$/, '').split('/').filter(Boolean);
    if (categoryMap[pathParts[pathParts.length - 1]]) {
      getHierarchyForInventoryApi(categoryMap[pathParts[pathParts.length - 1]]).then((data) => {
        setHierarchy(data);
        if (queryParams.get('platform')) {
          let platform = queryParams.get('platform');
          let item = data.find((i) => i.key === platform);
          if (item) {
            onSelect(item);
          }
        }
      });
    }
    // eslint-disable-next-line
  }, [route, queryParams]);

  useEffect(() => {
    const pathParts = route.pathname.replace(/\d+$/, '').split('/').filter(Boolean);

    const isNetworkDevice = pathParts[pathParts.length - 1] === 'network-devices';

    if (categoryMap[pathParts[pathParts.length - 1]] === 'asset' || isNetworkDevice) {
      getLocationDepartmentHierarchyForInventoryApi(selectedPlatform, isNetworkDevice).then(
        (data) => {
          setDepartmentHierarchy(data.filter((i) => i.type === 'department'));
          setLocationHierarchy(data.filter((i) => i.type === 'location'));
        }
      );
    }
    // eslint-disable-next-line
  }, [route, selectedPlatform]);

  function onSelectItem(data, e) {
    onSelect(e.node);
    const paths = route.pathname
      .split('/')
      .filter((i) => !/^\d+$/.test(i))
      .join('/');
    navigate(paths);
  }

  return (
    <div className="mt-6">
      {!isNetworkDevice ? (
        <Tree
          className="inventory-tree my-2"
          blockNode
          selectedKeys={selectedPlatform ? [selectedPlatform.key] : ['all']}
          autoExpandParent
          switcherIcon={<DownOutlined />}
          titleRender={(item) => {
            return (
              <div
                className="flex items-center text-label flex-1 justify-between"
                style={{ whiteSpace: 'nowrap' }}>
                <div>
                  {item.type === 'os' && (
                    <Icon name={`platform_${item.key.toLowerCase()}`} className="mr-2 text-lg" />
                  )}
                  {item.title}{' '}
                </div>
                <Badge
                  showZero
                  overflowCount={9999999999}
                  className="ml-2 count-badge"
                  count={item.count}
                />
              </div>
            );
          }}
          onSelect={onSelectItem}
          treeData={hierarchy}
        />
      ) : null}

      {isAsset || isNetworkDevice ? (
        <>
          {!isNetworkDevice ? <Divider /> : null}
          {locationHierarchy.length ? (
            <>
              <Tree
                className="inventory-tree my-2"
                blockNode
                defaultExpandedKeys={['location']}
                autoExpandParent
                switcherIcon={<DownOutlined />}
                titleRender={(item) => {
                  return (
                    <div
                      className="flex items-center text-label flex-1 justify-between"
                      style={{ whiteSpace: 'nowrap' }}>
                      <div>{item.title}</div>
                      <Badge
                        overflowCount={9999999999}
                        showZero
                        className="ml-2 count-badge"
                        count={item.count}
                      />
                    </div>
                  );
                }}
                onSelect={onSelectItem}
                treeData={locationHierarchy}
              />
              <Divider />
            </>
          ) : null}
          {departmentHierarchy.length ? (
            <Tree
              className="inventory-tree my-2"
              blockNode
              defaultExpandedKeys={['department']}
              autoExpandParent
              switcherIcon={<DownOutlined />}
              titleRender={(item) => {
                return (
                  <div
                    className="flex items-center text-label flex-1 justify-between"
                    style={{ whiteSpace: 'nowrap' }}>
                    <div>{item.title}</div>
                    <Badge
                      overflowCount={9999999999}
                      className="ml-2 count-badge"
                      count={item.count}
                      showZero
                    />
                  </div>
                );
              }}
              onSelect={onSelectItem}
              treeData={departmentHierarchy}
            />
          ) : null}
        </>
      ) : null}
    </div>
  );
}
