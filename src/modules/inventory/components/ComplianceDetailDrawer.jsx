import { Row, Col, Divider, Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
// import { getInventoryHostByIdApi } from '../api/hosts';
import { CrudProvider } from '@/src/hooks/crud';
import Status from '@/src/components/Status';
import exportScrollableElement from '@/src/utils/scrollable-capture';
import ScoreGauge from '@/src/components/ScoreGauge';
import PackageLogo from '../../device-automation/components/PackageLogo';
import Icon from '@/src/components/Icon';

export default function ComplianceDetail({ asset, assesment }) {
  const rulesColumns = [
    {
      title: 'Rule Name',
      dataIndex: 'name',
      key: 'name',
      sortable: false,
      width: '50%'
    },
    {
      title: 'Bindings',
      dataIndex: 'bindings',
      key: 'bindings',
      sortable: false
    },
    {
      title: 'Rule Type',
      dataIndex: 'ruleType',
      key: 'ruleType',
      sortable: false,
      render({ record }) {
        return record.ruleType.toUpperCase();
      }
    },
    {
      title: 'Impact',
      dataIndex: 'impact',
      key: 'impact',
      sortable: false,
      render({ record }) {
        return (record.impact || '').toUpperCase();
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      sortable: false,
      align: 'center',
      render({ record }) {
        return (
          <div className="flex justify-center">
            <Status
              color={
                record.status === 'success'
                  ? 'success'
                  : record.status === 'failed'
                  ? 'error'
                  : 'processing'
              }
              status={record.status || ''}
              useTag={true}
              useIcon={true}
            />
          </div>
        );
      }
    }
  ];

  async function downloadPdf() {
    document.querySelectorAll('.ant-float-btn-circle').forEach((i) => {
      i.style.display = 'none';
    });
    await exportScrollableElement(document.querySelector('.ant-drawer-body'), assesment.name);
    document.querySelectorAll('.ant-float-btn-circle').forEach((i) => {
      i.style.display = 'block';
    });
  }

  function getFormatedResult(result) {
    if (result) {
      return atob(result);
    } else {
      return '';
    }
  }

  return (
    <div className="flex flex-col min-h-0 h-full bg-page-background">
      <Row gutter={32} className="items-center" type="flex">
        <Col
          span={8}
          className="flex flex-col border-solid border-r-1 border-border border-l-0 border-t-0 border-b-0 h-full">
          <div className="flex flex-col justify-center flex-1 items-center">
            <div className="flex flex-col justify-center flex-1">
              <table>
                <tr>
                  <td className="border-solid border-b border-border py-2 border-t-0 border-l-0 border-r-0">
                    Host
                  </td>
                  <td className="border-solid border-b border-border py-2 border-t-0 border-l-0 border-r-0">
                    <span className="font-semibold">{asset.hostname}</span>
                  </td>
                </tr>
                <tr>
                  <td className="border-solid border-b border-border py-2 border-t-0 border-l-0 border-r-0">
                    OS
                  </td>
                  <td className="border-solid border-b border-border py-2 border-t-0 border-l-0 border-r-0">
                    <span className="font-semibold">
                      {asset.systemInfo.os} {asset.systemInfo.osVersion}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="border-solid border-b border-border py-2 border-t-0 border-l-0 border-r-0">
                    Hardware Vendor
                  </td>
                  <td className="border-solid border-b border-border py-2 border-t-0 border-l-0 border-r-0">
                    <span className="font-semibold">{asset.hardwareModel}</span>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </Col>
        <Col span={8} className="flex flex-col h-full">
          <div className="flex flex-col">
            <div className="flex items-center flex-wrap">
              <PackageLogo
                disabled
                package={assesment}
                style={{ width: '50px' }}
                className="mr-2"
              />
              <h4>{assesment.name}</h4>
            </div>
            <div className="mt-4">{assesment.description}</div>
          </div>
        </Col>
        <Col
          span={8}
          className="flex flex-col border-solid border-l-1 border-border border-r-0 border-t-0 border-b-0 h-full">
          <div className="flex flex-col items-center">
            <ScoreGauge
              style={{ width: 150 }}
              size={150}
              value={
                assesment.testedRules === 0
                  ? 0
                  : Math.ceil((assesment.passedRules * 100) / assesment.testedRules)
              }
            />
          </div>
        </Col>
      </Row>
      <Divider />
      <Row className="flex flex-col min-h-0 flex-1">
        <Col span={24}>
          <div className="-mx-2 flex flex-col flex-1 min-h-0 h-full">
            <CrudProvider
              columns={rulesColumns}
              disableRefresh
              appendColumns={[
                {
                  title: '',
                  width: '40px',
                  dataIndex: 'result',
                  key: 'result',
                  ellipsis: true,
                  sortable: false,
                  render({ record, view }) {
                    const hasLink = ['failed'].includes(record.status);
                    return (
                      <div className="flex items-center">
                        {hasLink ? (
                          <Button
                            shape="circle"
                            type="link"
                            onClick={() => view(record)}
                            className="mr-2"
                            title="View Task">
                            <Icon
                              name="eye"
                              style={{ fontSize: '1.1rem' }}
                              className="text-primary"
                            />
                          </Button>
                        ) : null}
                      </div>
                    );
                  }
                }
              ]}
              // key={renderKey}
              createContainerClasses="flex-1"
              beforeCreateSlot={(_, { data }) => (
                <div className="flex items-center justify-between flex-1">
                  <div className="">
                    <h3 className="mb-0 text-primary text-right">
                      Rules Status:&nbsp;
                      {assesment.passedRules}/{assesment.testedRules}
                    </h3>
                  </div>
                  <div className="text-right mx-4 flex items-center">
                    <Button
                      type="primary"
                      className="ml-2 -mr-2"
                      ghost
                      onClick={() => downloadPdf(data)}>
                      <DownloadOutlined /> Download
                    </Button>
                    {/* <TimelinePicker value={currentTimeline} onChange={handleChangeTimeline} /> */}
                  </div>
                </div>
              )}
              disableColumnSelection
              disablePagination
              drawerTitle={(item) => `Result of ${item.name}`}
              formFields={(item) => (
                <div className="py-4">
                  <div style={{ whiteSpace: 'pre-line' }}>{getFormatedResult(item.result)}</div>
                </div>
              )}
              fetchFn={(offset, size) =>
                Promise.resolve({
                  totalCount: assesment.rules.length,
                  result: assesment.rules.slice(offset, offset + size)
                })
              }
            />
          </div>
        </Col>
      </Row>
    </div>
  );
}
