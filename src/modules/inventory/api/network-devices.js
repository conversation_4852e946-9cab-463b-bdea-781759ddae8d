import api from '@api';
import { transformAssetSummary } from '../api/hosts';

const END_POINT = `/inventory/device`;
export function transform(item) {
  return {
    ...item,
    id: item.id,
    hostname: item.host_name,
    vendor: item.vendor,
    risk: item.risk,
    owner: item.users,
    department: item.department,
    location: item.location,
    version: item.version,
    currentStatus: item.current_status,
    createdAt: item.created_time,
    hardwareModel: item.hardware_model,
    status: item.status,
    eol_data: null,
    totalSeconds: item.total_seconds,
    riskScore: item.risk_score,
    name: item.host_name
  };
}

const sortKeyMap = {
  hostname: 'host_name',
  vendor: 'vendor',
  hardwareModel: 'hardware_model',
  riskScore: 'risk_score',
  owner: 'users',
  createdAt: 'created_time'
};

const searchableColumns = ['host_name', 'vendor', 'hardware_model', 'risk_score'];

export function getNetworkDevicesApi(
  offset,
  size,
  sortFilter,
  filter,
  locationFilter,
  departmentFilter
) {
  return api
    .post(`${END_POINT}`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {
            // sortBy: 'current_status'
          }),
      ...(filter && filter.key !== 'all'
        ? {
            // ...(filter.type === 'version'
            //   ? {
            //       version: filter.title,
            //       os: filter.platform
            //     }
            //   : {
            //       os: filter.children.map((item) => item.platform).join(',')
            //     })
          }
        : {}),
      ...(locationFilter && locationFilter.key !== 'all' && locationFilter.key !== 'location'
        ? {
            location: locationFilter.key
          }
        : {}),
      ...(departmentFilter &&
      departmentFilter.key !== 'all' &&
      departmentFilter.key !== 'department'
        ? {
            department: departmentFilter.key
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function deleteNetworkDeviceApi(data) {
  return api.delete(`${END_POINT}/${data.id}`);
}

function transformForServer(item) {
  return {
    id: item.id,
    users: item.owner,
    department: item.department,
    location: item.location
  };
}

export function updateInventoryNetworkDeviceApi(data) {
  return api.put(`${END_POINT}/${data.id}`, transformForServer(data)).then(() => data);
}

export function getNetworkDeviceSummaryApi(id) {
  return api.get(`${END_POINT}/summary/${id}`).then(({ result }) =>
    transformAssetSummary({
      ...result,
      id,
      departments: result.department,
      hostname: result.host_name,
      hardware_vendor: result.vendor
    })
  );
}

export function getNetworkDeviceVulnerabilityCountApi(id) {
  return api.get(`/network/vulnerability/severities?ID=${id}`).then((data) => {
    const total = Object.keys(data.result || {})
      .filter((key) => key !== 'total_count')
      .reduce((total, key) => total + data.result[key], 0);
    return [{ severity: 'All', count: total }].concat(
      Object.keys(data.result || {})
        .filter((key) => key !== 'total_count')
        .map((key) => ({
          severity: key,
          count: data.result[key]
        }))
    );
  });
}

export function networkDeviceVulnerabilityScanApi(assetId) {
  return api.get(`/network/vulnerability/scan/${assetId ? assetId : -1}`);
}

const interface_search_columns = [
  'interface_index',
  'description',
  'admin_status',
  'operational_state',
  'interface_type',
  'alias',
  'mac_address'
];
export function getInterfacesForNetworkDeviceApi(offset, size, sortFilter, filter) {
  return api
    .post(`/inventory/device/interfaces/${filter.id} `, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? interface_search_columns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result
      };
    });
}
