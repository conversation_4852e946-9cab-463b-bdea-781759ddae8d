import {
  transformAssetScope,
  transformAssetScopeForServer
} from '@/src/components/pickers/AssetScopePicker';
import generateId from '@/src/utils/id';
import api from '@api';
import { transformTimelineForServer } from '../../dashboard/widget-api';

const END_POINT = `/software/meter`;

const transform = (item) => ({
  id: item.id,
  rule_name: item.rule_name,
  software_id: item.software_id,
  file_name: item.file_name,
  original_file_name: item.original_file_name,
  product_name: item.product_name,
  file_version: item.file_version,
  comments: item.comments,
  status: <PERSON><PERSON>an(item.status),
  is_licensed: <PERSON>olean(item.is_licensed),
  ...transformAssetScope(item)
});

const transformForServer = (item) => ({
  id: item.id,
  rule_name: item.rule_name,
  software_id: item.software_id,
  file_name: item.file_name,
  original_file_name: item.original_file_name,
  product_name: item.product_name,
  file_version: item.file_version,
  comments: item.comments,
  ...transformAssetScopeForServer(item),
  status: item.status ? 1 : 0,
  is_licensed: item.is_licensed ? 1 : 0
});

const sortKeyMap = {};

const searchableColumns = [
  'rule_name',
  'file_name',
  'original_file_name',
  'product_name',
  'file_version',
  'comments'
];

export function getAllSoftwareMeteringRulesApi(offset, size, sortFilter = {}) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getSoftwareMeteringRuleApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateSoftwareMeteringRuleApi(item) {
  return api
    .put(`${END_POINT}/${item.id}`, transformForServer(item))
    .then((data) => getSoftwareMeteringRuleApi(data.result));
}

export function createSoftwareMeteringRuleApi(item) {
  return api
    .post(`${END_POINT}`, transformForServer(item))
    .then((data) => getSoftwareMeteringRuleApi(data.result));
}

export function deleteSoftwareMeteringRuleApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}

export function getEligibleSoftwaresApi(offset, size, sortFilter, scope) {
  return api
    .post(`/inventory/package/packages/list`, {
      offset,
      size,
      ...transformAssetScopeForServer({ scope }),
      qualification: sortFilter.searchTerm
        ? ['name'].map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then(({ result, totalCount }) => {
      return {
        totalCount,
        result
      };
    });
}

const summarySearchableColumns = ['rule_name', 'file_name'];
export function getSoftwareMeteringSummaryApi(
  assetId,
  offset,
  size,
  sortFilter = {},
  { timeline }
) {
  return api
    .post(assetId ? `/inventory/asset/software/meter/${assetId}` : `/software/meter/summary`, {
      offset,
      size,
      ...((timeline || {}).selected
        ? {
            timeline: transformTimelineForServer(timeline)
          }
        : {}),
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? summarySearchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map((i) => ({
          id: generateId(),
          rule_id: i.rule_id,
          rule_name: i.rule_name,
          file_name: i.file_name,
          usage_count: i.usage_count,
          discovered_count: i.discovered_count,
          usage_duration: i.usage_duration,
          is_licensed: i.is_licensed
        }))
      };
    });
}

export function getDiscoveredEndpointsForRuleApi(
  ruleId,
  offset,
  size,
  sortFilter = {},
  { timeline }
) {
  return api
    .post(`/software/meter/discovered`, {
      ruleId,
      offset,
      size,
      ...((timeline || {}).selected
        ? {
            timeline: transformTimelineForServer(timeline)
          }
        : {})
    })
    .then((data) => ({
      totalCount: data.totalCount,
      result: data.result.map((i) => ({
        id: generateId(),
        rule_id: i.rule_id,
        asset: i.asset,
        usage_count: i.usage_count,
        asset_id: i.asset_id,
        file_name: i.file_name,
        usage_duration: i.usage_duration
      }))
    }));
}

export function getDiscoveredUsageForRuleApi(
  ruleId,
  assetId,
  offset,
  size,
  sortFilter = {},
  { timeline }
) {
  return api
    .post(`/software/meter/usage`, {
      ruleId,
      assetId,
      offset,
      size,
      ...((timeline || {}).selected
        ? {
            timeline: transformTimelineForServer(timeline)
          }
        : {}),
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${sortFilter.sort.field}`
          }
        : {})
    })
    .then((data) => ({
      totalCount: data.totalCount,
      result: data.result.map((i) => ({
        id: generateId(),
        rule_name: i.rule_name,
        software_name: i.software_name,
        file_name: i.file_name,
        pid: i.pid,
        start_time: i.start_time,
        end_time: i.end_time
      }))
    }));
}

export function getTopTenUsageDurationApi(assetId, timeline) {
  return api
    .post(
      assetId ? `/inventory/asset/software/meter/top/${assetId}` : `/software/meter/summary/top`,
      {
        ...(timeline ? { timeline: transformTimelineForServer(timeline) } : {})
      }
    )
    .then(({ result }) => result.map((r) => ({ Rule: r.rule_name, Usage: r.usage_duration })));
}

export function getUsageCountChartApi(assetId, ruleId, timeline) {
  return api
    .post(`/software/meter/usage/count`, {
      ruleId,
      assetId,
      ...(timeline ? { timeline: transformTimelineForServer(timeline) } : {})
    })
    .then(({ result }) => result.map((r) => ({ Date: r['Event Date'], 'Usage Count': r.Count })));
}
