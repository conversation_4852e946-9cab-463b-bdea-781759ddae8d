import api from '@api';
import Dayjs from 'dayjs';
import Moment from 'moment';
import OmitBy from 'lodash/omitBy';

export const FORMAT = 'YYYY-MM-DD';

const END_POINT = `/software/license`;

const sortKeyMap = {
  software_name: 'software_name',
  software_manufacturer: 'software_manufacturer',
  software_version: 'software_version',
  license_owner: 'license_owner',
  compliance_status: 'compliance_status',
  total_purchased: 'total_purchased',
  managed_installations: 'managed_installations',
  remaining: 'remaining'
};

const searchableColumns = [
  'software_name',
  'software_manufacturer',
  'software_version',
  'license_owner',
  'compliance_status'
];

function transform(item) {
  return {
    id: item.id,
    software_name: item.software_name,
    software_id: item.software_id,
    software_manufacturer:
      item.software_manufacturer === 'null' ? undefined : item.software_manufacturer,
    software_version: item.software_version,
    license_owner: item.license_owner,
    total_purchased: item.total_purchased,
    managed_installations: item.managed_installations,
    remaining: item.remaining,
    compliance_status: item.compliance_status,
    created_time: item.created_time,
    modified_time: item.modified_time,
    archived: item.archived,

    ...(item.license_details
      ? {
          license_details: (item.license_details || []).map((i) => {
            return transformSingleLicense(i, item);
          })
        }
      : {}),

    ...(item.license_details && item.license_details.length
      ? transformSingleLicense(item.license_details[0], item)
      : {})
  };
}

function transformSingleLicense(item, parentLicense) {
  return {
    parentId: parentLicense.id,
    id: item.id,
    software_name: parentLicense.software_name,
    software_id: parentLicense.software_id,
    software_version: parentLicense.software_version,
    software_manufacturer:
      parentLicense.software_manufacturer === 'null'
        ? undefined
        : parentLicense.software_manufacturer,
    license_id: item.license_id,
    license_name: item.license_name,
    license_edition: item.license_edition,
    license_owner: parentLicense.license_owner,

    edition_id: item.edition_id,
    license_key: item.license_key,
    cost: item.cost,
    cost_currency: item.cost_currency,
    no_of_license_purchased: item.no_of_license_purchased,
    purchase_date: item.purchase_date
      ? Dayjs(Moment.unix(item.purchase_date).format(FORMAT), FORMAT)
      : undefined,
    expiry_date: item.expiry_date
      ? Dayjs(Moment.unix(item.expiry_date).format(FORMAT), FORMAT)
      : undefined,

    purchase_date_ms: item.purchase_date
      ? Moment.unix(item.purchase_date).format(FORMAT)
      : undefined,
    expiry_date_ms: item.expiry_date ? Moment.unix(item.expiry_date).format(FORMAT) : undefined,

    invoice_number: item.invoice_number,
    po_number: item.po_number,
    license_ref_number: item.license_ref_number,
    vendor: item.vendor,
    location: item.location,
    licensed_to: item.licensed_to,
    comments: item.comments,
    assets: (item.assets || '')
      .split(',')
      .map((i) => +i)
      .filter(Boolean),
    created_time: item.created_time,
    modified_time: item.modified_time,
    isChildLicense: true,

    ...(item.license_file_ref
      ? {
          license_file: [
            {
              ref: item.license_file_ref,
              name: item.license_file
            }
          ]
        }
      : {}),

    ...(item.invoice_file_ref
      ? {
          invoice_file: [
            {
              ref: item.invoice_file_ref,
              name: item.invoice_file
            }
          ]
        }
      : {}),

    ...(item.purchase_order_ref
      ? {
          purchase_order_file: [
            {
              ref: item.purchase_order_ref,
              name: item.purchase_order
            }
          ]
        }
      : {})
  };
}

// function transform(item) {
//   return {
//     id: item.id,
//     software_name: item.software_name,
//     software_id: item.software_id,
//     software_version: item.software_version,
//     software_manufacturer: item.software_manufacturer === 'null' ? '' : item.software_manufacturer,
//     software_owner: item.software_owner,
//     license_edition: item.license_edition,
//     license_name: item.license_name,
//     edition_id: item.edition_id,
//     license_key: item.license_key,
//     cost: item.cost,
//     cost_currency: item.cost_currency,
//     no_of_license_purchased: item.no_of_license_purchased,
//     purchase_date: item.purchase_date
//       ? Dayjs(Moment.unix(item.purchase_date).format(FORMAT), FORMAT)
//       : undefined,
//     expiry_date: item.expiry_date
//       ? Dayjs(Moment.unix(item.expiry_date).format(FORMAT), FORMAT)
//       : undefined,
//     invoice_number: item.invoice_number,
//     po_number: item.po_number,
//     license_ref_number: item.license_ref_number,
//     vendor: item.vendor,
//     location: item.location,
//     licensed_to: item.licensed_to,
//     comments: item.comments,
//     license_owner: item.license_owner === 'null' ? '' : item.license_owner,
//     total_purchased: item.total_purchased,
//     managed_installation: item.managed_installation,
//     remaining: item.remaining,
//     compliance_status: item.compliance_status,
//     created_time: item.created_time,
//     modified_time: item.modified_time,
//     achived: item.achived,
//     assets: (item.assets || '')
//       .split(',')
//       .map((i) => +i)
//       .filter(Boolean),
//     managed_installations: item.managed_installations,
//     ...(item.license_details
//       ? {
//           license_details: (item.license_details || []).map((i) => ({
//             parentId: item.id,
//             id: i.id,
//             software_name: item.software_name,
//             software_id: item.software_id,
//             software_version: item.software_version,
//             software_manufacturer: item.software_manufacturer,
//             license_id: i.license_id,
//             license_name: i.license_name,
//             license_edition: i.license_edition,
//             edition_id: i.edition_id,
//             license_key: i.license_key,
//             cost: i.cost,
//             cost_currency: i.cost_currency,
//             no_of_license_purchased: i.no_of_license_purchased,
//             purchase_date: i.purchase_date
//               ? Dayjs(Moment.unix(i.purchase_date).format(FORMAT), FORMAT)
//               : undefined,
//             expiry_date: i.expiry_date
//               ? Dayjs(Moment.unix(i.expiry_date).format(FORMAT), FORMAT)
//               : undefined,

//             purchase_date_ms: i.purchase_date,
//             expiry_date_ms: i.expiry_date,

//             invoice_number: i.invoice_number,
//             po_number: i.po_number,
//             license_ref_number: i.license_ref_number,
//             vendor: i.vendor,
//             location: i.location,
//             licensed_to: i.licensed_to,
//             comments: i.comments,
//             // license_file: i.license_file,
//             // license_file_ref: i.license_file_ref,
//             // invoice_file: i.invoice_file,
//             // invoice_file_ref: i.invoice_file_ref,
//             // purchase_order: i.purchase_order,
//             // purchase_order_ref: i.purchase_order_ref,
//             assets: (i.assets || '')
//               .split(',')
//               .map((i) => +i)
//               .filter(Boolean),
//             created_time: i.created_time,
//             modified_time: i.modified_time,
//             isChildLicense: true,

//             ...(i.license_file_ref
//               ? {
//                   license_file: [
//                     {
//                       ref: i.license_file_ref,
//                       name: i.license_file
//                     }
//                   ]
//                 }
//               : {}),

//             ...(i.invoice_file_ref
//               ? {
//                   invoice_file: [
//                     {
//                       ref: i.invoice_file_ref,
//                       name: i.invoice_file
//                     }
//                   ]
//                 }
//               : {}),

//             ...(i.purchase_order_ref
//               ? {
//                   purchase_order: [
//                     {
//                       ref: i.purchase_order_ref,
//                       name: i.purchase_order
//                     }
//                   ]
//                 }
//               : {})
//           }))
//         }
//       : {})
//   };
// }

function transformForServer(item) {
  let obj = {
    ...((item.isChildLicense || item.forEditLicense) && !item.forAddLicense ? { id: item.id } : {}),
    ...(item.forAddLicense ? { license_id: item.parentId || item.id } : {}),
    // ...(item.forEditLicense ? {} : {}),
    software_name: item.software_name,
    software_id: item.software_id,
    software_version: item.software_version,
    software_manufacturer:
      item.software_manufacturer === 'null' ? null : item.software_manufacturer,
    license_owner: item.license_owner || '',
    license_edition: item.license_edition,
    license_name: item.license_name,
    ...(item.edition_id !== null ? { edition_id: item.edition_id } : {}),
    license_key: item.license_key,
    ...(item.cost !== null ? { cost: item.cost } : {}),
    cost_currency: item.cost_currency,
    no_of_license_purchased: item.no_of_license_purchased || 0,
    purchase_date: item.purchase_date ? item.purchase_date.unix() : undefined,
    expiry_date: item.expiry_date ? item.expiry_date.unix() : undefined,
    invoice_number: item.invoice_number,
    po_number: item.po_number,
    license_ref_number: item.license_ref_number,
    vendor: item.vendor,
    location: item.location,
    licensed_to: item.licensed_to,
    comments: item.comments,
    ...(item.license_file && item.license_file.length > 0 && item.license_file[0].response
      ? {
          license_file_ref: item.license_file[0].response.ref,
          license_file: item.license_file[0].response.name
        }
      : {}),
    ...(item.invoice_file && item.invoice_file.length > 0 && item.invoice_file[0].response
      ? {
          invoice_file_ref: item.invoice_file[0].response.ref,
          invoice_file: item.invoice_file[0].response.name
        }
      : {}),
    ...(item.purchase_order_file &&
    item.purchase_order_file.length > 0 &&
    item.purchase_order_file[0].response
      ? {
          purchase_order_ref: item.purchase_order_file[0].response.ref,
          purchase_order: item.purchase_order_file[0].response.name
        }
      : {}),
    assets: (item.assets || []).join(',')
  };

  obj = OmitBy(obj, (value) => value === null);

  return obj;
}

export function getAllLicenseManagementApi(offset, size, sortFilter) {
  // return Promise.resolve({
  //   totalCount: 1,
  //   result: [{ software_name: 'name', software_id: 1 }]
  // });
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,

      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map((i) => transform(i))
      };
    });
}

export function updateLicenseManagementApi(item) {
  return api
    .put(`${END_POINT}/${item.parentId || item.id}`, {
      ...transformForServer(item)
    })
    .then((data) => {
      return getLicenseManagementApi(data.result);
    });
}

export function createLicenseManagementApi(formData) {
  return api.post(`${END_POINT}`, transformForServer(formData)).then((data) => {
    return getLicenseManagementApi(data.result);
  });
}

export function getLicenseManagementApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function deleteLicenseManagementApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}

export function deleteLicenseDetaiilsApi(item, license) {
  return api.delete(`${END_POINT}/${item.id}/details/${license.id}`);
}

export function getAssetsForLicenseId(parentId, childId, offset, size, sortFilter) {
  return api
    .get(`${END_POINT}/${parentId}/assets/${childId}`, {
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${sortFilter.sort.field}`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then(({ result }) => {
      return {
        totalCount: result.length,
        result: result.slice(offset, size).map((item) => ({
          id: item.id,
          hostname: item.hostname
        }))
      };
    });
}
