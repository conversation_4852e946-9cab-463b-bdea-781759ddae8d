import { useState, createContext, useContext } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Outlet } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import InventoryHierarchy from '../components/InventoryHierarchy';
import Permission<PERSON>hecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import SplitPane from '@/src/components/SplitPane';
import { License } from '@/src/components/LicenseProvider';
import NotFound from '@/src/components/NotFound';

const InventoryLayoutContext = createContext();

export default function InventoryLayout() {
  const [showMenu, setShowMenu] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();
  const { isEndpointOps } = License.useLicense();
  const [isMenuVisible, setIsMenuVisible] = useState(true);

  const [selectedFilter, setSelectedFilter] = useState(null);
  const [selectedLocationFilter, setLocationSelectedFilter] = useState(null);
  const [selectedDepartmentFilter, setDepartmentSelectedFilter] = useState(null);
  const pathParts = location.pathname.replace(/\d+$/, '').split('/').filter(Boolean);
  const isNetworkDevice = pathParts[pathParts.length - 1] === 'network-devices';

  if (isNetworkDevice && !isEndpointOps()) {
    return <NotFound />;
  }

  function handleFilterSelected(value) {
    if (value.type === 'location') {
      setLocationSelectedFilter(value);
    } else if (value.type === 'department') {
      setDepartmentSelectedFilter(value);
    } else {
      setSelectedFilter(value);
    }
    navigate(/\d+^/.test(location.pathname) ? `/inventory/endpoints` : location.pathname);
  }

  return (
    <InventoryLayoutContext.Provider
      value={{
        hideMenu: () => setShowMenu(false),
        showMenu: () => setShowMenu(true),
        displayNone: () => setIsMenuVisible(false),
        displayBlock: () => setIsMenuVisible(true),
        filter: selectedFilter,
        locationFilter: selectedLocationFilter,
        departmentFilter: selectedDepartmentFilter
      }}>
      <div className="h-full flex flex-col">
        <PageHeading icon="inventory" title="Inventory" />
        <PermissionChecker permission={constants.View_Inventory} redirect>
          <SplitPane
            hasMenu={showMenu}
            isMenuVisible={isMenuVisible}
            onVisibleChange={(i) => setIsMenuVisible(i)}
            leftPane={
              <InventoryHierarchy
                selectedPlatform={selectedFilter}
                selectedDepartment={selectedDepartmentFilter}
                selectedLocation={selectedLocationFilter}
                onSelect={(item) => handleFilterSelected(item)}
              />
            }
            rightPane={<Outlet />}
          />
        </PermissionChecker>
      </div>
    </InventoryLayoutContext.Provider>
  );
}

export function useInventoryLayout() {
  return useContext(InventoryLayoutContext);
}
