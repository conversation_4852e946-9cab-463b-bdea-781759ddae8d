import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import PageHeading from '@/src/components/PageHeading';
import PermissionChecker from '@/src/components/PermissionChecker';
import { Button, Drawer } from 'antd';
import Icon from '@/src/components/Icon';

import constants from '@/src/constants/index';

import {
  getAllLicenseManagementApi,
  updateLicenseManagementApi,
  createLicenseManagementApi,
  deleteLicenseManagementApi,
  getAssetsForLicenseId,
  getLicenseManagementApi
} from '../api/license-managment';

import { CrudProvider } from '@/src/hooks/crud';

import LicenseManagementForm from '../components/LicenseManagementForm';
import { useInventoryLayout } from '../layout/InventoryLayout';
import { LicenseEdition } from '@/src/components/pickers/LicenseEditionPicker';
import { Currency } from '@/src/components/pickers/CurrencyPicker';
import { Asset } from '@/src/components/pickers/AssetPicker';
import { Software } from '@/src/components/pickers/SoftwarePicker';
import LicensedComponent from '@/src/components/LicensedComponent';

export default function LicenseManagement() {
  const navigate = useNavigate();
  const layout = useInventoryLayout();
  const [assetsForManagedInstallations, setShowAssetForManagedInstallations] = useState(null);

  function getSingleLicense(item) {
    return getLicenseManagementApi(item.id).then((l) => {
      return l;
    });
  }

  async function getAndSet(item, setterFn, forAdd = false) {
    const singleLicense = await getSingleLicense(item);

    if (setterFn) {
      setterFn({ ...singleLicense, forAddLicense: forAdd, forEditLicense: !forAdd });
    }
  }

  useEffect(() => {
    layout.hideMenu();

    return () => layout.showMenu();
    // eslint-disable-next-line
  }, []);

  const assetColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Host Name',
      dataIndex: 'hostname',
      key: 'hostname',
      render({ record }) {
        return (
          <div className="flex items-center">
            {/* eslint-disable-next-line */}
            <a
              className="cursor-pointer"
              onClick={() => navigate(`/inventory/endpoints/${record.id}`)}>
              {record.hostname}
            </a>
          </div>
        );
      }
    }
  ];

  const columns = [
    {
      title: 'Software Name',
      dataIndex: 'software_name',
      width: 100,
      key: 'software_name',
      render({ record }) {
        return (
          <div className="flex items-center">
            {/* eslint-disable-next-line */}
            <a
              className="cursor-pointer"
              onClick={() => navigate(`/inventory/license-management/${record.id}`)}>
              {record.software_name}
            </a>
          </div>
        );
      }
    },

    {
      title: 'Software Manufacturer',
      dataIndex: 'software_manufacturer',
      key: 'software_manufacturer'
    },
    {
      title: 'Software Version',
      dataIndex: 'software_version',
      key: 'software_version'
    },
    {
      title: 'License Owner',
      dataIndex: 'license_owner',
      key: 'license_owner'
    },

    {
      title: 'Total Purchased',
      dataIndex: 'total_purchased',
      key: 'total_purchased',
      hidden: true
    },
    {
      title: 'Managed Installations',
      dataIndex: 'managed_installations',
      key: 'managed_installations',
      render({ record }) {
        return (
          <Button type="link" onClick={() => setShowAssetForManagedInstallations(record)}>
            {record.managed_installations}
          </Button>
        );
      },
      hidden: true
    },
    {
      title: 'Remaining',
      dataIndex: 'remaining',
      key: 'remaining'
    },
    {
      title: 'Compliance Status',
      dataIndex: 'compliance_status',
      key: 'compliance_status'
    },
    {
      title: 'Created On',
      dataIndex: 'created_time',
      key: 'created_time',
      type: 'datetime',
      hidden: true
    },
    {
      title: 'Modified Time',
      dataIndex: 'modified_time',
      key: 'modified_time',
      type: 'datetime',
      hidden: true
    },

    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      width: 100,

      editPermissions: [constants.Update_Inventory],
      prependAction({ record, edit }) {
        return (
          <Button
            shape="circle"
            type="link"
            // onClick={() => getAndSet(record, edit, true)}
            onClick={() => edit({ ...record, forAddLicense: true })}
            className="mr-2"
            title="Add More">
            <Icon name="add" style={{ fontSize: '1.1rem' }} className="text-primary" />
          </Button>
        );
      }
    }
  ];

  return (
    <LicensedComponent allowedProducts={[LicensedComponent.EndpointOps]} useNotFound={true}>
      <PageHeading icon="inventory" title={`License Management`} />
      <Software.Provider scope={{ assetFilter: 1 }} refetchOnSearch>
        <Asset.Provider>
          <LicenseEdition.Provider>
            <Currency.Provider>
              <CrudProvider
                columns={columns}
                resourceTitle="License"
                hasSearch
                updateFn={updateLicenseManagementApi}
                onEdit={(item, setFormItem) => getAndSet(item, setFormItem)}
                fetchFn={(...args) => getAllLicenseManagementApi(...args)}
                createFn={createLicenseManagementApi}
                deleteFn={deleteLicenseManagementApi}
                defaultFormItem={{
                  software_name: undefined,
                  // license_name: 'LicenseName_7682',
                  forAddLicense: false
                }}
                createSlot={(createFn) => (
                  <PermissionChecker permission={constants.Create_Inventory}>
                    <Button type="primary" onClick={createFn}>
                      Create
                    </Button>
                  </PermissionChecker>
                )}
                formFields={(item, _, { disabled }) => (
                  <LicenseManagementForm item={item} disabled={disabled} />
                )}
                drawerTitle={(item) =>
                  item.forAddLicense ? 'Add License' : item.id ? 'Edit License' : 'Create License'
                }
                formActions={({ formItem, resetForm, processingForm }) => (
                  <>
                    <Button
                      type="primary"
                      loading={processingForm}
                      htmlType="submit"
                      className="mr-2">
                      {formItem.forAddLicense ? 'Add' : formItem.id ? 'Update' : 'Create'}
                    </Button>

                    <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
                      Reset
                    </Button>
                  </>
                )}
              />
            </Currency.Provider>
          </LicenseEdition.Provider>
        </Asset.Provider>
      </Software.Provider>

      <Drawer
        title={`
          Assets for License ${
            assetsForManagedInstallations ? assetsForManagedInstallations.license_name : null
          }`}
        placement="right"
        width="50%"
        onClose={() => setShowAssetForManagedInstallations(null)}
        destroyOnClose
        open={Boolean(assetsForManagedInstallations)}>
        {Boolean(assetsForManagedInstallations) && (
          <CrudProvider
            resourceTitle="Assets"
            hasSearch
            fetchFn={(...args) =>
              getAssetsForLicenseId(assetsForManagedInstallations.id, -1, ...args)
            }
            columns={assetColumns}
          />
        )}
      </Drawer>
    </LicensedComponent>
  );
}
