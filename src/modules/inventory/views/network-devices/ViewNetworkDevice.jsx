import React from 'react';
import { Spin } from 'antd';
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import { User } from '@/src/components/pickers/UserPicker';
import { Department } from '@components/pickers/DepartmentPicker';
import { Location } from '@components/pickers/LocationPicker';
import { useAuth } from '@/src/hooks/auth';
import {
  updateInventoryNetworkDeviceApi,
  getNetworkDeviceSummaryApi
} from '../../api/network-devices.js';
import { useInventoryLayout } from '../../layout/InventoryLayout';
import DeviceDetail from '../../components/network-device/DeviceDetail.jsx';

export default function ViewNetworkDevice() {
  const params = useParams();
  const navigate = useNavigate();

  const [device, setDevice] = useState(null);
  const [loading, setLoading] = useState(true);
  const { formatDateTime } = useAuth();

  const context = useInventoryLayout();

  function setDeviceData(data) {
    setDevice({ ...data, lastSeen: data.lastSeen ? formatDateTime(data.lastSeen) : '' });
  }

  useEffect(() => {
    context.displayNone();

    getNetworkDeviceSummaryApi(params.id)
      .then((data) => {
        setDeviceData(data);
        setLoading(false);
      })
      .catch(() => {
        navigate('/inventory/network-devices');
      });

    // eslint-disable-next-line
  }, [params]);

  function handleOnChange(data) {
    updateInventoryNetworkDeviceApi(data)
      .then(() => getNetworkDeviceSummaryApi(params.id))
      .then((data) => setDeviceData(data));
  }
  return !loading && device && device.id ? (
    <User.Provider>
      <Department.Provider>
        <Location.Provider>
          <div className="flex flex-col flex-1 h-full">
            <PageHeading
              icon={(device.device_type || 'snmp device').toLowerCase()}
              title={`${device.hostname}`}
            />
            <DeviceDetail device={device} onChange={handleOnChange} />
          </div>
        </Location.Provider>
      </Department.Provider>
    </User.Provider>
  ) : (
    <Spin spinning={loading}>
      <div className="flex flex-col min-w-0 min-h-0 flex-1" />
    </Spin>
  );
}
