import React from 'react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Tag } from 'antd';
import constants from '@/src/constants/index';
import { User } from '@components/pickers/UserPicker';
import { Department } from '@components/pickers/DepartmentPicker';
import { Location } from '@components/pickers/LocationPicker';

import SystemLogo from '@/src/components/SystemLogo';
import Score from '@/src/components/Score';
import PageHeading from '@/src/components/PageHeading';

import { useInventoryLayout } from '../../layout/InventoryLayout';
import { CrudProvider } from '@/src/hooks/crud';

import { getAssignableUsersApi } from '@/src/modules/settings/api/user-management/users';
import {
  deleteNetworkDeviceApi,
  getNetworkDevicesApi,
  updateInventoryNetworkDeviceApi
} from '../../api/network-devices';
import LiveStatus from '@/src/components/LiveStatus';
import Icon from '@/src/components/Icon';

export default function List() {
  const layout = useInventoryLayout();
  const navigate = useNavigate();

  const columns = [
    {
      title: 'Host Name',
      dataIndex: 'hostname',
      width: 100,
      key: 'hostname',
      render({ record }) {
        return (
          <div className="flex items-center">
            {/* eslint-disable-next-line */}
            <a className="cursor-pointer" onClick={() => navigate(`${record.id}`)}>
              {record.hostname}
            </a>
          </div>
        );
      }
    },
    {
      title: 'Assigned To',
      dataIndex: 'owner',
      key: 'owner',
      sortable: false,
      render({ record, update }) {
        return (
          <User.Picker value={record.owner} onChange={(id) => update({ ...record, owner: id })} />
        );
      }
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
      sortable: false,
      hidden: true,
      render({ record, update }) {
        return (
          <Department.Picker
            value={record.department}
            onChange={(id) => update({ ...record, department: id })}
          />
        );
      }
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      render({ record, update }) {
        return (
          <Location.Picker
            value={record.location}
            onChange={(id) => update({ ...record, location: id })}
          />
        );
      }
    },
    {
      title: 'Vendor',
      dataIndex: 'vendor',
      key: 'vendor',
      render({ record }) {
        return (
          <>
            <div className="flex items-center">
              <SystemLogo name={record.vendor} className="w-8 flex-shrink-0" type="vendor" />
              <span className="ml-1">{record.vendor}</span>
            </div>
          </>
        );
      }
    },
    {
      title: 'Device Model',
      dataIndex: 'device_model',
      key: 'device_model'
    },
    {
      title: 'Device Type',
      dataIndex: 'device_type',
      key: 'device_type',
      render({ record }) {
        return (
          <div className="flex items-center">
            <Icon
              name={(record.device_type || 'snmp device').toLowerCase()}
              className="mr-2 text-xl text-neutral-light"
            />
            {record.device_type || 'SNMP Device'}
          </div>
        );
      }
    },
    {
      title: 'Vulnerabilities',
      dataIndex: 'vulnerability_count',
      key: 'vulnerability_count',
      align: 'center',
      sortable: false,
      render({ record }) {
        return <Tag color="error">{record.vulnerability_count}</Tag>;
      }
    },
    {
      title: 'Risk',
      dataIndex: 'riskScore',
      key: 'riskScore',
      sortable: false,
      align: 'center',
      hidden: true,
      render({ record }) {
        return <Score value={record.riskScore} useCircle category="risk" size={40} />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },

    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      buttons: ['delete'],
      editPermissions: [constants.Update_Inventory],
      deletePermissions: [constants.Delete_Inventory]
    }
  ];

  useEffect(() => {
    // layout.hideMenu();

    return () => layout.showMenu();
    // eslint-disable-next-line
  }, []);
  return (
    <User.Provider apiFn={getAssignableUsersApi}>
      <Location.Provider>
        <Department.Provider>
          <PageHeading icon="inventory" title={`Network Devices`} />

          <div className="flex flex-1 flex-col min-h-0">
            <CrudProvider
              resourceTitle="Network Device"
              columns={columns}
              fetchFn={(...args) =>
                getNetworkDevicesApi(
                  ...args,
                  layout.filter,
                  layout.locationFilter,
                  layout.departmentFilter
                )
              }
              searchboxProps={{
                size: 'medium',
                style: {
                  width: '350px'
                }
              }}
              hasSearch
              key={`
                ${layout.filter ? layout.filter.title : 'none'}
                ${layout.locationFilter ? layout.locationFilter.title : 'none'}
                ${layout.departmentFilter ? layout.departmentFilter.title : 'none'}
                `}
              deleteFn={deleteNetworkDeviceApi}
              updateFn={updateInventoryNetworkDeviceApi}
              prependColumns={[
                {
                  title: ' ',
                  resizable: false,
                  width: 20,
                  key: 'currentStatus',
                  dataIndex: 'currentStatus',
                  order: -1,
                  sortable: false,
                  selectable: false,
                  render({ record }) {
                    return (
                      <LiveStatus status={record.currentStatus}>
                        <span className="mr-1" />
                      </LiveStatus>
                    );
                  }
                }
              ]}
            />
          </div>
        </Department.Provider>
      </Location.Provider>
    </User.Provider>
  );
}
