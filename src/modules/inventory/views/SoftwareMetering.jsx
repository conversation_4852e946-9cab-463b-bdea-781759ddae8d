import { Tabs } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import { useEffect, useState } from 'react';
import SoftwareMeteringRules from '../components/SoftwareMeteringRules';
import SoftwareMeteringSummary from '../components/SoftwareMeteringSummary';
import { useInventoryLayout } from '../layout/InventoryLayout';
import SplitPane from '@/src/components/SplitPane';
import LicensedComponent from '@/src/components/LicensedComponent';

export default function SoftwareMetering() {
  const layout = useInventoryLayout();
  const navigate = useNavigate();
  const location = useLocation();

  const tabs = [
    {
      label: 'SW Metering Summary',
      key: 'summary'
    },
    {
      label: 'SW Metering Rules',
      key: 'rules'
    }
  ];

  useEffect(() => {
    layout.hideMenu();

    return () => layout.showMenu();
    // eslint-disable-next-line
  }, []);

  const [activeKey] = useState(location.pathname.indexOf('rules') >= 0 ? 'rules' : 'summary');

  return (
    <LicensedComponent allowedProducts={[LicensedComponent.EndpointOps]} useNotFound={true}>
      <div className="flex flex-1 min-h-0 flex-col">
        <PageHeading icon="inventory" title={`Software Metering`} />
        <SplitPane
          leftPane={
            <Tabs
              activeKey={activeKey}
              onChange={(key) => {
                navigate(`/inventory/software-metering/${key}`);
              }}
              items={tabs}
              tabPosition="left"
              className="w-full h-full"
            />
          }
          rightPane={
            activeKey === 'rules' ? <SoftwareMeteringRules /> : <SoftwareMeteringSummary />
          }
        />
      </div>
    </LicensedComponent>
  );
}
