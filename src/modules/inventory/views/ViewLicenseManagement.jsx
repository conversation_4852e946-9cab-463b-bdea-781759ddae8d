import { useEffect, useState, useCallback, Fragment } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Spin, Row, Col, Divider, But<PERSON>, Drawer } from 'antd';
import { useNavigate } from 'react-router-dom';

import constants from '@/src/constants/index';
import { CrudProvider } from '@/src/hooks/crud';
import { Asset } from '@/src/components/pickers/AssetPicker';
import { useInventoryLayout } from '../layout/InventoryLayout';
import PageHeading from '@/src/components/PageHeading';
// import Dayjs from 'dayjs';

import {
  getLicenseManagementApi,
  deleteLicenseDetaiilsApi,
  updateLicenseManagementApi,
  getAssetsForLicenseId
  // FORMAT
} from '../api/license-managment';
import LicenseManagementForm from '../components/LicenseManagementForm';

import { LicenseEdition } from '@/src/components/pickers/LicenseEditionPicker';
import { Currency } from '@/src/components/pickers/CurrencyPicker';
import { Software } from '@/src/components/pickers/SoftwarePicker';
import Icon from '@/src/components/Icon';

const softwareDetailBox = [
  {
    key: 'software_name',
    label: 'Software Name'
  },
  {
    key: 'software_version',
    label: 'Version'
  },

  {
    key: 'software_manufacturer',
    label: 'Manufacturer'
  }
  // {
  //   key: 'software_access_type',
  //   label: 'Software Access Type'
  // },
  // {
  //   key: 'edition_id',
  //   label: 'Edition Type'
  // }
];

const licesneDetailBox = [
  {
    key: 'license_owner',
    label: 'License Owner'
  },
  // {
  //   key: 'purchased_copies',
  //   label: 'Purchased Copies'
  // },

  {
    key: 'managed_installations',
    label: 'Managed Installations'
  },
  // {
  //   key: 'network_installtions',
  //   label: 'Network Installtions'
  // },
  {
    key: 'compliance_status',
    label: 'Compliance Status'
  }
];

export default function ViewLicenseManagement() {
  const navigate = useNavigate();

  const context = useInventoryLayout();
  const params = useParams();
  const [licenseContext, setLicenseContext] = useState({ license_details: [] });
  const [updateKey, setUpdateKey] = useState(0); // Key to force re-renders
  const [assetsForLicense, setShowAssetForLicense] = useState(null);

  // const [isDeleting, setIsDeleting] = useState(false);

  const [loading, setLoading] = useState(true);
  const layout = useInventoryLayout();

  async function fetchLicenseData(shoLoader = false) {
    if (shoLoader) setLoading(true);
    await getLicenseManagementApi(params.id).then((data) => {
      setLicenseContext(data);
      setLoading(false);
      return data;
    });
  }

  function updateLicenseManagementAndUpdateApi(item) {
    return updateLicenseManagementApi(item).then((data) => {
      setLicenseContext(data);

      setUpdateKey((prevKey) => prevKey + 1); // Change the key to force re-render
      // return (data.license_details || []).filter((l) => l.id === item.id);
    });
  }

  useEffect(() => {
    layout.hideMenu();
    context.displayNone();
    fetchLicenseData(true);
    return () => {
      context.displayBlock();
      layout.showMenu();
    };
    // eslint-disable-next-line
  }, []);

  function deleteSingleLicenseDetails(licenseDetail) {
    // setIsDeleting(true);

    return deleteLicenseDetaiilsApi(licenseContext, licenseDetail).then(() => {
      const updatedLicenseContext = {
        ...licenseContext,
        license_details: (licenseContext.license_details || []).filter(
          (l) => l.id !== licenseDetail.id
        )
      };

      setLicenseContext(updatedLicenseContext);
      setUpdateKey((prevKey) => prevKey + 1); // Change the key to force re-render

      // fetchLicenseData().then(() => {
      //   // setIsDeleting(false);
      // });
    });
  }

  const fetchFn = useCallback(
    (offset, size) => {
      return Promise.resolve({
        totalCount: licenseContext.license_details.length,
        result: licenseContext.license_details.slice(offset, offset + size)
      });
    },
    [licenseContext]
  );

  const assetColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Host Name',
      dataIndex: 'hostname',
      key: 'hostname',
      render({ record }) {
        return (
          <div className="flex items-center">
            {/* eslint-disable-next-line */}
            <a
              className="cursor-pointer"
              onClick={() => navigate(`/inventory/endpoints/${record.id}`)}>
              {record.hostname}
            </a>
          </div>
        );
      }
    }
  ];

  const purchasedLicenseColumns = [
    {
      title: 'License Name',
      dataIndex: 'license_name',
      key: 'license_name',
      hidden: false,
      sortable: false,
      searchable: false
    },

    {
      title: 'Licensed To',
      dataIndex: 'licensed_to',
      key: 'licensed_to',
      hidden: false,
      sortable: false,
      searchable: false
    },
    {
      title: 'Purchased',
      dataIndex: 'no_of_license_purchased',
      key: 'no_of_license_purchased',
      hidden: false,
      sortable: false,
      searchable: false
    },
    {
      title: 'Purchase Date',
      dataIndex: 'purchase_date_ms',
      key: 'purchase_date_ms',
      hidden: false,
      sortable: false,
      searchable: false
      // type: 'datetime'

      // render({ record }) {
      //   return <span>{Dayjs(record.purchase_date).format(FORMAT)}</span>;
      // }
    },
    {
      title: 'Expiry Date',
      dataIndex: 'expiry_date_ms',
      key: 'expiry_date_ms',
      hidden: false,
      sortable: false,
      searchable: false
      // type: 'datetime'

      // render({ record }) {
      //   return <span>{Dayjs(record.expiry_date).format(FORMAT)}</span>;
      // }
    },

    {
      title: 'Associated Endpoints',
      dataIndex: 'assets',
      key: 'assets',
      render({ record }) {
        return (
          <Button type="link" onClick={() => setShowAssetForLicense(record)}>
            {(record.assets || []).length}
          </Button>
        );
      },
      align: 'center',
      sortable: false,
      searchable: false
    },
    // {
    //   title: 'License File',
    //   dataIndex: 'license_file',
    //   key: 'license_file',
    //   hidden: false,
    //   render({ record }) {
    //     return <div className="flex items-center">{/* eslint-disable-next-line */}</div>;
    //   }
    // },
    // {
    //   title: 'Invoice File',
    //   dataIndex: 'invoice_file',
    //   key: 'invoice_file',
    //   hidden: false,
    //   render({ record }) {
    //     return <div className="flex items-center">{/* eslint-disable-next-line */}</div>;
    //   }
    // },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Inventory],
      deletePermissions: [constants.Update_Inventory]
    }
  ];

  return !loading ? (
    <Software.Provider scope={{ assetFilter: 1 }} refetchOnSearch>
      <LicenseEdition.Provider>
        <Currency.Provider>
          <Asset.Provider>
            <div className="flex flex-col min-h-0 h-full">
              <PageHeading
                beforeIcon={
                  <Link
                    to={'..'}
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(-1);
                    }}
                    className="mr-2 text-lg">
                    <Icon name="arrow-left" />
                  </Link>
                }
                icon="license"
                title={`${licenseContext.software_name}`}
              />
              <div className="flex-1 flex flex-col min-h-0 overflow-y-auto">
                <Row className="">
                  <Col span={12} className=" flex flex-col   pr-1">
                    <Row className=" min-h-0 w-full rounded  bg-seperator  px-2 pt-2">
                      <Col span={24} className=" flex flex-col mb-2 text-primary text-base	">
                        Software Details
                      </Col>

                      {softwareDetailBox.map((d, index) => (
                        <Fragment key={d.key}>
                          <Col
                            span={11}
                            className=" text-ellipsis hover:whitespace-normal mb-2 text-neutral-light">
                            {d.label}
                          </Col>
                          <Col span={2} className="text-center  mb-2">
                            :
                          </Col>
                          <Col span={11} className="text-ellipsis hover:whitespace-normal  mb-2">
                            {licenseContext[d.key]}
                          </Col>
                        </Fragment>
                      ))}
                    </Row>
                  </Col>
                  <Col span={12} className=" flex flex-col  pl-1">
                    <Row className=" min-h-0 w-full rounded  bg-seperator px-2 pt-2">
                      <Col span={24} className=" flex flex-col  mb-2 text-primary text-base	">
                        License Details
                      </Col>

                      {licesneDetailBox.map((d, index) => (
                        <Fragment key={d.key}>
                          <Col
                            span={11}
                            className=" text-ellipsis hover:whitespace-normal mb-2 text-neutral-light">
                            {d.label}
                          </Col>
                          <Col span={2} className="text-center  mb-2">
                            :
                          </Col>
                          <Col span={11} className="text-ellipsis hover:whitespace-normal  mb-2">
                            {licenseContext[d.key]}
                          </Col>
                        </Fragment>
                      ))}
                    </Row>
                  </Col>
                </Row>
                <div className="mt-2 flex flex-col flex-1">
                  <div span={24} className=" flex flex-col  my-2 text-primary text-base grow-0">
                    Purchased License
                  </div>

                  <Divider className="my-1 grow-0" />
                  <div className="flex flex-col  mb-2  flex-1">
                    <CrudProvider
                      key={updateKey}
                      resourceTitle="License"
                      disableHeadingSlot
                      disableColumnSelection
                      disableExport
                      disableRefresh
                      fetchFn={fetchFn}
                      deleteFn={deleteSingleLicenseDetails}
                      columns={purchasedLicenseColumns}
                      updateFn={updateLicenseManagementAndUpdateApi}
                      formFields={(item, _, { disabled }) => (
                        <LicenseManagementForm item={item} disabled={disabled} />
                      )}
                      className="px-0"
                    />
                  </div>
                </div>
              </div>

              <Drawer
                title={`
          Assets for License ${assetsForLicense ? assetsForLicense.license_name : null}`}
                placement="right"
                width="50%"
                onClose={() => setShowAssetForLicense(null)}
                destroyOnClose
                open={Boolean(assetsForLicense)}>
                {Boolean(assetsForLicense) && (
                  <CrudProvider
                    resourceTitle="Assets"
                    hasSearch
                    fetchFn={(...args) =>
                      getAssetsForLicenseId(licenseContext.id, assetsForLicense.id, ...args)
                    }
                    columns={assetColumns}
                  />
                )}
              </Drawer>
            </div>
          </Asset.Provider>
        </Currency.Provider>
      </LicenseEdition.Provider>
    </Software.Provider>
  ) : (
    <Spin spinning={loading}>
      <div className="flex flex-col min-w-0 min-h-0 flex-1" />
    </Spin>
  );
}
