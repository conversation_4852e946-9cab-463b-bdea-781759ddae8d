import { useNavigate } from 'react-router-dom';
import { Progress, Alert } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { Link } from 'react-router-dom';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import { User } from '@components/pickers/UserPicker';
import { Department } from '@components/pickers/DepartmentPicker';
import { Location } from '@components/pickers/LocationPicker';
import {
  deleteInventoryHostApi,
  getInventoryHostsApi,
  updateInventoryHostApi
} from '../../api/hosts';
import { useInventoryLayout } from '../../layout/InventoryLayout';
import HostExpandedRow from '../../components/hosts/expanded-row/HostExpandedRow';
import LiveStatus from '@/src/components/LiveStatus';
import constants from '@/src/constants/index';
import HostStatistics from '../../components/hosts/HostStatistics';
import PageHeading from '@/src/components/PageHeading';
import SystemLogo from '@/src/components/SystemLogo';
import Score from '@/src/components/Score';
import { getAssignableUsersApi } from '@/src/modules/settings/api/user-management/users';
import { useEffect, useState } from 'react';
import { getAllApprovalApi } from '@/src/modules/settings/api/agent-management/agent-approval';
import { Permissions } from '@/src/components/Permissions';

export default function Hosts() {
  const context = useInventoryLayout();
  const { hasPermission } = Permissions.usePermission();

  const columns = [
    {
      title: 'Host Name',
      dataIndex: 'hostname',
      width: 100,
      key: 'hostname',
      render({ record }) {
        return (
          <div className="flex items-center">
            {/* eslint-disable-next-line */}
            <a className="cursor-pointer" onClick={() => navigate(`${record.id}`)}>
              {record.hostname}
            </a>
          </div>
        );
      }
    },
    {
      title: 'Risk',
      dataIndex: 'risk',
      key: 'risk',
      sortable: false,
      align: 'center',
      hidden: true,
      render({ record }) {
        return <Score value={record.risk} useCircle category="risk" size={40} />;
      }
    },
    {
      title: 'Assigned To',
      dataIndex: 'owner',
      key: 'owner',
      render({ record, update }) {
        return (
          <User.Picker value={record.owner} onChange={(id) => update({ ...record, owner: id })} />
        );
      }
    },
    {
      title: 'OS Version',
      dataIndex: 'os_version',
      key: 'os_version',
      hidden: true,
      ellipsis: true
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
      hidden: true,
      render({ record, update }) {
        return (
          <Department.Picker
            value={record.department}
            onChange={(id) => update({ ...record, department: id })}
          />
        );
      }
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      render({ record, update }) {
        return (
          <Location.Picker
            value={record.location}
            onChange={(id) => update({ ...record, location: id })}
          />
        );
      }
    },
    {
      title: 'Vendor',
      dataIndex: 'hardware_vendor',
      key: 'hardware_vendor',
      render({ record }) {
        return (
          <>
            <div className="flex items-center">
              <SystemLogo
                name={record.hardware_vendor}
                className="w-8 flex-shrink-0"
                type="vendor"
              />
              <span className="ml-1">{record.hardware_vendor}</span>
            </div>
          </>
        );
      }
    },
    {
      title: 'Memory',
      dataIndex: 'physical_memory',
      key: 'physical_memory',
      align: 'center'
    },
    {
      title: 'Disk',
      dataIndex: 'total_disk_space',
      key: 'total_disk_space',
      width: 150,
      align: 'center',
      render({ record }) {
        const diskPercent = Math.round(
          (parseFloat(record.used_disk_space) * 100) / parseFloat(record.total_disk_space)
        );
        return (
          <Progress
            title={`${diskPercent}%`}
            strokeColor={diskPercent > 80 ? '#EB5758' : diskPercent > 60 ? '#F2994B' : '#89C540'}
            percent={diskPercent}
            size="small"
          />
        );
      }
    },
    {
      title: 'Hardware Model',
      dataIndex: 'hardware_model',
      key: 'hardware_model'
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version'
    },
    {
      title: 'Arch',
      dataIndex: 'arch',
      key: 'arch',
      hidden: true
    },
    {
      title: 'Build',
      dataIndex: 'build',
      key: 'build',
      hidden: true
    },
    {
      title: 'Code Name',
      dataIndex: 'code_name',
      key: 'code_name',
      hidden: true
    },
    {
      title: 'CPU Brand',
      dataIndex: 'cpu_brand',
      key: 'cpu_brand',
      hidden: true
    },
    {
      title: 'CPU Logical Cores',
      dataIndex: 'cpu_logical_cores',
      key: 'cpu_logical_cores',
      hidden: true,
      align: 'center'
    },
    {
      title: 'CPU Physical Cores',
      dataIndex: 'cpu_physical_cores',
      key: 'cpu_physical_cores',
      align: 'center'
    },
    {
      title: 'CPU Type',
      dataIndex: 'cpu_type',
      key: 'cpu_type',
      hidden: true
    },
    {
      title: 'Kernel Version',
      dataIndex: 'kernel_version',
      key: 'kernel_version',
      hidden: true
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      buttons: ['delete'],
      editPermissions: [constants.Update_Inventory],
      deletePermissions: [constants.Delete_Inventory]
    }
  ];

  const navigate = useNavigate();

  const [totalPendingApproval, setTotalPendingApproval] = useState(0);

  useEffect(() => {
    if (hasPermission([constants.Manage_Inventory], false)) {
      const fn = () =>
        getAllApprovalApi(0, 1, { status: -1 }).then((data) =>
          setTotalPendingApproval(data.totalCount)
        );
      fn();
      let interval = setInterval(fn, 10000);

      return () => clearInterval(interval);
    }
    // eslint-disable-next-line
  }, []);

  return (
    <User.Provider apiFn={getAssignableUsersApi}>
      <Department.Provider>
        <Location.Provider>
          {totalPendingApproval > 0 ? (
            <Link to="/settings/agent-management/agent-approvals">
              <Alert
                message={`You have total ${totalPendingApproval} pending agent approval`}
                type="warning"
                showIcon
              />
            </Link>
          ) : null}
          <HostStatistics />
          <PageHeading icon="inventory" title={`Endpoints`} />
          <div className="flex flex-1 flex-col min-h-0">
            <CrudProvider
              columns={columns}
              titleColumn="hostname"
              prependColumns={[
                {
                  title: ' ',
                  resizable: false,
                  width: 50,
                  key: 'currentStatus',
                  dataIndex: 'currentStatus',
                  order: -1,
                  sortable: false,
                  selectable: false,
                  render({ record }) {
                    return (
                      <LiveStatus status={record.currentStatus}>
                        <span className="mr-1" />
                      </LiveStatus>
                    );
                  }
                }
              ]}
              deleteFn={deleteInventoryHostApi}
              allowSelection
              key={`
                ${context.filter ? context.filter.title : 'none'}
                ${context.locationFilter ? context.locationFilter.title : 'none'}
                ${context.departmentFilter ? context.departmentFilter.title : 'none'}
                `}
              searchboxProps={{
                size: 'medium',
                style: {
                  width: '350px'
                }
              }}
              resourceTitle="Asset"
              expandable={{
                expandedRowRender: (record) => <HostExpandedRow asset={record} />,
                rowExpandable: (record) => true,
                expandIcon: ({ expanded, onExpand, record }) => (
                  <span className="text-label">
                    {expanded ? (
                      <DownOutlined className="text-label" onClick={(e) => onExpand(record, e)} />
                    ) : (
                      <RightOutlined className="text-label" onClick={(e) => onExpand(record, e)} />
                    )}
                  </span>
                )
              }}
              // createSlot={(_, { selectedItems, fetchData }) => (
              //   <>
              //     {selectedItems.length > 0 ? (
              //       <Button type="link" htmlType="button">
              //         <Icon name="delete" className="text-xl text-danger" />
              //       </Button>
              //     ) : null}
              //   </>
              // )}
              hasSearch
              fetchFn={(...args) =>
                getInventoryHostsApi(
                  ...args,
                  context.filter,
                  context.locationFilter,
                  context.departmentFilter
                )
              }
              updateFn={updateInventoryHostApi}
              onView={(item) => navigate(`${item.id}`)}
            />
          </div>
        </Location.Provider>
      </Department.Provider>
    </User.Provider>
  );
}
