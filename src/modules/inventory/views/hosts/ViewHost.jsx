import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Spin, Row, Col } from 'antd';
import { User } from '@components/pickers/UserPicker';
import { Department } from '@components/pickers/DepartmentPicker';
import { Location } from '@components/pickers/LocationPicker';
import { CrudProvider } from '@/src/hooks/crud';
import { ReactComponent as CrossIcon } from '@/src/icons/general/cross.svg';
import { ReactComponent as CheckIcon } from '@/src/icons/general/check.svg';
import { ReactComponent as Exclamation } from '@/src/icons/general/exclamation.svg';

import { useInventoryLayout } from '../../layout/InventoryLayout';
import {
  deleteInventoryHostApi,
  getHostsSummaryApi,
  updateInventoryHostApi
} from '../../api/hosts';
// import HostOverview from '../../components/hosts/HostOverview';
import HostDetail from '../../components/hosts/HostDetail';
import { getQuickCheckForHostApi } from '../../api/hosts';
import InvestigateDrawer from '../../components/hosts/InvestigateDrawer';
import PageHeading from '@/src/components/PageHeading';
import { getAssignableUsersApi } from '@/src/modules/settings/api/user-management/users';
import Icon from '@/src/components/Icon';
import { useAuth } from '@/src/hooks/auth';
import { useLayout } from '@/src/layouts/Layout';

export default function ViewHost() {
  const context = useInventoryLayout();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [asset, setAsset] = useState(null);
  const params = useParams();
  const [investigate, setInvestigate] = useState(null);
  const [isQuickCheckVisible, setIsQuickCheckVisible] = useState(false);
  const [quickCheckStats, setQuickCheckStats] = useState({});
  const [hasData, sethasData] = useState(false);
  const { setMasking } = useLayout();

  const { formatDateTime } = useAuth();

  useEffect(() => {
    context.displayNone();
    let timeout;
    const getApiFn = () =>
      getHostsSummaryApi(params.id).then((data) => {
        setAssetData(data);
        setLoading(false);
        if (timeout) {
          clearTimeout(timeout);
        }
        timeout = setTimeout(getApiFn, 20000);
      });

    timeout = setTimeout(getApiFn, 20000);

    return () => {
      context.displayBlock();
      clearTimeout(timeout);
    };
    // eslint-disable-next-line
  }, []);
  useEffect(() => {
    getHostsSummaryApi(params.id)
      .then((data) => {
        setAssetData(data);
        setLoading(false);
      })
      .catch(() => {
        navigate('/inventory/endpoints');
      });
    // eslint-disable-next-line
  }, [params]);

  function handleOnChange(data) {
    updateInventoryHostApi(data)
      .then(() => getHostsSummaryApi(params.id))
      .then((data) => setAssetData(data));
  }

  function handleDeleteAsset() {
    deleteInventoryHostApi(asset).then(() => {
      navigate('/inventory/endpoints');
    });
  }

  function setAssetData(data) {
    setAsset({ ...data, lastSeen: data.lastSeen ? formatDateTime(data.lastSeen) : '' });
  }
  return !loading && asset.id ? (
    <User.Provider apiFn={getAssignableUsersApi}>
      <Department.Provider>
        <Location.Provider>
          <div className="flex flex-col min-h-0 h-full">
            <PageHeading icon="host" title={`${asset.hostname}`} />

            <Row className="h-full flex-1 min-h-0 relative">
              <Col span={24} className="h-full flex flex-col min-h-0">
                <HostDetail
                  asset={asset}
                  onChange={handleOnChange}
                  onInvestigate={() => setInvestigate({})}
                  onDelete={() => handleDeleteAsset()}
                />
              </Col>
              <div
                className={`cursor-pointer px-2 py-1 border-solid rounded border-border ${
                  asset.currentStatus.toLowerCase() === 'offline'
                    ? 'bg-seperator'
                    : quickCheckStats.failed > 0
                    ? 'bg-danger'
                    : quickCheckStats.succeeded > 0
                    ? 'bg-success'
                    : 'bg-seperator'
                }`}
                onClick={() =>
                  setIsQuickCheckVisible((v) => {
                    setMasking(!v);
                    return !v;
                  })
                }
                style={{
                  position: 'absolute',
                  right: isQuickCheckVisible
                    ? hasData
                      ? 'calc(20.833333333333336% - 1rem)'
                      : 'calc(20.833333333333336% - 5rem)'
                    : '-1rem',
                  zIndex: isQuickCheckVisible ? 1001 : 99,
                  cursor: 'pointer',
                  top: '20%',
                  borderTopRightRadius: 0,
                  borderBottomRightRadius: 0
                }}>
                <span className="text-base text-white">
                  <Icon name="quick-check" />
                </span>
              </div>
              <Col
                span={5}
                style={{
                  right: '-1rem',
                  zIndex: 1001,
                  ...(isQuickCheckVisible ? {} : { display: 'none' }),
                  transition: 'width 0.3 linear'
                }}
                className="h-full flex flex-col absolute bg-seperator shadow-lg border-solid rounded border-border">
                <div className="flex flex-col h-full rounded-lg mb-2 mt-2">
                  <div className="p-2 bg-lightest rounded-lg">
                    <div className="mb-0 font-semibold text-base text-center">End Point Vital</div>
                  </div>
                  <div className="flex-1 flex flex-col min-h-0 overflow-auto">
                    <div className="flex flex-col flex-1 min-h-0">
                      <CrudProvider
                        resourceTitle="EndpointVital"
                        columns={[]}
                        hasSearch
                        autoRefresh={10000}
                        disablePagination
                        disableColumnSelection
                        tableSlot={({ rowKey, data }) => (
                          <>
                            {sethasData(!!data?.length)}
                            {data.length <= 0 ? (
                              <h4 className="text-center">No Data found!</h4>
                            ) : null}
                            {data.map((query) => (
                              <div
                                key={rowKey(query)}
                                className={`p-2 mb-2 rounded-lg shadow-lg bg-seperator widget-row droppable-element vital-status-box ${
                                  query.status === 'disable'
                                    ? 'error'
                                    : query.status === 'enable'
                                    ? 'success'
                                    : query.status === 'unknown'
                                    ? 'unknown-status'
                                    : ''
                                } ${
                                  asset.currentStatus.toLowerCase() === 'online' ? '' : 'offline'
                                }`}>
                                <div className="flex justify-between">
                                  <div className={`flex-1 flex items-center`}>
                                    <div className="mr-4 ml-2">
                                      {query.status === 'disable' ? (
                                        <div className="text-danger text-lg w-8 h-8 flex items-center justify-center">
                                          <CrossIcon className="w-full h-full" />
                                        </div>
                                      ) : query.status === 'enable' ? (
                                        <div className="text-danger text-lg w-8 h-8 flex items-center justify-center">
                                          <CheckIcon className="w-full h-full" />
                                        </div>
                                      ) : (
                                        <div className="text-danger text-lg w-8 h-8 flex items-center justify-center">
                                          <Exclamation className="w-full h-full" />
                                        </div>
                                      )}
                                    </div>
                                    <div className="font-semibold text-base cursor-pointer m-0">
                                      {query.name}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </>
                        )}
                        fetchFn={(...args) =>
                          getQuickCheckForHostApi(asset.id, ...args).then((data) => {
                            setQuickCheckStats({
                              total: data.totalCount,
                              failed: data.result.filter((r) => r.status === 'disable').length,
                              succeeded: data.result.filter((r) => r.status === 'enable').length
                            });
                            return data;
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
            {investigate ? (
              <InvestigateDrawer
                asset={asset}
                item={investigate}
                onClose={() => setInvestigate(null)}
              />
            ) : null}
          </div>
        </Location.Provider>
      </Department.Provider>
    </User.Provider>
  ) : (
    <Spin spinning={loading}>
      <div className="flex flex-col min-w-0 min-h-0 flex-1" />
    </Spin>
  );
}
