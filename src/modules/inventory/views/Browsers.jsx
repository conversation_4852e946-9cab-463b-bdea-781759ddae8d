import { <PERSON><PERSON>, Drawer } from 'antd';
import VulnerabilityDrawer from '../components/VulnerabilityDrawer';
import { useNavigate } from 'react-router-dom';
import { CrudProvider } from '@/src/hooks/crud';
import { getInventoryBrowsersApi, updateInventoryBrowserApi } from '../api/hosts';
import { useInventoryLayout } from '../layout/InventoryLayout';
import { useState } from 'react';
import PageHeading from '@/src/components/PageHeading';
// import VulnerabilityDot from '@/src/components/VulnerabilityDot';
import { getAssetsForSoftwareId } from '../../vulnerability/api/vulnerability-list';
// import { useEffect } from 'react';

export default function Browsers() {
  const context = useInventoryLayout();
  const [selectedPackage, setVulnerabilityView] = useState(null);
  const [assetsForSoftware, setShowAssetForSoftware] = useState(null);

  // useEffect(() => {
  //   context.hideMenu();

  //   return () => context.showMenu();
  // });

  const columns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   key: 'id'
    // },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version'
    },
    // {
    //   title: 'Release Date',
    //   dataIndex: 'releaseDate',
    //   key: 'releaseDate',
    //   sortable: false
    // },
    // {
    //   title: 'EOS',
    //   dataIndex: 'eosDate',
    //   key: 'eosDate',
    //   sortable: false
    // },
    // {
    //   title: 'EOL',
    //   dataIndex: 'eolDate',
    //   key: 'eolDate',
    //   sortable: false
    // },
    // {
    //   title: 'Vulnerability',
    //   dataIndex: 'vulnerability',
    //   key: 'vulnerability',
    //   render({ record, update }) {
    //     return (
    //       <Button type="link" onClick={() => setVulnerabilityView(record)}>
    //         {record.vulnerability}
    //       </Button>
    //     );
    //   }
    // },
    {
      title: 'Endpoints',
      dataIndex: 'asset_count',
      key: 'asset_count',
      render({ record }) {
        return (
          <Button type="link" onClick={() => setShowAssetForSoftware(record)}>
            {record.asset_count}
          </Button>
        );
      },
      align: 'center'
    }
    // {
    //   title: 'Blacklisted',
    //   dataIndex: 'blacklist',
    //   key: 'blacklist',
    //   sortable: false,
    //   render({ record, update }) {
    //     return (
    //       <Switch
    //         checked={record.blacklist}
    //         onChange={(blacklist) => update({ ...record, blacklist })}
    //       />
    //     );
    //   }
    // }
  ];

  const navigate = useNavigate();

  const assetColumns = [
    {
      title: 'Host Name',
      dataIndex: 'host_name',
      key: 'host_name',
      render({ record }) {
        return (
          <div className="flex items-center">
            {/* eslint-disable-next-line */}
            <a
              className="cursor-pointer"
              onClick={() => navigate(`/inventory/endpoints/${record.id}`)}>
              {record.hostname}
            </a>
          </div>
        );
      }
    },
    {
      title: 'Platform Version',
      dataIndex: 'platform_version',
      key: 'platform_version'
    }
  ];

  return (
    <>
      <PageHeading icon="inventory" title={`Browsers`} />
      <CrudProvider
        columns={columns}
        key={
          context.filter
            ? `${context.filter.title}
          -${context.filter.parent ? context.filter.parent : 'none'}`
            : '1'
        }
        resourceTitle="Browser"
        hasSearch
        // prependColumns={[
        //   {
        //     title: '',
        //     dataIndex: 'dot_id',
        //     key: 'dot_id',
        //     width: 50,
        //     sortable: false,
        //     render({ record }) {
        //       return (
        //         <VulnerabilityDot eolDate={record.eolDate} vulnerability={record.vulnerability} />
        //       );
        //     }
        //   }
        // ]}
        updateFn={updateInventoryBrowserApi}
        fetchFn={(...args) => getInventoryBrowsersApi(...args, context.filter)}
        onView={(item) => navigate(`${item.id}`)}
      />
      {selectedPackage ? (
        <VulnerabilityDrawer
          software={selectedPackage}
          closeDrawer={() => setVulnerabilityView(null)}
        />
      ) : null}
      <Drawer
        title={`
          Assets for Software ${assetsForSoftware ? assetsForSoftware.name : null}`}
        placement="right"
        width="50%"
        onClose={() => setShowAssetForSoftware(null)}
        destroyOnClose
        open={Boolean(assetsForSoftware)}>
        {Boolean(assetsForSoftware) && (
          <CrudProvider
            resourceTitle="Assets"
            hasSearch
            fetchFn={(...args) => getAssetsForSoftwareId(assetsForSoftware.id, ...args)}
            columns={assetColumns}
          />
        )}
      </Drawer>
    </>
  );
}
