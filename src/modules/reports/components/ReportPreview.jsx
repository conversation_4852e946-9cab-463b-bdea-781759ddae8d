import { useEffect, useState } from 'react';
import { Table } from 'antd';
import buildResult from '@/src/components/widget/views/result-builder';

export default function ReportPreview({ data }) {
  const [rows, setRows] = useState(null);
  const [columns, setColumns] = useState(null);
  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 20,
      showSizeChanger: true,
      showTotal: (total, range) => `showing ${range[0]}-${range[1]} of ${total} items`
    }
  });

  useEffect(() => {
    buildResultForDisplay();
    // eslint-disable-next-line
  }, [data]);

  function buildResultForDisplay() {
    const result = buildResult(data);
    setRows(result.data);
    setColumns(result.columns);
    setTableParams((prev) => ({
      ...prev,
      pagination: { ...prev.pagination, total: result.data.length }
    }));
  }

  const handleTableChange = (pagination, filters, sorter) => {
    setTableParams({
      ...tableParams,
      pagination: {
        ...tableParams.pagination,
        ...pagination
      },
      filters,
      sorter
    });
  };

  return (
    rows && (
      <div className="flex flex-1 min-h-0 flex-col">
        <Table
          className="crud-table"
          size="small"
          columns={columns.filter((c) => c.hidden !== true)}
          rowKey={(record) => record.guid}
          dataSource={rows}
          onChange={handleTableChange}
          {...tableParams}
        />
      </div>
    )
  );
}
