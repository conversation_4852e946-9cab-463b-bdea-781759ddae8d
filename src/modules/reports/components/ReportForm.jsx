import { useEffect, useMemo, useRef, useState } from 'react';
import FindIndex from 'lodash/findIndex';
import Merge from 'lodash/merge';
import Omit from 'lodash/omit';
import { EditOutlined, DownOutlined } from '@ant-design/icons';
import GridLayout from 'react-grid-layout';
import {
  Row,
  Col,
  Radio,
  Form,
  Button,
  Input,
  Select,
  Switch,
  Drawer,
  Space,
  ConfigProvider,
  Dropdown,
  theme
} from 'antd';
import CloneDeep from 'lodash/cloneDeep';
import { Link } from 'react-router-dom';
import Icon from '@/src/components/Icon';
import SchedulePicker from '@/src/components/pickers/SchedulePicker';
import RecipientsPicker from '@/src/components/pickers/RecipientsPicker';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';
import WidgetForm from '../../dashboard/components/WidgetForm';
import Repeater from '@/src/components/Repeater';
import generateId from '@/src/utils/id';
import WidgetTypePicker from '../../dashboard/components/WidgetTypePicker';
import { calculateNewItemPosition } from '../../dashboard/helpers/dashboard-helper';
import WidgetSelector from '../../dashboard/components/WidgetSelector';
import WidgetContainer from '@/src/components/widget/WidgetContainer';
import { Asset } from '@/src/components/pickers/AssetPicker';
import { Department } from '@/src/components/pickers/DepartmentPicker';

export default function ReportForm({ defaultValue, onSubmit, processing, disabled }) {
  const [form] = Form.useForm();
  const [width, setWidth] = useState(null);
  const [isWidgetSelectionDrawerOpen, setWidgetSelectionDrawerOpen] = useState(false);
  const containerRef = useRef(null);
  const [editingDataSource, setEditingDataSource] = useState(null);
  const [widgetPreviewKey, setWidgetPreviewKey] = useState(0);
  const layout = Form.useWatch('layout', form);

  const GRID_COLUMNS = 4;
  const ROW_HEIGHT = 180;

  const deliveryTypeOptions = [
    { label: 'Email', value: 'email' }
    // { label: 'Live', value: 'live' }
  ];

  useEffect(() => {
    if (containerRef.current) {
      setWidth(containerRef.current.offsetWidth);
    }
  }, [containerRef]);

  const widgetMap = useMemo(() => {
    return form.getFieldValue('widgets')?.reduce((acc, widget) => {
      acc[widget.guid] = widget;
      return acc;
    }, {});
    // eslint-disable-next-line
  }, [form.getFieldValue('widgets')]);

  const timeline = Form.useWatch('timeline', form);

  function resetForm() {
    form.resetFields();
  }

  function handleDataSourceUpdated(updatedData) {
    let data = { ...editingDataSource, ...updatedData, timeline: form.getFieldValue('timeline') };
    let index = FindIndex(form.getFieldValue('widgets'), { guid: data.guid });
    if (index !== -1) {
      let widgets = [...form.getFieldValue('widgets')];
      widgets[index] = { ...data };
      form.setFieldsValue({ widgets });
    } else {
      let widgets = [...form.getFieldValue('widgets')];
      widgets.push({ ...data });
      form.setFieldsValue({ widgets });
    }
    index = FindIndex(form.getFieldValue('layout'), { i: data.guid });
    if (index === -1) {
      handleAppendWidget(data);
    }
    setWidgetPreviewKey(0);
    setEditingDataSource(null);
  }

  function handleEditDataSource(data) {
    if (widgetPreviewKey === 0) {
      setWidgetPreviewKey(data.query ? 1 : 0);
    }
    setEditingDataSource((existingData) => ({ ...existingData, ...data }));
  }

  function handleLayoutUpdate(layout) {
    form.setFieldsValue({ layout });
  }

  function handleAppendWidget(widget) {
    let layout = form.getFieldValue('layout') || [];
    let widgetSize = { h: 1, w: (widget.type || '').toLowerCase() === 'grid' ? 4 : 2 };
    const widgetPosition = calculateNewItemPosition(widgetSize, layout, 2);

    handleLayoutUpdate([
      ...(form.getFieldValue('layout') || []),
      {
        ...widgetPosition,
        ...widgetSize,
        minW: (widget.type || '').toLowerCase() === 'grid' ? 4 : 1,
        maxH: (widget.type || '').toLowerCase() === 'grid' ? 1 : undefined,
        minH: 1,
        i: widget.guid
      }
    ]);
  }

  function handleRemoveDataSource(data) {
    let index = FindIndex(form.getFieldValue('widgets'), { guid: data.guid });
    if (index !== -1) {
      let widgets = [...form.getFieldValue('widgets')];
      widgets.splice(index, 1);
      form.setFieldsValue({ widgets });
    }
    index = FindIndex(form.getFieldValue('layout'), { i: data.guid });
    if (index !== -1) {
      let layout = [...form.getFieldValue('layout')];
      layout.splice(index, 1);
      handleLayoutUpdate(layout);
    }
  }

  return (
    <Asset.Provider>
      <Department.Provider>
        <div className="flex flex-col h-full flex-1 min-h-0">
          <div className="flex items-center">
            <Link to=".." className="mr-2 text-lg">
              <Icon name="arrow-left" />
            </Link>
            <h1 className="text-primary my-4 text-lg">
              {defaultValue.id ? (disabled ? 'View' : 'Edit') : 'Create'} Report
            </h1>
          </div>
          <div className="py-2 flex-1 flex flex-col min-h-0 mb-2">
            <Form
              layout="vertical"
              disabled={disabled}
              form={form}
              className="h-full flex flex-col min-h-0"
              onFinish={onSubmit}
              initialValues={CloneDeep(defaultValue)}>
              <Row gutter={16} className="flex-1 min-h-0">
                <Col span={10} className="h-full overflow-y-auto overflow-x-hidden">
                  <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                    <Input placeholder="Report Name" />
                  </Form.Item>
                  <Form.Item label="Description" name="description">
                    <Input.TextArea placeholder="Description" />
                  </Form.Item>
                  <Form.Item label="Timeline" name={'timeline'} rules={[{ required: true }]}>
                    <TimelinePicker />
                  </Form.Item>
                  <Row>
                    <Col span={24}>
                      <Form.Item>
                        <Repeater
                          disabled={disabled}
                          name="widgets"
                          itemLabel="Widget"
                          addSectionRender={({ add, defaultItem, ...props }) => {
                            return (
                              <Dropdown
                                menu={{
                                  items: [
                                    {
                                      label: 'Create New',
                                      key: 'create'
                                    },
                                    {
                                      label: 'Choose Existing Widget',
                                      key: 'existing'
                                    }
                                  ],
                                  onClick: (e) => {
                                    if (e.key === 'existing') {
                                      setWidgetSelectionDrawerOpen(true);
                                    } else {
                                      let data = defaultItem();
                                      setEditingDataSource(data);
                                    }
                                  }
                                }}>
                                <Button>
                                  <Space>
                                    <Icon name="add" /> Add New Widget
                                    <DownOutlined />
                                  </Space>
                                </Button>
                              </Dropdown>
                            );
                          }}
                          rules={[
                            {
                              validator: async (_, names) => {
                                if (!names || names.length < 1) {
                                  return Promise.reject(new Error('At least 1 Widget is required'));
                                }
                              }
                            }
                          ]}
                          onAddItem={(item) => setEditingDataSource(item)}
                          defaultItem={() => ({
                            type: 'Grid',
                            guid: generateId(),
                            widgetDataFetchingType: 'query_based',
                            widgetLiveOrOffline: 'Offline',
                            name: `Widget ${(form.getFieldValue('widgets')?.length || 0) + 1}`
                          })}>
                          {({ key, name }) => (
                            <div className="flex flex-col mb-4" key={key}>
                              <div className="flex items-center">
                                <div
                                  className={`flex-1 px-2 py-1 bg-seperator rounded-lg widget-row droppable-element`}>
                                  <Row className="items-center">
                                    <Col span={24} className="">
                                      <div className="pr-2">
                                        <div className="p-2 mb-4">
                                          <div className="flex justify-between">
                                            <div className="icon mr-2">
                                              <WidgetTypePicker
                                                value={form.getFieldValue([
                                                  'widgets',
                                                  name,
                                                  'type'
                                                ])}
                                                onlyImage
                                              />
                                            </div>
                                            <div className="flex-1">
                                              <h4 className="m-0">
                                                {form.getFieldValue(['widgets', name, 'name'])}
                                              </h4>
                                              <small
                                                className="text-label"
                                                style={{
                                                  overflow: 'hidden',
                                                  display: '-webkit-box',
                                                  WebkitLineClamp: 2,
                                                  lineClamp: 2,
                                                  WebkitBoxOrient: 'vertical'
                                                }}>
                                                {form.getFieldValue([
                                                  'widgets',
                                                  name,
                                                  'description'
                                                ])}
                                              </small>
                                            </div>
                                            {!disabled ? (
                                              <div className="flex items-center shrink-0">
                                                <span
                                                  className="cursor-pointer text-lg text-primary mr-2"
                                                  title="Edit Widget"
                                                  onClick={() =>
                                                    handleEditDataSource(
                                                      form.getFieldValue(['widgets', name])
                                                    )
                                                  }>
                                                  <EditOutlined />
                                                </span>
                                                <span
                                                  className="cursor-pointer text-lg text-danger"
                                                  title="Remove Widget"
                                                  onClick={() => {
                                                    handleRemoveDataSource(
                                                      form.getFieldValue(['widgets', name])
                                                    );
                                                  }}>
                                                  <Icon name="close" className="text-danger" />
                                                </span>
                                              </div>
                                            ) : null}
                                          </div>
                                        </div>
                                      </div>
                                    </Col>
                                  </Row>
                                </div>
                              </div>
                            </div>
                          )}
                        </Repeater>
                      </Form.Item>
                    </Col>
                    <Col span={24}>
                      <Row gutter={0}>
                        <Col span={8}>
                          <Form.Item
                            label="Enable Report Schedule"
                            name="isScheduled"
                            valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Form.Item noStyle shouldUpdate>
                        {({ getFieldValue }) => {
                          return getFieldValue('isScheduled') ? (
                            <>
                              <Row gutter={0}>
                                <Col span={4}>
                                  <Form.Item
                                    label="Export Type"
                                    name="exportAs"
                                    rules={[{ required: true }]}>
                                    <Radio.Group
                                      size="medium"
                                      optionType="button"
                                      buttonStyle="solid">
                                      <Radio value="pdf">PDF</Radio>
                                      {/* <Radio value="xlsx" disabled>
                                    XLSX
                                  </Radio> */}
                                    </Radio.Group>
                                  </Form.Item>
                                </Col>
                              </Row>
                              <Row gutter={0}>
                                <Col span={24}>
                                  <Form.Item label="" name="schedule" className="mb-0">
                                    <SchedulePicker gutter={0} />
                                  </Form.Item>
                                </Col>
                              </Row>
                              <Row gutter={0}>
                                <Col span={8} className="pr-2">
                                  <Form.Item
                                    label="Delivery"
                                    name="deliveryType"
                                    rules={[{ required: true }]}>
                                    <Select options={deliveryTypeOptions} />
                                  </Form.Item>
                                </Col>
                                <Col span={16} className="pl-2">
                                  <Form.Item
                                    label="Recipients"
                                    name="recipients"
                                    rules={[{ required: true }]}>
                                    <RecipientsPicker />
                                  </Form.Item>
                                </Col>
                              </Row>
                            </>
                          ) : null;
                        }}
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>
                <Col
                  span={14}
                  className="h-full border-solid border-border border-l-1 border-r-0 border-t-0 border-b-0 rounded">
                  <div className="flex flex-col h-full">
                    <h3 className="text-center font-semibold">Report Preview</h3>
                    <Form.Item name="layout" noStyle></Form.Item>
                    <ConfigProvider
                      componentSize={'small'}
                      theme={{
                        algorithm: theme.defaultAlgorithm
                      }}>
                      <div
                        data-theme="light-theme"
                        className="flex flex-col flex-1 min-h-0 overflow-y-auto overflow-x-hidden bg-page-background text-color rounded">
                        <div className="pr-2" ref={containerRef}>
                          {width && (
                            <GridLayout
                              className="layout"
                              containerPadding={[10, 10]}
                              layout={
                                disabled
                                  ? layout.map((i) => ({
                                      ...i,
                                      isDraggable: false,
                                      isResizable: false
                                    }))
                                  : layout
                              }
                              width={width}
                              disabled={disabled}
                              rowHeight={ROW_HEIGHT}
                              cols={GRID_COLUMNS}
                              margin={[8, 8]}
                              compactType="vertical"
                              onLayoutChange={handleLayoutUpdate}>
                              {(layout || []).map((item) => (
                                <div key={item.i} className="widget-container">
                                  {widgetMap[item.i].type.toLowerCase() === 'gauge' ? null : (
                                    <div className="px-2">{widgetMap[item.i].name}</div>
                                  )}
                                  <div className="flex flex-col min-h-0 flex-1">
                                    <div className="flex flex-col h-full overflow-auto min-h-0">
                                      <WidgetContainer
                                        isPreview
                                        isReport
                                        timeline={timeline}
                                        widget={widgetMap[item.i]}
                                      />
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </GridLayout>
                          )}
                        </div>
                      </div>
                    </ConfigProvider>
                  </div>
                </Col>
              </Row>
              <Row gutter={16} className="mt-2">
                {!disabled && (
                  <Col span={10} className="text-right">
                    <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                      {defaultValue.id ? 'Update' : 'Create'}
                    </Button>
                    {/* <Button
                  type="primary"
                  ghost
                  htmlType="button"
                  className="mr-2"
                  onClick={() => {
                    form.validateFields(['query_context'], { recursive: true }).then((result) => {
                      setQueryToUse({
                        ...transformQueryForServer(form.getFieldValue(['query_context'])),
                        ...transformAssetScopeForServer(form.getFieldsValue())
                      });
                      setPreviewKey((key) => key + 1);
                    });
                  }}>
                  Generate Preview
                </Button> */}
                    <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
                      Reset
                    </Button>
                  </Col>
                )}
              </Row>
            </Form>
            <Drawer
              open={Boolean(editingDataSource)}
              onClose={() => setEditingDataSource(null)}
              width="95%"
              destroyOnClose
              title={'Edit Widget'}
              maskClosable={false}>
              <Form
                layout="vertical"
                className="h-full"
                onFinish={handleDataSourceUpdated}
                onValuesChange={(values) => {
                  setEditingDataSource(Merge({ ...editingDataSource }, { ...values }));
                }}
                initialValues={editingDataSource}>
                <WidgetForm
                  hideResetButton
                  submitBtnLabel="Update"
                  value={editingDataSource || {}}
                  disabledTimeline
                  shouldRenderPreview={widgetPreviewKey > 0}
                  previewRenderKey={widgetPreviewKey}
                  widgetContextBuilderFn={(data) => {
                    data.timeline = form.getFieldValue('timeline');
                    return data;
                  }}
                  onRerenderPreview={(data = {}) => {
                    if (data.query) {
                      setWidgetPreviewKey((key) => key + 1);
                    }
                  }}
                  onChange={(data) => {
                    setEditingDataSource((existingData) => ({ ...existingData, ...data }));
                  }}
                />
              </Form>
            </Drawer>
            <Drawer
              title="Select Widget"
              placement="right"
              width="50%"
              mask={false}
              onClose={() => setWidgetSelectionDrawerOpen(false)}
              open={isWidgetSelectionDrawerOpen}>
              <WidgetSelector
                disabled
                onAppendWidget={(widget) => {
                  setWidgetSelectionDrawerOpen(false);
                  let data = Omit(widget, ['id']);
                  data.guid = generateId();
                  form.setFieldsValue({ widgets: [...form.getFieldValue('widgets'), data] });
                  handleAppendWidget(data);
                }}
              />
            </Drawer>
          </div>
        </div>
      </Department.Provider>
    </Asset.Provider>
  );
}
