import { Link } from 'react-router-dom';
import { Button, Progress } from 'antd';
import FindIndex from 'lodash/findIndex';
import { DownloadOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { CrudProvider } from '@/src/hooks/crud';
import { User } from '@/src/components/pickers/UserPicker';
import {
  createReportApi,
  deleteReportApi,
  downloadReportApi,
  getAllReportsApi,
  updateReportA<PERSON>
} from '../api/reports';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { useEffect } from 'react';
import Bus from '@/src/utils/emitter';
import { useAuth } from '@/src/hooks/auth';
import { useLayout } from '@/src/layouts/Layout';

export default function ReportsList() {
  const layout = useLayout();

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render({ record }) {
        return <Link to={`/reports/${record.id}`}>{record.name}</Link>;
      }
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      sortable: false,
      ellipsis: true
    },
    {
      title: 'Download',
      dataIndex: 'download',
      key: 'download',
      sortable: false,
      align: 'center',
      render({ record }) {
        return record.progress ? (
          <Progress type="circle" percent={record.progress} size={40} />
        ) : (
          <Button type="primary" onClick={() => downloadReport(record)}>
            <DownloadOutlined />
          </Button>
        );
      }
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      render({ record }) {
        return <User.Picker textOnly value={record.createdBy} disabled />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Report],
      deletePermissions: [constants.Delete_Report]
    }
  ];

  const { token } = useAuth();

  const navigate = useNavigate();

  let generationId = [];

  function onReportFileReceived(item) {
    // eslint-disable-next-line
    if (generationId.includes(item.uuid)) {
      generationId = generationId.filter((i) => i !== item.uuid);
      window.open(`/api/download?file-name=${item.result}&mid=${token.access_token}`);
    }
  }

  useEffect(() => {
    Bus.on('report_export', onReportFileReceived);

    return () => Bus.off('report_export', onReportFileReceived);
    // eslint-disable-next-line
  }, []);

  function downloadReport(record) {
    return downloadReportApi(record).then((response) => {
      generationId.push(record.uuid);
      layout.notification.success({
        message: 'Report Export!',
        description: 'Report export is queued, we will notify you once the report is ready!'
      });
    });
  }

  return (
    <User.Provider>
      <CrudProvider
        columns={columns}
        initCrud={({ updateSingeItem, getCurrentData }) => {
          const listener = (event) => {
            const data = getCurrentData();
            const dataItemIndex = FindIndex(data, {
              id: event.reportId
              // uuid: event.uuid
            });
            if (dataItemIndex !== -1) {
              updateSingeItem({
                ...data[dataItemIndex],
                progress: event.progress < 100 ? event.progress : null
              });
            }
          };
          Bus.on('report_progress', listener);
          return () => {
            Bus.off('report_progress', listener);
          };
        }}
        defaultFormItem={{
          exportAs: 'pdf',
          deliveryType: 'email'
        }}
        resourceTitle="Report"
        hasSearch
        fetchFn={getAllReportsApi}
        deleteFn={deleteReportApi}
        createFn={createReportApi}
        updateFn={updateReportApi}
        onEdit={(item) => navigate(`/reports/${item.id}/edit`)}
        createSlot={() => (
          <PermissionChecker permission={constants.Create_Report}>
            <Button type="primary" onClick={() => navigate('/reports/create')}>
              Create
            </Button>
          </PermissionChecker>
        )}
      />
    </User.Provider>
  );
}
