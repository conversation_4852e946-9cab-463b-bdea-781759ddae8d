import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { createReportApi } from '../api/reports';
import ReportForm from '../components/ReportForm';
import { User } from '@/src/components/pickers/UserPicker';

export default function CreateReport() {
  const navigate = useNavigate();
  const [processing, setProcessing] = useState(false);

  function handleSubmit(values) {
    setProcessing(true);
    createReportApi(values)
      .then(() => navigate('/reports'))
      .finally(() => setProcessing(false));
  }

  return (
    <User.Provider>
      <ReportForm
        processing={processing}
        onSubmit={handleSubmit}
        defaultValue={{
          type: 'offline',
          deliveryType: 'email',
          exportAs: 'pdf',
          widgets: [],
          layout: [],
          timeline: {
            selected: 'Today'
          }
        }}
      />
    </User.Provider>
  );
}
