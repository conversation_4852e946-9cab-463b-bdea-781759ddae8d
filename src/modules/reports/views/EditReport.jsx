import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Spin } from 'antd';
import { getReportApi, updateReportApi } from '../api/reports';
import ReportForm from '../components/ReportForm';
import { User } from '@/src/components/pickers/UserPicker';

export default function EditReport() {
  const navigate = useNavigate();
  const [processing, setProcessing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [defaultValue, setDefaultValue] = useState(null);
  const params = useParams();

  useEffect(() => {
    getReportApi(params.id)
      .then((data) => {
        setDefaultValue(data);
        setLoading(false);
      })
      .catch(() => {
        navigate('/reports');
      });
    // eslint-disable-next-line
  }, [params]);

  function handleSubmit(values) {
    values.id = defaultValue.id;
    setProcessing(true);
    updateReportApi(values)
      .then(() => navigate('/reports'))
      .finally(() => setProcessing(false));
  }

  return !loading && defaultValue.id ? (
    <User.Provider>
      <ReportForm processing={processing} onSubmit={handleSubmit} defaultValue={defaultValue} />
    </User.Provider>
  ) : (
    <Spin spinning={loading} className="flex flex-col min-h-0">
      <div className="flex flex-col min-w-0 min-h-0 flex-1" />
    </Spin>
  );
}
