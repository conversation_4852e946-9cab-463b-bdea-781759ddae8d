import { But<PERSON>, Row, Col, Form, Input, Select } from 'antd';
import Capitalize from 'lodash/capitalize';
import Words from 'lodash/words';
import { CrudProvider } from '@/src/hooks/crud';
import { User } from '@/src/components/pickers/UserPicker';
import {
  createRootkitRuleApi,
  deleteRootkitRuleApi,
  getAllRootkitRulesApi,
  updateRootkitRuleApi
} from '../api/rootkit';
import IntervalInput from '@/src/components/IntervalInput';
import constants from '@/src/constants/index';
import PermissionChecker from '@/src/components/PermissionChecker';

export default function RootkitRules() {
  const platformOptions = ['Linux', 'Mac', 'Windows'].map((i) => ({
    label: i,
    value: i.toLowerCase()
  }));

  const detectionRuleOptions = ['file_path'].map((i) => ({
    label: Words(i)
      .map((i) => Capitalize(i))
      .join(' '),
    value: i
  }));

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Platform',
      dataIndex: 'platform',
      key: 'platform'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      sortable: false,
      ellipsis: true
    },
    {
      title: 'Detection Rule',
      dataIndex: 'detectionRule',
      key: 'detectionRule',
      render({ record }) {
        return Words(record.detectionRule)
          .map((i) => Capitalize(i))
          .join(' ');
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Root_Kit],
      deletePermissions: [constants.Delete_Root_Kit]
    }
  ];

  return (
    <User.Provider>
      <CrudProvider
        columns={columns}
        defaultFormItem={{
          detectionRule: 'file_path',
          interval: {}
        }}
        resourceTitle="Rootkit Rule"
        hasSearch
        fetchFn={getAllRootkitRulesApi}
        deleteFn={deleteRootkitRuleApi}
        createFn={createRootkitRuleApi}
        updateFn={updateRootkitRuleApi}
        createSlot={(createFn) => (
          <PermissionChecker permission={constants.Create_Root_Kit}>
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          </PermissionChecker>
        )}
        formFields={(item) => (
          <>
            <Row gutter={32}>
              <Col span={12}>
                <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                  <Input placeholder="Name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Platform" name="platform" rules={[{ required: true }]}>
                  <Select options={platformOptions} placeholder="Platform" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Detection Rule" name="detectionRule" rules={[{ required: true }]}>
                  <Select options={detectionRuleOptions} placeholder="Detection Rule" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Interval" name="interval" rules={[{ required: true }]}>
                  <IntervalInput />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="File Paths" name="filePaths" rules={[{ required: true }]}>
                  <Select mode="tags" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Description" name="description">
                  <Input.TextArea placeholder="Description" />
                </Form.Item>
              </Col>
            </Row>
          </>
        )}
      />
    </User.Provider>
  );
}
