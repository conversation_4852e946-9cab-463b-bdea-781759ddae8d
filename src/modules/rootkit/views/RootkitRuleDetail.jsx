import { <PERSON>rud<PERSON>rovider } from '@/src/hooks/crud';
import { getAllRootkitRuleResultsApi } from '../api/rootkit';
import { Asset } from '@/src/components/pickers/AssetPicker';

export default function RootkitRuleDetail() {
  const columns = [
    {
      title: 'Endpoint',
      dataIndex: 'asset_name',
      key: 'asset_name',
      render({ record }) {
        return <Asset.Picker value={record.asset_name} disabled textOnly linkToAsset resolveId />;
      }
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Platform',
      dataIndex: 'platform',
      key: 'platform'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      sortable: false,
      ellipsis: true
    },
    {
      title: 'File Name',
      dataIndex: 'filename',
      key: 'filename'
    },
    {
      title: 'File Path',
      dataIndex: 'path',
      key: 'path',
      ellipsis: true
    },
    {
      title: 'PID',
      dataIndex: 'pid_with_namespace',
      key: 'pid_with_namespace',
      ellipsis: true
    },
    {
      title: 'Original File Name',
      dataIndex: 'original_filename',
      key: 'original_filename'
    },
    {
      title: 'Mount Namespace ID',
      dataIndex: 'mount_namespace_id',
      key: 'mount_namespace_id',
      hidden: true
    },
    {
      title: 'UID',
      dataIndex: 'uid',
      key: 'uid',
      hidden: true
    },
    {
      title: 'GID',
      dataIndex: 'gid',
      key: 'gid',
      hidden: true
    },
    {
      title: 'Size',
      dataIndex: 'size',
      key: 'size',
      hidden: true
    },
    {
      title: 'B Time',
      dataIndex: 'btime',
      key: 'btime',
      hidden: true
    },
    {
      title: 'A Time',
      dataIndex: 'atime',
      key: 'atime',
      hidden: true
    },
    {
      title: 'M Time',
      dataIndex: 'mtime',
      key: 'mtime',
      hidden: true
    },
    {
      title: 'C Time',
      dataIndex: 'ctime',
      key: 'ctime',
      hidden: true
    },
    {
      title: 'Symlink',
      dataIndex: 'symlink',
      key: 'symlink',
      hidden: true
    },
    {
      title: 'Created On',
      dataIndex: 'created_time',
      key: 'created_time',
      type: 'datetime'
    }
  ];

  return (
    <Asset.Provider>
      <CrudProvider
        columns={columns}
        resourceTitle="Rootkit Rule Result"
        hasSearch
        fetchFn={getAllRootkitRuleResultsApi}
      />
    </Asset.Provider>
  );
}
