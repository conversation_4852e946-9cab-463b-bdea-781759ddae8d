import api from '@api';

const END_POINT = `/rootkit/rule`;

const transform = (item) => ({
  id: item.id,
  name: item.name,
  platform: item.platform,
  description: item.description,
  detectionRule: item.detection_rule,
  filePaths: item.file_paths.split(','),
  interval: {
    value: +item.interval,
    unit: item.interval_unit
  },
  createdAt: item.created_time
});

const transformForServer = async (item) => {
  return Promise.resolve({
    id: item.id,
    name: item.name,
    platform: item.platform,
    description: item.description,
    detection_rule: item.detectionRule,
    file_paths: item.filePaths.join(','),
    interval: (item.interval || {}).value,
    interval_unit: (item.interval || {}).unit
  });
};

const transformRootkitResult = (item) => {
  return {
    id: item.id,
    name: item.name,
    description: item.description,
    filename: item.filename,
    platform: item.platform,
    path: item.path,
    uid: item.uid,
    gid: item.gid,
    size: item.size,
    btime: item.btime,
    atime: item.atime,
    mtime: item.mtime,
    ctime: item.ctime,
    symlink: item.symlink,
    pid_with_namespace: item.pid_with_namespace,
    original_filename: item.original_filename,
    mount_namespace_id: item.mount_namespace_id,
    created_time: item.created_time,
    asset_name: item.asset_name
  };
};

const sortKeyMap = {
  name: 'name',
  detectionRule: 'detection_rule'
};

const searchableColumns = ['name', 'description', 'platform', 'file_paths', 'detection_rule'];

export function getAllRootkitRulesApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getRootkitRuleApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateRootkitRuleApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getRootkitRuleApi(data.result));
}

export function createRootkitRuleApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getRootkitRuleApi(data.result));
}

export function deleteRootkitRuleApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}

const resultSearchableColumns = [
  'name',
  'description',
  'platform',
  'filename',
  'path',
  'pid_with_namespace',
  'original_filename',
  'mount_namespace_id'
];

export function getAllRootkitRuleResultsApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/result`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? resultSearchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transformRootkitResult)
      };
    });
}
