import api from '@api';
import {
  transformAssetScope,
  transformAssetScopeForServer
} from '@/src/components/pickers/AssetScopePicker';

const END_POINT = `/software/block-executable`;

function transform(item) {
  const context = item.context || {};
  const isBrowseExecutable =
    context.input_type === 'Browse Executable' && item.rule_type === 'hash';
  return {
    id: item.id,
    name: item.application_name,
    description: item.description,
    rule_type: item.rule_type,
    application_action: item.application_action,
    ...transformAssetScope(item),
    executable_name: isBrowseExecutable
      ? [
          {
            ref: context.executable_ref,
            name: context.executable_name
          }
        ]
      : context.executable_name,
    input_type: context.input_type,
    version: context.version,
    file_size: context.file_size,
    md5_hash: context.md5_hash,
    sha_256_hash: context.sha_256_hash,
    executable_ref: context.executable_ref,
    modified_time: item.modified_time,
    created_time: item.created_time,
    executable_name_view: context.executable_name

    // executable_name: context.executable_name
  };
}
const transformForServer = async (item) => {
  const isBrowseExecutable = item.input_type === 'Browse Executable' && item.rule_type === 'hash';
  const isPath = item.rule_type === 'path';

  return Promise.resolve({
    id: item.id,
    application_name: item.name,
    description: item.description,
    rule_type: item.rule_type,
    application_action: item.application_action,
    ...transformAssetScopeForServer(item),

    context: {
      executable_name: isBrowseExecutable
        ? item.executable_name[0].response.name
        : item.executable_name,
      executable_ref: isBrowseExecutable ? item.executable_name[0].response.ref : undefined,

      ...(!isPath ? { input_type: item.input_type } : {}),

      version: item.version,
      file_size: item.file_size,
      md5_hash: item.md5_hash,
      sha_256_hash: item.sha_256_hash
    }
  });
};

const sortKeyMap = {
  name: 'application_name',
  description: 'description',
  created_time: 'created_time'
};

const searchableColumns = ['application_name', 'description', 'context'];

export function getAllApplicationControlApi(offset, size, sortFilter, { category }) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: (category
        ? [
            {
              operator: 'Equals',
              column: 'refModel',
              value: category
            }
          ]
        : []
      ).concat(
        sortFilter.searchTerm
          ? searchableColumns.map((c) => ({
              operator: 'Contains',
              column: c,
              value: sortFilter.searchTerm
            }))
          : []
      )
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: (data.result || []).map(transform)
      };
    });
}

export function getApplicationControlApi(id) {
  return api.get(`${END_POINT}/${id}`).then((result) => {
    return transform(result.result);
  });
}

export function updateApplicationControlApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getApplicationControlApi(data.result));
}

export function bulkDeleteApplicationControlApi(ids) {
  return api.delete(`${END_POINT}`, {
    data: { ids }
  });
}

export function createApplicationControlApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getApplicationControlApi(data.result));
}

export function deleteApplicationControlApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}

export function getMD5HashApi(name) {
  return api.get(`software/block-executable/file/details/${name}`).then((res) => {
    return res.result || {};
  });
}
