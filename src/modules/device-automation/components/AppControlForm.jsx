// import PackageUploader from '@/src/components/PackageUploader';

// import PlatformPicker from '@/src/components/pickers/PlatformPicker';
import { Form, Input, Row, Col, Radio, Button } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';
import Uploader from '@/src/components/Uploader';
import { getMD5HashApi } from '../app-control';

export default function AppControlForm({ item = {} }) {
  const form = Form.useFormInstance();

  const isPath = form.getFieldValue('rule_type') === 'path';
  const isBrowseExecutable = form.getFieldValue('input_type') === 'Browse Executable';

  function getMD5Hash(res) {
    return getMD5HashApi(res?.ref).then((res) => {
      form.setFieldValue('file_size', res.file_size);
      form.setFieldValue('md5_hash', res.md5_hash);
      form.setFieldValue('sha_256_hash', res.sha_256_hash);
    });
  }

  return (
    <div className="flex flex-col min-h-0 h-full overflow-y-auto overflow-x-hidden">
      <Row gutter={32}>
        <Col span={24}>
          <Form.Item label="Application Name" name="name" rules={[{ required: true }]}>
            <Input placeholder="Name" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Description" name="description">
            <Input.TextArea placeholder="Description" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label=" " noStyle name="scope" rules={[{ required: true }]}>
            <AssetScopePicker
              label="Scope"
              gutter={16}
              skipProvider={true}
              name={['scope', 'assetFilter']}
              subname={['scope', 'assets']}
              disabledScopeOptions={[5, 6, 7, 8]}
            />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item label="Action" name="application_action" rules={[{ required: true }]}>
            <Radio.Group optionType="button" buttonStyle="solid" size="default">
              <Radio value="Allow">Allow</Radio>
              <Radio value="Block">Deny</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Rule Type" name="rule_type" rules={[{ required: true }]}>
            <Radio.Group
              optionType="button"
              buttonStyle="solid"
              size="default"
              onChange={(value) => {
                form.setFieldValue('executable_name', undefined);
                form.setFieldValue('file_size', undefined);
                form.setFieldValue('md5_hash', undefined);
                form.setFieldValue('sha_256_hash', undefined);
              }}>
              <Radio value="path">Path</Radio>
              <Radio value="hash">Hash</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>

        {isPath ? (
          <>
            <Col span={12}>
              <Form.Item
                label="Executable Name"
                name="executable_name"
                rules={[{ required: true }]}>
                <Input placeholder="*.exe / *.msi / *.bat" />
              </Form.Item>
            </Col>
          </>
        ) : (
          <>
            <Col span={24}>
              <Form.Item label="Input Type " name="input_type" rules={[{ required: true }]}>
                <Radio.Group
                  size="default"
                  optionType="button"
                  buttonStyle="solid"
                  onChange={(value) => {
                    form.setFieldValue('file_size', undefined);
                    form.setFieldValue('md5_hash', undefined);
                    form.setFieldValue('sha_256_hash', undefined);
                    form.setFieldValue('executable_name', undefined);
                  }}>
                  <Radio value="Browse Executable">Browse Executable</Radio>
                  <Radio value="Enter Hash Value">Enter Hash Value</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Version" name="version" rules={[{ required: true }]}>
                <Input placeholder="version" />
              </Form.Item>
            </Col>

            {isBrowseExecutable ? (
              <Col span={12}>
                <Form.Item
                  label="Browse Executable"
                  name="executable_name"
                  rules={[{ required: true }]}>
                  <Uploader
                    fileField
                    maxCount={1}
                    onChange={(item) => {
                      if (item[0]?.response) {
                        setTimeout(() => {
                          getMD5Hash(item[0]?.response);
                        });
                      }
                    }}>
                    <Button icon={<UploadOutlined />}>Select File</Button>
                  </Uploader>
                </Form.Item>
              </Col>
            ) : (
              <Col span={12}>
                <Form.Item
                  label="Executable Name"
                  name="executable_name"
                  rules={[{ required: true }]}>
                  <Input placeholder="*.exe / *.msi / *.bat" />
                </Form.Item>
              </Col>
            )}

            <Col span={12}>
              <Form.Item label="File Size" name="file_size" rules={[{ required: true }]}>
                <Input
                  placeholder="File Size"
                  disabled={isBrowseExecutable}
                  addonAfter={isBrowseExecutable ? undefined : 'Bytes'}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="MD5 Hash " name="md5_hash" rules={[{ required: true }]}>
                <Input placeholder="32 Character String" disabled={isBrowseExecutable} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="SHA-256 Hash" name="sha_256_hash" rules={[{ required: true }]}>
                <Input placeholder="64 Character String" disabled={isBrowseExecutable} />
              </Form.Item>
            </Col>
          </>
        )}
      </Row>
    </div>
  );
}
