import { useEffect } from 'react';
import { Button, Tag } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
// import PermissionChecker from '@/src/components/PermissionChecker';
// import constants from '@/src/constants/index';
import {
  getAllApplicationControlApi,
  createApplicationControlApi,
  updateApplicationControlApi,
  deleteApplicationControlApi
} from '../app-control';
import AppControlForm from '../components/AppControlForm';
import { useInventoryLayout } from '../layout/DeviceAutomationLayout';
import { Asset } from '@/src/components/pickers/AssetPicker';
import PageHeading from '@/src/components/PageHeading';
import Status from '@/src/components/Status';
import { Department } from '@/src/components/pickers/DepartmentPicker';

// import Icon from '@/src/components/Icon';

export default function AppControl({ type }) {
  const layout = useInventoryLayout();

  useEffect(() => {
    layout.hideMenu();

    return () => layout.showMenu();
    // eslint-disable-next-line
  }, []);

  const columns = [
    {
      title: 'Application',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link',
      sortable: false
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      sortable: false
    },
    {
      title: 'Executable',
      dataIndex: 'executable_name_view',
      key: 'executable_name_view',
      sortable: false
    },
    {
      title: 'Rule Type',
      dataIndex: 'rule_type',
      key: 'rule_type',
      sortable: false,
      render({ record }) {
        return <Tag color="processing">{record.rule_type.toUpperCase()}</Tag>;
      }
    },
    {
      title: 'Action',
      dataIndex: 'application_action',
      key: 'application_action',
      sortable: false,
      render({ record }) {
        return <Status useIcon={true} useTag={true} status={record.application_action} />;
      }
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version',
      sortable: false
    },
    {
      title: 'Created On',
      dataIndex: 'created_time',
      key: 'created_time',
      type: 'datetime',
      hidden: true
    },
    {
      title: 'Modified Time',
      dataIndex: 'modified_time',
      key: 'modified_time',
      type: 'datetime',
      hidden: true
    },

    {
      title: '',
      dataIndex: 'actions',
      key: 'actions'
    }
  ];

  return (
    <Asset.Provider>
      <Department.Provider>
        <PageHeading icon="automation" title="Application Control" />
        <CrudProvider
          columns={columns}
          defaultFormItem={{
            rule_type: 'path',
            application_action: 'Block',
            scope: {
              assetFilter: 4
            },
            input_type: 'Browse Executable'
          }}
          titleColumn="displayName"
          resourceTitle={`Application Control`}
          disableFormScrolling
          hasSearch
          fetchFn={(...args) => getAllApplicationControlApi(...args, { category: type })}
          deleteFn={deleteApplicationControlApi}
          createFn={createApplicationControlApi}
          updateFn={updateApplicationControlApi}
          createSlot={(createFn) => (
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          )}
          formFields={(item) => <AppControlForm item={item} />}
        />
      </Department.Provider>
    </Asset.Provider>
  );
}
