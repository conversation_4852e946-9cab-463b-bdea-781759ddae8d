import { But<PERSON>, Tag, Drawer } from 'antd';
import Capitalize from 'lodash/capitalize';
import { CrudProvider } from '@/src/hooks/crud';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { User } from '@/src/components/pickers/UserPicker';
import { DeploymentPolicy } from '@/src/components/pickers/DeploymentPolicyPicker';
import {
  getAllDeploymentsApi,
  createDeploymentApi,
  updateDeploymentApi,
  deleteDeploymentApi
} from '@/src/modules/device-automation/deployments';
import DeployemntForm from '@/src/modules/device-automation/components/DeploymentForm';
import { Asset } from '@/src/components/pickers/AssetPicker';
import { ComplianceBundle } from '@/src/components/pickers/ComplianceBundlePicker';
import PageHeading from '@/src/components/PageHeading';
import { useState } from 'react';
import FrameworkDetail from '../components/FrameworkDetail';

export default function ComplianceDeployments({ type = 'compliances' }) {
  const tagColorMap = {
    draft: 'warning',
    initiated: 'processing',
    in_progress: 'processing',
    failed: 'error',
    cancelled: 'error',
    completed: 'success'
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Name',
      dataIndex: 'displayName',
      key: 'displayName'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Stage',
      dataIndex: 'deploymentStage',
      align: 'center',
      key: 'deploymentStage',
      width: 80,
      sortable: false,
      render({ record }) {
        return (
          <Tag
            color={tagColorMap[record.deploymentStage]}
            className="inline-flex items-center justify-center"
            style={{
              textAlign: 'center',
              textTransform: 'uppercase',
              minWidth: '80px'
            }}>
            {Capitalize((record.deploymentStage || '').toLowerCase()).replaceAll('_', ' ')}
          </Tag>
        );
      }
    },
    {
      title: 'Policy',
      dataIndex: 'deploymentPolicyId',
      align: 'center',
      key: 'deploymentPolicyId',
      sortable: false,
      render({ record }) {
        return <DeploymentPolicy.Picker textOnly value={record.deploymentPolicyId} disabled />;
      }
    },
    {
      title: 'Pending',
      dataIndex: 'pending',
      key: 'pending',
      align: 'center',
      width: 50,
      render({ record }) {
        return (
          <Tag
            color={'warning'}
            className="inline-flex items-center justify-center"
            style={{
              textAlign: 'center',
              textTransform: 'uppercase'
            }}>
            {record.pendingTasks}
          </Tag>
        );
      }
    },
    {
      title: 'Completed',
      dataIndex: 'completed',
      key: 'completed',
      align: 'center',
      width: 60,
      render({ record }) {
        return (
          <Tag
            color={'success'}
            className="inline-flex items-center justify-center"
            style={{
              textAlign: 'center',
              textTransform: 'uppercase'
            }}>
            {record.completedTasks}
          </Tag>
        );
      }
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      sortable: false,
      render({ record }) {
        return <User.Picker textOnly value={record.createdBy} disabled />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Compliance],
      deletePermissions: [constants.Delete_Compliance],
      canHaveButtons(record) {
        if (record.deploymentStage === 'draft') {
          return ['edit', 'delete'];
        }
        return ['delete'];
      }
    }
  ];

  const [deploymentForResult, setDeploymentForResult] = useState(null);

  return (
    <ComplianceBundle.Provider category={'compliances'}>
      <Asset.Provider>
        <DeploymentPolicy.Provider>
          <User.Provider>
            <PageHeading icon="compliance-deployment" title="Compliance Deployment" />
            <CrudProvider
              columns={columns}
              defaultFormItem={{
                deploymentType: 'install',
                deploymentCategory: type || 'compliances',
                deploymentStage: 'draft',
                retryCount: 3,
                resourceType: 'bundle'
              }}
              titleColumn="displayName"
              resourceTitle={`Compliance Deployment`}
              disableFormScrolling
              hasSearch
              fetchFn={(...args) => getAllDeploymentsApi(...args, { category: type })}
              deleteFn={(...args) => deleteDeploymentApi(...args, { category: type })}
              createFn={(...args) => createDeploymentApi(...args, { category: type })}
              updateFn={(...args) => updateDeploymentApi(...args, { category: type })}
              appendColumns={[
                {
                  title: 'Result',
                  dataIndex: 'result',
                  key: 'result',
                  render({ record }) {
                    if (record.deploymentStage === 'draft') {
                      return null;
                    }
                    return (
                      <Button type="link" onClick={() => setDeploymentForResult(record)}>
                        View Result
                      </Button>
                    );
                  }
                }
              ]}
              createSlot={(createFn) => (
                <PermissionChecker permission={constants.Create_Compliance}>
                  <Button type="primary" onClick={createFn}>
                    Create
                  </Button>
                </PermissionChecker>
              )}
              formFields={(item, _, { disabled }) => (
                <DeployemntForm item={item} disabled={disabled} />
              )}
              formActions={({ processingForm, resetForm, submitForm, formItem }) => (
                <>
                  <Button
                    type="primary"
                    loading={processingForm}
                    className="mr-2"
                    onClick={() => {
                      submitForm({
                        deploymentStage:
                          formItem.deploymentStage === 'draft'
                            ? 'initiated'
                            : formItem.deploymentStage || 'initiated'
                      });
                    }}>
                    Publish
                  </Button>
                  {formItem.deploymentStage === 'draft' ? (
                    <Button
                      type="primary"
                      loading={processingForm}
                      className="mr-2"
                      onClick={() => {
                        submitForm({
                          deploymentStage: 'draft'
                        });
                      }}>
                      Save As Draft
                    </Button>
                  ) : null}
                  <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
                    Reset
                  </Button>
                </>
              )}
            />
            <Drawer
              title={
                deploymentForResult ? (
                  <div className="flex items-center justify-between">
                    <div>
                      {deploymentForResult.name}: {deploymentForResult.displayName}
                    </div>
                  </div>
                ) : (
                  ``
                )
              }
              placement={'right'}
              width={'70%'}
              onClose={() => setDeploymentForResult(null)}
              destroyOnClose
              open={Boolean(deploymentForResult)}>
              {deploymentForResult ? (
                <FrameworkDetail deploymentId={deploymentForResult.id} />
              ) : null}
            </Drawer>
          </User.Provider>
        </DeploymentPolicy.Provider>
      </Asset.Provider>
    </ComplianceBundle.Provider>
  );
}
