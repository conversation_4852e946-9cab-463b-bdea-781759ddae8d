import { <PERSON><PERSON>, Tag, Drawer } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { User } from '@/src/components/pickers/UserPicker';
import {
  getAllBundlesApi,
  createBundleApi,
  updateBundleApi,
  deleteBundleApi,
  bulkDeleteBundleApi
} from '@src/modules/device-automation/bundles';
import BundleForm from '@src/modules/device-automation/components/BundleForm';
import Icon from '@/src/components/Icon';
import { useState } from 'react';
import BulkDeleteButton from '@/src/components/BulkDeleteButton';
import ComplianceRules from './ComplianceRules';
import { getAllRulesApi } from '../api/compliance-rules';
import { ComplianceRule } from '@/src/components/pickers/ComplianceRulePicker';
import PackageLogo from '../../device-automation/components/PackageLogo';
import PageHeading from '@/src/components/PageHeading';
// import { useState } from 'react';

export default function ComplianceBundle({ disabled }) {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render({ record, update, view }) {
        return (
          <div className="flex items-center">
            <PackageLogo
              disabled={disabled}
              package={record}
              style={{ width: '30px' }}
              className="mr-2"
              onChange={(logo) => {
                update({
                  ...record,
                  iconFile: [logo]
                });
              }}
            />
            {/* eslint-disable-next-line */}
            <a
              className="cursor-pointer"
              // eslint-disable-next-line
              href="javascript:;"
              onClick={() => view(record)}>
              {record.name}
            </a>
          </div>
        );
      }
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'OS',
      dataIndex: 'os',
      key: 'os',
      sortable: false,
      align: 'center',
      render({ record }) {
        return (
          <div className="text-xl">
            <Icon name={`platform_${record.os}`} />
          </div>
        );
      }
    },
    {
      title: 'Compliances',
      dataIndex: 'referenceIds',
      key: 'referenceIds',
      align: 'center',
      sortable: false,
      render({ record }) {
        return (
          <Button type="link" onClick={() => setShowDetailsFor(record)}>
            <Tag color="success">{record.referenceIds.length}</Tag>
          </Button>
        );
      }
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      sortable: false,
      render({ record }) {
        return <User.Picker textOnly value={record.createdBy} disabled />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Compliance],
      deletePermissions: [constants.Delete_Compliance]
    }
  ];

  const [showDetailFor, setShowDetailsFor] = useState(null);

  return (
    <User.Provider>
      <PageHeading icon="compliance-bundle" title="Compliance Framework" />
      <CrudProvider
        columns={columns}
        defaultFormItem={{
          type: 'compliances'
        }}
        titleColumn="displayName"
        resourceTitle={`Compliance Framework`}
        disableFormScrolling
        hasSearch
        fetchFn={(...args) => getAllBundlesApi(...args, { category: 'compliances' })}
        deleteFn={deleteBundleApi}
        createFn={createBundleApi}
        updateFn={updateBundleApi}
        allowSelection
        createAfterSlot={(_, { selectedItems, fetchData, setSelectedItems }) => (
          <BulkDeleteButton
            selectedItems={selectedItems}
            resourceTitle={`Compliance Bundle`}
            className="ml-2"
            onConfirm={() =>
              bulkDeleteBundleApi(selectedItems).then(() => {
                setSelectedItems([]);
                return fetchData();
              })
            }
          />
        )}
        createSlot={(createFn) => (
          <PermissionChecker permission={constants.Create_Compliance}>
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          </PermissionChecker>
        )}
        formFields={(item) => (
          <ComplianceRule.Provider>
            <BundleForm item={item} />
          </ComplianceRule.Provider>
        )}
      />
      <Drawer
        title={
          showDetailFor ? (
            <div className="flex items-center justify-between">
              <div>{showDetailFor.name}</div>
            </div>
          ) : (
            ``
          )
        }
        placement={'right'}
        width={'70%'}
        onClose={() => setShowDetailsFor(null)}
        destroyOnClose
        open={Boolean(showDetailFor) || Boolean(showDetailFor)}>
        {showDetailFor ? (
          <ComplianceRules
            disabled
            fetchFn={(offset, size, sortFilters) =>
              getAllRulesApi(offset, size, { ...sortFilters, ids: showDetailFor.referenceIds })
            }
          />
        ) : null}
      </Drawer>
    </User.Provider>
  );
}
