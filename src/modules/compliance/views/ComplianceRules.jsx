import { Button, Switch, Tag } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { Asset } from '@/src/components/pickers/AssetPicker';
import { User } from '@/src/components/pickers/UserPicker';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import constants from '@/src/constants/index';
import {
  bulkDeleteRulesApi,
  createRuleApi,
  deleteRuleApi,
  getAllRulesApi,
  testComplianceApi,
  updateRuleApi
} from '../api/compliance-rules';
import ComplianceRuleForm from '../components/ComplianceRuleForm';
import PermissionChecker from '@/src/components/PermissionChecker';
import PageHeading from '@/src/components/PageHeading';
import BulkDeleteButton from '@/src/components/BulkDeleteButton';
import { TestResourceCreationProvider } from '@/src/components/TestResourceCreationTask';

export default function ComplianceRules({ fetchFn, disabled }) {
  const columns = [
    ...(disabled
      ? []
      : [
          {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
            render({ record, update }) {
              return (
                <Switch
                  checked={record.status}
                  onChange={(e) => {
                    update({ ...record, status: e });
                  }}
                />
              );
            },
            sortable: false
          }
        ]),
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Bindings',
      dataIndex: 'bindings',
      key: 'bindings'
    },
    {
      title: 'Type',
      dataIndex: 'ruleType',
      key: 'ruleType'
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      sortable: false,
      render({ record }) {
        return (
          <>
            {record.tags.map((tag) => (
              <Tag key={tag} color="processing">
                {tag}
              </Tag>
            ))}
          </>
        );
      }
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      sortable: false,
      render({ record }) {
        return <User.Picker textOnly value={record.createdBy} disabled />;
      }
    },
    ...(disabled
      ? []
      : [
          {
            title: '',
            dataIndex: 'actions',
            key: 'actions',
            editPermissions: [constants.Update_Compliance],
            deletePermissions: [constants.Delete_Compliance]
          }
        ])
  ];

  return (
    <Asset.Provider>
      <User.Provider>
        <Department.Provider>
          <PageHeading icon="compliance-rule" title="Compliance Rules" />
          <CrudProvider
            columns={columns}
            defaultFormItem={{
              status: true,
              executionType: 'command',
              cmdType: 'cmd',
              rules: [{ condition: 'and', commandType: 'cmd' }]
            }}
            resourceTitle="Compliance Rule"
            hasSearch
            fetchFn={fetchFn || getAllRulesApi}
            deleteFn={deleteRuleApi}
            createFn={createRuleApi}
            updateFn={updateRuleApi}
            disableColumnSelection={disabled}
            allowSelection={!disabled}
            createAfterSlot={(_, { selectedItems, fetchData, setSelectedItems }) => (
              <BulkDeleteButton
                selectedItems={selectedItems}
                resourceTitle={`Compliance Rule`}
                className="ml-2"
                onConfirm={() =>
                  bulkDeleteRulesApi(selectedItems).then(() => {
                    setSelectedItems([]);
                    return fetchData();
                  })
                }
              />
            )}
            createSlot={(createFn) =>
              !disabled && (
                <PermissionChecker permission={constants.Create_Compliance}>
                  <Button type="primary" onClick={createFn}>
                    Create
                  </Button>
                </PermissionChecker>
              )
            }
            formFields={(item, change, { disabled, validateForm }) => (
              <TestResourceCreationProvider testApiFn={(data) => testComplianceApi(data)}>
                <ComplianceRuleForm
                  disabled={disabled}
                  value={item}
                  onChange={change}
                  validateForm={validateForm}
                />
              </TestResourceCreationProvider>
            )}
          />
        </Department.Provider>
      </User.Provider>
    </Asset.Provider>
  );
}
