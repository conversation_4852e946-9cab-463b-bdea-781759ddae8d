import { Outlet } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { useState, createContext, useContext, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Tabs } from 'antd';
// import PermissionChecker from '@/src/components/PermissionChecker';
// import constants from '@/src/constants/index';
import SplitPane from '@/src/components/SplitPane';
import Icon from '@/src/components/Icon';
import LicensedComponent from '@/src/components/LicensedComponent';

const ComplianceLayoutContext = createContext();

export default function DeviceAutomationLayout() {
  const [showMenu, setShowMenu] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();
  const [isMenuVisible, setIsMenuVisible] = useState(true);
  const [route, setRoute] = useState();

  useEffect(() => {
    setRoute(location.pathname.split('/').pop());
  }, [location]);

  function handleFilterSelected(value) {
    navigate(`/compliance/${location.pathname.indexOf(value) >= 0 ? '' : `${value}`}`);
  }

  const tabs = [
    {
      key: 'rules',
      label: (
        <div className="flex items-center mr-1">
          <Icon name="compliance-rule" className="text-base" />
          <span className="">Compliance Rule</span>
        </div>
      )
    },
    {
      key: 'bundle',
      label: (
        <div className="flex items-center mr-1">
          <Icon name="compliance-bundle" className="text-base" />
          <span className="">Compliance Framework</span>
        </div>
      )
    },
    {
      key: 'deployments',
      label: (
        <div className="flex items-center mr-1">
          <Icon name="compliance-deployment" className="text-base" />
          <span className="">Compliance Deployment</span>
        </div>
      )
    }
  ];

  return (
    <LicensedComponent allowedProducts={[LicensedComponent.EndpointOps]} useNotFound>
      <ComplianceLayoutContext.Provider
        value={{
          hideMenu: () => setShowMenu(false),
          showMenu: () => setShowMenu(true),
          displayNone: () => setIsMenuVisible(false),
          displayBlock: () => setIsMenuVisible(true),
          route,
          setRoute
        }}>
        <div className="h-full flex flex-col">
          <PageHeading icon="compliance" title="Compliance Rules" />
          <PermissionChecker redirect permission={constants.View_Compliance}>
            <SplitPane
              hasMenu={showMenu}
              isMenuVisible={isMenuVisible}
              onVisibleChange={(i) => setIsMenuVisible(i)}
              leftPane={
                <Tabs
                  activeKey={route}
                  tabPosition="left"
                  className="h-full mt-12 device-automation-tab"
                  onChange={(key) => handleFilterSelected(key)}
                  items={tabs}
                />
              }
              rightPane={<Outlet />}
            />
          </PermissionChecker>
        </div>
      </ComplianceLayoutContext.Provider>
    </LicensedComponent>
  );
}

export function useInventoryLayout() {
  return useContext(ComplianceLayoutContext);
}
