import api from '@api';

const END_POINT = `/compliance/framework`;

const transform = (item) => ({
  id: item.id,
  status: <PERSON><PERSON>an(item.status),
  name: item.framework_name,
  type: item.type,
  description: item.description,
  rules: item.rules,
  assetFilter: parseInt(item.asset_filter),
  assets:
    item.assets !== 'null' && item.assets !== null ? item.assets.split(',').map((i) => +i) : [],
  complianceAssets: item.tested_asset,
  rulesTested: item.rules_tested,
  passedTest: item.passed_test || '',
  createdBy: item.created_by
});

const transformForServer = async (item) => {
  return Promise.resolve({
    status: item.status ? 1 : 0,
    framework_name: item.name,
    description: item.description,
    type: item.type,
    asset_filter: item.assetFilter,
    assets: (item.assets || []).length ? item.assets.join(',') : null
  });
};

const sortKeyMap = {
  name: 'framework_name',
  assetFilter: 'asset_filter',
  passedTest: 'passed_test',
  rulesTested: 'rules_tested',
  complianceAssets: 'tested_asset',
  createdBy: 'created_by'
};

const searchableColumns = ['framework_name', 'description', 'type'];

export function getAllFrameworkApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getFrameworkApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateFramework(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getFrameworkApi(data.result));
}

export function createFramework(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getFrameworkApi(data.result));
}

export function deleteFramework(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}

const RULE_END_POINT = `/compliance/framework/rule`;

const transformRule = (rule) => ({
  id: rule.id,
  status: Boolean(rule.status),
  name: rule.rule_name,
  bindings: rule.bindings,
  ruleType: rule.type,
  description: rule.description,
  audit: rule.audit,
  impact: rule.impact,
  createdBy: +rule.created_by,
  remediation: rule.remediation,
  assetFilter: parseInt(rule.asset_filter),
  assets:
    rule.assets && rule.assets !== 'null' && rule.assets !== null
      ? rule.assets.split(',').map((i) => +i)
      : [],
  plugin: rule.plugin,
  interval: {
    value: +rule.interval,
    unit: rule.interval_unit || 'seconds'
  },
  commands: rule.commands,
  expectedResult: rule.expected_result,
  validateRuleAssets: rule.validate_rule_assets,
  lastChecked: rule.last_checked
});

const transformRuleForServer = async (rule) => {
  return Promise.resolve({
    id: rule.id,
    status: rule.status ? 1 : 0,
    rule_name: rule.name,
    type: rule.ruleType,
    bindings: rule.bindings,
    description: rule.description,
    audit: rule.audit,
    impact: rule.impact,
    remediation: rule.remediation,
    asset_filter: rule.assetFilter,
    assets: (rule.assets || []).length ? rule.assets.join(',') : null,
    plugin: rule.plugin,
    interval: (rule.interval || {}).value,
    interval_unit: (rule.interval || {}).unit,
    commands: rule.commands,
    expected_result: rule.expectedResult,
    validate_rule_assets: rule.validateRuleAssets
  });
};

const ruleSortKeyMap = {
  name: 'rule_name',
  createdBy: 'created_by',
  ruleType: 'type',
  expectedResult: 'expected_result',
  validateRuleAssets: 'validate_rule_assets',
  lastChecked: 'last_checked'
};

const rulesSearchableColumns = [
  'rule_name',
  'bindings',
  'type',
  'description',
  'audit',
  'remediation',
  'impact',
  'plugin',
  'commands'
];

export function getAllFrameworkRulesApi(frameworkId, offset, size, sortFilter) {
  return api
    .post(`${RULE_END_POINT}/${frameworkId}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              ruleSortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? rulesSearchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transformRule)
      };
    });
}

export function getFrameworkRuleApi(frameworkId, id) {
  return api
    .get(`${RULE_END_POINT}/${frameworkId}/${id}`)
    .then(({ result }) => transformRule(result));
}

export function updateFrameworkRule(frameworkId, item) {
  return transformRuleForServer(item)
    .then((data) => {
      return api.put(`${RULE_END_POINT}/${frameworkId}/${item.id}`, data);
    })
    .then((data) => getFrameworkRuleApi(frameworkId, data.result));
}

export function createFrameworkRule(frameworkId, item) {
  return transformRuleForServer(item)
    .then((data) => {
      return api.post(`${RULE_END_POINT}/${frameworkId}`, data);
    })
    .then((data) => getFrameworkRuleApi(frameworkId, data.result));
}

export function deleteFrameworkRule(frameworkId, item) {
  return api.delete(`${RULE_END_POINT}/${frameworkId}/${item.id}`);
}
