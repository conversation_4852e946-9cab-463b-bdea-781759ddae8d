import { Form, Row, Col, Divider, Input, But<PERSON>, Select } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import Repeater from '@/src/components/Repeater';
import Icon from '@/src/components/Icon';

export default function ComplianceRuleOutputConditions({ disabled, name, ...props }) {
  const conditionOptions = [
    { label: 'AND', value: 'and' },
    { label: 'OR', value: 'or' }
  ];
  const operatorOptions = [
    'Equal',
    'NotEqual',
    'GreaterThan',
    'GreaterThanEqual',
    'LessThan',
    'LessThanEqual',
    'Contains',
    'NotContains',
    'StartWith',
    'EndWith'
  ].map((i) => ({ value: i, label: i }));

  return (
    <Repeater
      {...props}
      disabled={disabled}
      name={name}
      defaultItem={{
        condition: 'and',
        operator: 'Equal'
      }}
      addBtnText={`Add New Output Check`}>
      {({ key, name, index, ...restField }, actions) => (
        <div className="flex flex-col" key={key}>
          <div className="flex items-center">
            <div
              className={`flex-1 mr-2 px-2 py-1 rounded border-solid border-border
        ${disabled ? '' : 'bg-border'}`}>
              <Row gutter={8}>
                <Col span={8}>
                  <div className="flex flex-1">
                    <Form.Item
                      {...restField}
                      label="Compare Using"
                      name={[name, 'operator']}
                      className="flex-1"
                      rules={[{ required: true }]}>
                      <Select placeholder={'Select Operator'} options={operatorOptions} />
                    </Form.Item>
                  </div>
                </Col>
                <Col span={16}>
                  <div className="flex flex-1">
                    <Form.Item
                      {...restField}
                      label="Compare With"
                      name={[name, 'value']}
                      className="flex-1"
                      rules={[{ required: true }]}>
                      <Input placeholder={'Output To Check'} />
                    </Form.Item>
                  </div>
                </Col>
              </Row>
            </div>
            <div className="flex-shrink-0 flex">
              {!disabled && (
                <Button
                  shape="circle"
                  className="mr-1"
                  title="Move Up"
                  disabled={index === 0}
                  onClick={() => actions.move(index, index - 1)}>
                  <ArrowUpOutlined />
                </Button>
              )}
              {!disabled && (
                <Button
                  shape="circle"
                  className="mr-1"
                  title="Move Down"
                  disabled={index === actions.length - 1}
                  onClick={() => actions.move(index, index + 1)}>
                  <ArrowDownOutlined />
                </Button>
              )}
              {!disabled && (
                <Button
                  shape="circle"
                  type="danger"
                  style={{ visibility: name === 0 ? 'hidden' : 'visible' }}
                  onClick={() => actions.remove(name)}>
                  <Icon name="close" className="text-danger" style={{ fontSize: '1.5rem' }} />
                </Button>
              )}
            </div>
          </div>
          {index + 1 < actions.length ? (
            <Divider className="my-1">
              <Form.Item
                label=" "
                {...restField}
                name={[name, 'condition']}
                noStyle
                rules={[{ required: true }]}>
                <Select options={conditionOptions} placeholder="Condition Type" />
              </Form.Item>
            </Divider>
          ) : null}
        </div>
      )}
    </Repeater>
  );
}
