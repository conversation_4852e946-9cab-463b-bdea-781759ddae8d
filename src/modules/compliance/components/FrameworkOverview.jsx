import { Row, Col } from 'antd';
import PackageLogo from '../../device-automation/components/PackageLogo';

export default function FrameworkOverview({ framework }) {
  return (
    <Row className="my-2 border-border border-solid rounded">
      <Col span={24} className="bg-lightest p-2">
        <Row className="h-full">
          <Col span={9} className="flex items-center justify-center">
            <PackageLogo disabled package={framework} style={{ height: '100%' }} />
          </Col>
          <Col span={15} className="my-2">
            <h4>{framework.name}</h4>
            <small className="text-label">{framework.description}</small>
          </Col>
        </Row>
      </Col>
    </Row>
  );
}
