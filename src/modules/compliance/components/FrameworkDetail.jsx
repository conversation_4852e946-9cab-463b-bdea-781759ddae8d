import { useEffect, useState } from 'react';
import { getHeatmapForDeploymentApi } from '../api/compliance-rules';
import { Spin, Row, Col } from 'antd';
import Heatmap from '@/src/components/Heatmap';
import FrameworkOverview from './FrameworkOverview';
import { CrudProvider } from '@/src/hooks/crud';
import generateId from '@/src/utils/id';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';

export default function FrameworkDetail({ deploymentId }) {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState(null);
  const [timeline, setTimeline] = useState({ selected: 'Today' });
  const [renderKey, setRenderKey] = useState(0);
  const columns = [
    {
      key: 'name',
      title: 'Rule Name',
      dataIndex: 'name',
      sortable: false,
      width: '50%'
    },
    {
      key: 'bindings',
      title: 'Bindings',
      dataIndex: 'bindings',
      sortable: false
    },
    {
      key: 'description',
      title: 'Description',
      dataIndex: 'description',
      ellipsis: true,
      sortable: false
    },
    {
      key: 'total',
      title: 'Total Tested',
      dataIndex: 'total',
      sortable: false
    },
    {
      key: 'success',
      title: 'Total Passed',
      dataIndex: 'success',
      sortable: false
    },
    {
      key: 'failed',
      title: 'Total Failed',
      dataIndex: 'failed',
      sortable: false
    }
  ];

  useEffect(() => {
    getHeatmapForDeploymentApi(deploymentId, timeline).then((data) => {
      setData(data[0]);
      setLoading(false);
    });
  }, [deploymentId, timeline]);

  useEffect(() => {
    setRenderKey((key) => (key = key + 1));
  }, [data]);

  function handleTimelineChange(timeline) {
    setTimeline(timeline);
  }
  return loading ? (
    <div className="flex flex-1 items-center justify-center">
      <Spin spinning={true} />
    </div>
  ) : (
    <div className="h-full flex flex-col min-h-0 overflow-y-auto overflow-x-hidden">
      <FrameworkOverview framework={data} />
      <Row className="my-2 border-border border-solid rounded">
        <Col span={24} className="bg-lightest p-2">
          <Heatmap data={data.rules} />
        </Col>
      </Row>
      <CrudProvider
        hasSearch={false}
        key={renderKey}
        disableColumnSelection
        disableRefresh
        createSlot={() => (
          <>
            <TimelinePicker value={timeline} onChange={handleTimelineChange} />
          </>
        )}
        fetchFn={(offset, size) => {
          return Promise.resolve({
            totalCount: data.rules.length,
            result: data.rules
              .slice(offset, offset + size)
              .map((d) => ({ ...d, id: d.id || generateId() }))
          });
        }}
        columns={columns}
      />
    </div>
  );
}
