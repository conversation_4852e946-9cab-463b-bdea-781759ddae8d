import { Col, Row, Form, Input, Select, Switch, Button, Divider } from 'antd';
import { Fragment } from 'react';
import Repeater from '@/src/components/Repeater';
import Icon from '@/src/components/Icon';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import PackageUploader, { ALLOWED_SCRIPT_EXTENSIONS } from '@/src/components/PackageUploader';
import AiScriptGenerator from '@/src/components/ai-script-generator/Container';
import ComplianceRuleOutputConditions from './ComplianceRuleOutputConditions';
import { useTestResourceCreation } from '@/src/components/TestResourceCreationTask';

export default function ComplianceRuleForm({ value, disabled, onChange, validateForm }) {
  const impactOptions = [
    { label: 'High', value: 'high' },
    { label: 'Medium', value: 'medium' },
    { label: 'Low', value: 'low' }
  ];
  const ruleTypeOptions = [
    { label: 'AAA', value: 'aaa' },
    { label: 'Configuration', value: 'configuration' },
    { label: 'Networking', value: 'networking' },
    { label: 'Patching', value: 'patching' },
    { label: 'Security', value: 'security' }
  ];

  const ruleExecutorTypeOptions = [
    { label: 'Command', value: 'cmd' },
    { label: 'Registry', value: 'reg' }
  ];

  const conditionOptions = [
    { label: 'AND', value: 'and' },
    { label: 'OR', value: 'or' }
  ];

  const commandTypeOptions = [
    { label: 'Cmd', value: 'cmd' },
    { label: 'Powershell', value: 'ps' }
  ];

  const typeOptions = ['command', 'script'].map((i) => ({ value: i, label: i }));

  const form = Form.useFormInstance();

  const testResourceContext = useTestResourceCreation();

  return (
    <Fragment>
      <Row gutter={32}>
        <Col span={12}>
          <Form.Item label="Rule Name" name="name" rules={[{ required: true }]}>
            <Input placeholder="Rule Name" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Status" name="status" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Execution Type" name="executionType" rules={[{ required: true }]}>
            <Select
              options={typeOptions}
              disabled={value.id}
              placeholder="Please select"
              onChange={(value) => {
                form.setFieldValue('executionType', value);
                form.setFieldValue('rules', [{ condition: 'and' }]);
              }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Bindings" name="bindings" rules={[{ required: true }]}>
            <Input placeholder="Bindings" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Rule Type" name="ruleType" rules={[{ required: true }]}>
            <Select options={ruleTypeOptions} placeholder="Rule Type" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Impact" name="impact" rules={[{ required: true }]}>
            <Select options={impactOptions} placeholder="Impact" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Tags" name="tags">
            <Select mode="tags" placeholder="Tags" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Description" name="description">
            <Input.TextArea placeholder="Description" rows={5} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Impact" name="audit" rules={[{ required: true }]}>
            <Input.TextArea placeholder="Audit" rows={5} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Remediation" name="remediation" rules={[{ required: true }]}>
            <Input.TextArea placeholder="Remediation" rows={5} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={32} className="bg-lightest px-4 py-2 rounded ">
        {/* <Col span={24}>
          <Form.Item label=" " noStyle name="scope">
            <AssetScopePicker
              label="Scope"
              skipProvider
              gutter={16}
              name={['scope', 'assetFilter']}
              subname={['scope', 'assets']}
            />
          </Form.Item>
        </Col> */}
        {form.getFieldValue('executionType') === 'command' ? (
          <Col span={13}>
            <Form.Item label="Rule Execution Type" name="cmdType" rules={[{ required: true }]}>
              <Select
                options={ruleExecutorTypeOptions}
                placeholder="Rule Type"
                onChange={(v) => {
                  form.setFieldValue('cmdType', v);
                  form.setFieldValue('rules', [
                    { condition: 'and', ...(v === 'cmd' ? { commandType: 'cmd' } : {}) }
                  ]);
                }}
              />
            </Form.Item>
          </Col>
        ) : null}
        <Col span={11}>{testResourceContext.testControl()}</Col>
        <Col span={24}>
          <Repeater
            disabled={disabled}
            name="rules"
            defaultItem={{
              condition: 'and',
              ...(form.getFieldValue('cmdType') === 'cmd' ? { commandType: 'cmd' } : {})
            }}
            addBtnText={`Add New ${
              form.getFieldValue('executionType') === 'script'
                ? 'Script'
                : form.getFieldValue('cmdType') === 'reg'
                ? 'Registry Check'
                : 'Command'
            }`}>
            {({ key, name, index, ...restField }, actions) => (
              <div className="flex flex-col mb-4" key={key}>
                <div className="flex items-center">
                  <div
                    className={`flex-1 mr-2 px-2 py-1 rounded border-solid border-border
        ${disabled ? '' : 'bg-border'}`}>
                    <Row gutter={8}>
                      {form.getFieldValue('executionType') === 'command' ? (
                        <>
                          <Col span={form.getFieldValue('cmdType') === 'reg' ? 15 : 15}>
                            <div className="flex flex-1">
                              <Form.Item
                                {...restField}
                                name={[name, 'command']}
                                className="flex-1"
                                label={
                                  form.getFieldValue('cmdType') === 'reg'
                                    ? 'Registry Path'
                                    : 'Command'
                                }
                                rules={[{ required: true }]}>
                                <Input
                                  placeholder={
                                    form.getFieldValue('cmdType') === 'reg'
                                      ? 'Registry Path'
                                      : 'Command'
                                  }
                                />
                              </Form.Item>
                              {form.getFieldValue('cmdType') !== 'reg' ? (
                                <Form.Item
                                  {...restField}
                                  name={[name, 'command']}
                                  label=" "
                                  className="inline-flex ml-2">
                                  <AiScriptGenerator
                                    type="command"
                                    platform={form.getFieldValue('os')}
                                    script_type={
                                      form.getFieldValue('os') === 'windows'
                                        ? form.getFieldValue([
                                            'configurationActions',
                                            name,
                                            'commandType'
                                          ]) === 'ps'
                                          ? 'powershell'
                                          : 'batch'
                                        : 'bash'
                                    }>
                                    <Icon name="ai" className="text-lg" />
                                  </AiScriptGenerator>
                                </Form.Item>
                              ) : null}
                            </div>
                          </Col>

                          {form.getFieldValue('cmdType') === 'reg' ? (
                            <Col span={9}>
                              <Form.Item
                                {...restField}
                                name={[name, 'key']}
                                label="Registry Key"
                                rules={[{ required: true }]}>
                                <Input placeholder="Registry Key" />
                              </Form.Item>
                            </Col>
                          ) : null}

                          {form.getFieldValue('cmdType') === 'cmd' ? (
                            <Col span={9}>
                              <Form.Item
                                {...restField}
                                name={[name, 'commandType']}
                                label={'Command Type'}>
                                <Select options={commandTypeOptions} placeholder="Command Type" />
                              </Form.Item>
                            </Col>
                          ) : null}
                        </>
                      ) : (
                        <>
                          <Col span={24}>
                            <div className="inline">
                              <Form.Item
                                {...restField}
                                name={[name, 'scriptFile']}
                                className="inline-flex"
                                label="Script File">
                                <PackageUploader extensions={ALLOWED_SCRIPT_EXTENSIONS} />
                              </Form.Item>
                              <Form.Item
                                {...restField}
                                className="inline-flex ml-2"
                                name={[name, 'scriptFile']}
                                label=" ">
                                <AiScriptGenerator type="script" />
                              </Form.Item>
                            </div>
                          </Col>
                        </>
                      )}
                      <Col span={24}>
                        {/* <Form.Item
                          {...restField}
                          name={[name, 'complianceRuleConditions']}
                          label="Expected Output">
                          <Input placeholder="Expected Output" />
                        </Form.Item> */}
                        <ComplianceRuleOutputConditions name={[name, 'complianceRuleConditions']} />
                      </Col>
                    </Row>
                  </div>
                  <div className="flex-shrink-0 flex items-center">
                    {!disabled && (
                      <Button
                        shape="circle"
                        className="mr-1"
                        title="Move Up"
                        disabled={index === 0}
                        onClick={() => actions.move(index, index - 1)}>
                        <ArrowUpOutlined />
                      </Button>
                    )}
                    {!disabled && (
                      <Button
                        shape="circle"
                        className="mr-1"
                        title="Move Down"
                        disabled={index === actions.length - 1}
                        onClick={() => actions.move(index, index + 1)}>
                        <ArrowDownOutlined />
                      </Button>
                    )}
                    {!disabled && (
                      <Button
                        shape="circle"
                        type="danger"
                        style={{ visibility: name === 0 ? 'hidden' : 'visible' }}
                        onClick={() => actions.remove(name)}>
                        <Icon name="close" className="text-danger" style={{ fontSize: '1.5rem' }} />
                      </Button>
                    )}
                  </div>
                </div>
                {index + 1 < actions.length ? (
                  <Divider>
                    <Form.Item
                      label=" "
                      {...restField}
                      name={[name, 'condition']}
                      noStyle
                      rules={[{ required: true }]}>
                      <Select options={conditionOptions} placeholder="Condition Type" />
                    </Form.Item>
                  </Divider>
                ) : null}
              </div>
            )}
          </Repeater>
        </Col>
      </Row>
      {testResourceContext.result()}
    </Fragment>
  );
}
