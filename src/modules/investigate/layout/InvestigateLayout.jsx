import { Row, Col } from 'antd';
import { Outlet } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import AnimatedRoutes from '@components/AnimatedRoutes';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';

export default function InvestigateLayout() {
  return (
    <div className="h-full flex flex-col">
      <PageHeading icon="investigate" title="Investigate" />
      <div className="flex-1 min-h-0 flex flex-col">
        <Row className="h-full">
          <PermissionChecker permission={constants.View_Investigate}>
            <AnimatedRoutes element={Col} span={24} className="h-full flex flex-col">
              <Outlet />
            </AnimatedRoutes>
          </PermissionChecker>
        </Row>
      </div>
    </div>
  );
}
