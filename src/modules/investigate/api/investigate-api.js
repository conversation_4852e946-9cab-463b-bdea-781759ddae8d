import {
  transformAssetScope,
  transformAssetScopeForServer
} from '@/src/components/pickers/AssetScopePicker';
import api from '@api';

const END_POINT = `/live/investigate`;

const transform = (item) => ({
  id: item.id,
  assetCount: (item.assets || []).length,
  description: item.description,
  name: item.query_name,
  query: item.query,
  ...transformAssetScope(item),
  createdAt: item.created_time
});

const transformForServer = (item) => ({
  query_name: item.name,
  query: item.query,
  description: item.description,
  ...transformAssetScopeForServer(item)
});

const searchableColumns = ['query_name', 'description', 'query'];

export function getAllInvestigateApi(offset, size, sortFilter = {}) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getInvestigateApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function createInvestigateApi(data) {
  return api
    .post(`${END_POINT}`, transformForServer(data))
    .then((data) => getInvestigateApi(data.result));
}

export function updateInvestigateApi(data) {
  return api
    .put(`${END_POINT}/${data.id}`, transformForServer(data))
    .then(() => getInvestigateApi(data.id));
}

export function deleteInvestigateApi({ id }) {
  return api.delete(`${END_POINT}/${id}`);
}
