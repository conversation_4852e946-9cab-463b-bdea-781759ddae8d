import { useEffect, useRef, useState } from 'react';
import { Form, Input, Row, Col, Button, Drawer } from 'antd';
import CloneDeep from 'lodash/cloneDeep';
import { Link, useNavigate } from 'react-router-dom';
import Icon from '@/src/components/Icon';
import AssetScopePicker, {
  transformAssetScopeForServer
} from '@/src/components/pickers/AssetScopePicker';
import QueryEditor from '@/src/components/pickers/QueryEditor';
import SocketEventHandler from '@/src/components/SocketEventHandler';
import { deleteInvestigateApi, getAllInvestigateApi } from '../api/investigate-api';
import InvestigateResult from './InvestigateResult';
import { CrudProvider } from '@/src/hooks/crud';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';

export default function InvestigateForm({ defaultValue, onSubmit, processing, disabled }) {
  const [formKey, setFormKey] = useState(1);
  const [resultKey, setResultKey] = useState(0);
  const formRef = useRef(null);
  const navigate = useNavigate();
  const [runQueryForItem, setRunQueryForItem] = useState(null);

  useEffect(() => {
    if (defaultValue.id) {
      resetForm();
    }
    // eslint-disable-next-line
  }, [defaultValue]);

  function handleQuickHelpSelected(item) {
    navigate(`/investigate/${item}/edit`);
  }

  function resetForm() {
    setFormKey(formKey + 1);
    setResultKey(0);
  }

  async function handleRun() {
    try {
      await formRef.current.validateFields(['query']);
      setResultKey(resultKey + 1);
      setRunQueryForItem(formRef.current.getFieldsValue());
    } catch (e) {
      console.error(e);
    }
  }

  return (
    <div className="flex flex-col min-h-0 flex-1">
      <div className="flex items-center">
        <Link to=".." className="mr-2 text-lg">
          <Icon name="arrow-left" />
        </Link>
        <h1 className="text-primary my-4 text-lg">
          {defaultValue.id ? (disabled ? 'View' : 'Edit') : 'Create'} Query
        </h1>
      </div>
      <Row gutter={32} className="flex flex-1 min-h-0 mb-4">
        <Col span={12} className="flex flex-col h-full min-h-0 overflow-auto">
          <div className="rounded px-2 py-2">
            <Form
              ref={formRef}
              layout="vertical"
              disabled={disabled}
              key={formKey}
              onFinish={onSubmit}
              initialValues={CloneDeep(defaultValue)}>
              <Row>
                <Col span={24}>
                  <Form.Item label="Query Name" name="name" rules={[{ required: true }]}>
                    <Input placeholder="Query Name" />
                  </Form.Item>
                  <Form.Item label="Description" name="description" rules={[{ required: true }]}>
                    <Input.TextArea placeholder="Description" rows={5} />
                  </Form.Item>
                  {/* <Row gutter={16} className="items-center -mt-5">
                    <Col span={20}> */}
                  <Form.Item label=" " noStyle name="scope">
                    <AssetScopePicker
                      label="Scope"
                      gutter={16}
                      name={['scope', 'assetFilter']}
                      subname={['scope', 'assets']}
                    />
                  </Form.Item>
                  {/*  </Col>
                  </Row> */}
                  <Form.Item name="query" label="Command" rules={[{ required: true }]}>
                    <QueryEditor />
                  </Form.Item>
                </Col>
                {!disabled && (
                  <Col span={24} className="text-right">
                    <Button
                      className="mr-2"
                      type="primary"
                      disabled={false}
                      onClick={handleRun}
                      ghost
                      htmlType="button">
                      Run
                    </Button>
                    <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                      Save Query
                    </Button>
                    <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
                      Reset
                    </Button>
                  </Col>
                )}
              </Row>
            </Form>
          </div>
        </Col>
        <Col span={12} className="flex flex-col h-full min-h-0">
          <div className="flex flex-col h-full border-border border-solid rounded">
            <CrudProvider
              resourceTitle="Investigate"
              disableColumnSelection
              columns={[]}
              hasSearch
              deleteFn={deleteInvestigateApi}
              onEdit={(item) => handleQuickHelpSelected(item.id)}
              disableFormScrolling
              tableSlot={({ rowKey, data, edit, deleteItem }) => (
                <Row className="mt-4">
                  {data.map((investigate) => (
                    <Col span={24} key={rowKey(investigate)} className="">
                      <div className="pr-2">
                        <div className="p-2 mb-4 bg-seperator rounded-lg widget-row droppable-element">
                          <div className="flex flex-col justify-between">
                            <div className="flex-1">
                              <h4 className="m-0">{investigate.name}</h4>
                              <small
                                className="text-label"
                                style={{
                                  overflow: 'hidden',
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  lineClamp: 2,
                                  WebkitBoxOrient: 'vertical'
                                }}>
                                {investigate.description}
                              </small>
                            </div>
                            <div className="flex items-center justify-end shrink-0">
                              <span
                                className="cursor-pointer text-lg text-primary mx-1"
                                title="Run Query"
                                onClick={(e) => setRunQueryForItem(investigate)}>
                                <Icon name="play" />
                              </span>
                              <PermissionChecker permission={[constants.Update_Investigate]} hasAny>
                                <span
                                  className="cursor-pointer text-lg text-primary"
                                  title="Edit Query"
                                  onClick={(e) => edit(investigate)}>
                                  <Icon name="edit" />
                                </span>
                              </PermissionChecker>
                              <PermissionChecker permission={[constants.Delete_Investigate]} hasAny>
                                <span
                                  className="cursor-pointer text-lg text-primary mx-1"
                                  title="Delete Query"
                                  onClick={(e) => deleteItem(investigate)}>
                                  <Icon name="delete" />
                                </span>
                              </PermissionChecker>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              )}
              fetchFn={getAllInvestigateApi}
              formActions={() => <span />}
            />
          </div>
        </Col>
      </Row>
      <Drawer
        title={`Result for Query ${runQueryForItem?.name || ''}`}
        placement={'bottom'}
        height={'80vh'}
        onClose={() => setRunQueryForItem(null)}
        destroyOnClose
        maskClosable={false}
        open={runQueryForItem !== null}>
        {runQueryForItem !== null ? (
          <SocketEventHandler
            event="live_query"
            key={`${resultKey}-${runQueryForItem.id || ''}`}
            mergePayload
            context={{
              ...transformAssetScopeForServer(runQueryForItem),
              query: runQueryForItem.query
            }}>
            {(payload) => (
              <div className="h-full flex flex-col min-h-0">
                <InvestigateResult data={payload} />
              </div>
            )}
          </SocketEventHandler>
        ) : null}
      </Drawer>
    </div>
  );
}
