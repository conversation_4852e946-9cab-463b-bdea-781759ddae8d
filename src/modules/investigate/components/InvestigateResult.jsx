import Moment from 'moment';
import { Ta<PERSON>, <PERSON><PERSON> } from 'antd';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import generateId from '@/src/utils/id';
import exportData from '@/src/utils/export';
import { downloadFile } from '@/src/utils/download';
import QueryEditor from '@/src/components/pickers/QueryEditor';
import { CrudProvider } from '@/src/hooks/crud';

function JsonResult({ data, asset = {} }) {
  function handleExport() {
    const blob = new Blob([JSON.stringify(data, null, 4)], {
      type: 'application/json'
    });
    downloadFile(
      blob,
      undefined,
      `zirozen_inv${asset.hostname ? `_${asset.hostname}` : ''}_${Moment().format(
        'DDMMYYYYHHMM'
      )}.json`
    );
  }
  return (
    <div className="flex flex-1 min-h-0 flex-col">
      <div className="flex justify-end mb-1">
        <Button
          className="ml-2"
          htmlType="button"
          type="primary"
          ghost
          onClick={() => handleExport()}>
          Export JSON
        </Button>
      </div>
      <div className="flex flex-1 min-h-0 flex-col overflow-auto">
        <QueryEditor value={JSON.stringify(data, null, 4)} disabled mode="json" height="100%" />
      </div>
    </div>
  );
}

function TableResult({ data, keyFn, asset = {} }) {
  const reservedKeys = ['asset_name', 'asset_id'];

  const columns = [
    {
      key: 'asset_name',
      title: 'Endpoint',
      dataIndex: 'asset_name',
      render({ record }) {
        return <Link to={`/inventory/endpoints/${record.asset_id}`}>{record.asset_name}</Link>;
      }
    },
    ...Object.keys(data[0] || {})
      .filter((item) => reservedKeys.includes(item) === false)
      .map((key) => ({
        key: key,
        title: key,
        dataIndex: key
      }))
  ];

  function handleExport(type) {
    exportData(columns, data, type).then((blob) => {
      downloadFile(
        blob,
        undefined,
        `zirozen_inv${asset.hostname ? `_${asset.hostname}` : ''}_${Moment().format(
          'DDMMYYYYHHMM'
        )}.${type}`
      );
    });
  }

  return (
    <div className="flex flex-1 min-h-0 flex-col">
      <div className="flex justify-end mb-1">
        <Button htmlType="button" type="primary" ghost onClick={() => handleExport('csv')}>
          Export CSV
        </Button>
        <Button
          className="ml-2"
          htmlType="button"
          type="primary"
          ghost
          onClick={() => handleExport('pdf')}>
          Export PDF
        </Button>
      </div>
      <div className="flex flex-1 min-h-0 flex-col">
        <CrudProvider
          rowKey={keyFn ? keyFn : (record) => record.id}
          disableHeadingSlot
          disableColumnSelection
          disableExport
          disableRefresh
          fetchFn={(offset, size) => {
            return Promise.resolve({
              totalCount: data.length,
              result: data.slice(offset, offset + size).map((d) => ({ ...d, id: generateId() }))
            });
          }}
          columns={columns}
        />
      </div>
    </div>
  );
}

export default function InvestigateResult({ data, asset }) {
  const tabs = [
    {
      label: 'Formatted',
      key: 'formatted',
      component: <TableResult data={data} asset={asset} />
    },
    {
      label: 'JSON',
      key: 'json',
      component: <JsonResult data={data} asset={asset} />
    }
  ];

  const [activeKey, setActiveKey] = useState('formatted');

  return (
    <>
      <Tabs
        activeKey={activeKey}
        onChange={(key) => {
          setActiveKey(key);
        }}
        items={tabs}
        className="sticky-tabs"
      />
      <div className="flex-1 flex flex-col min-h-0 overflow-auto">
        {tabs.find((tab) => tab.key === activeKey).component}
      </div>
    </>
  );
}
