import { Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createInvestigateApi,
  deleteInvestigateApi,
  getAllInvestigateApi,
  updateInvestigateApi
} from '../api/investigate-api';
import { assetFilterOptions } from '@/src/components/pickers/AssetScopePicker';
import constants from '@/src/constants/index';
import PermissionChecker from '@/src/components/PermissionChecker';

export default function List() {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Scope',
      dataIndex: 'scope',
      key: 'scope',
      render({ record }) {
        const option = assetFilterOptions.find((d) => d.value === record.scope.assetFilter);
        return option ? option.label : null;
      }
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Investigate],
      deletePermissions: [constants.Delete_Investigate]
    }
  ];

  const navigate = useNavigate();

  return (
    <CrudProvider
      columns={columns}
      resourceTitle="Investigate Query"
      hasSearch
      fetchFn={getAllInvestigateApi}
      updateFn={updateInvestigateApi}
      deleteFn={deleteInvestigateApi}
      createFn={createInvestigateApi}
      onView={(item) => navigate(`/investigate/${item.id}/view`)}
      onEdit={(item) => navigate(`/investigate/${item.id}/edit`)}
      createSlot={() => (
        <PermissionChecker permission={constants.Create_Investigate}>
          <Button type="primary" onClick={() => navigate('/investigate/create')}>
            Create
          </Button>
        </PermissionChecker>
      )}
    />
  );
}
