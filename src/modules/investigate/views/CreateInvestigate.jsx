import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import InvestigateForm from '../components/InvestigateForm';
import { createInvestigateApi } from '../api/investigate-api';

export default function CreateInvestigate() {
  const navigate = useNavigate();
  const [processing, setProcessing] = useState(false);

  function handleSubmit(values) {
    setProcessing(true);
    createInvestigateApi(values)
      .then(() => navigate('/investigate'))
      .finally(() => setProcessing(false));
  }

  return (
    <InvestigateForm
      processing={processing}
      onSubmit={handleSubmit}
      defaultValue={{
        selectedQueryId: undefined,
        scope: {
          assetFilter: 4
        }
      }}
    />
  );
}
