import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Spin } from 'antd';
import { getInvestigateApi, updateInvestigateApi } from '../api/investigate-api';
import InvestigateForm from '../components/InvestigateForm';

export default function EditInvestigate({ disabled }) {
  const navigate = useNavigate();
  const [processing, setProcessing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [defaultValue, setDefaultValue] = useState(null);
  const params = useParams();

  useEffect(() => {
    getInvestigateApi(params.id)
      .then((data) => {
        setDefaultValue({ ...data, selectedQueryId: +params.id });
        setLoading(false);
      })
      .catch(() => {
        navigate('/investigate/create');
      });
    // eslint-disable-next-line
  }, [params]);

  function handleSubmit(values) {
    values.id = defaultValue.id;
    setProcessing(true);
    updateInvestigateApi(values).finally(() => setProcessing(false));
  }

  return loading ? (
    <Spin spinning={loading}>
      <div className="flex flex-col min-w-0 min-h-0 flex-1" />
    </Spin>
  ) : (
    <InvestigateForm
      processing={processing}
      disabled={disabled}
      onSubmit={handleSubmit}
      defaultValue={defaultValue}
    />
  );
}
