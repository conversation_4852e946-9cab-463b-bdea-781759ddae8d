import generateId from '@/src/utils/id';
import api from '@api';

const END_POINT = `/analytics/dashboard`;

const transformContext = (context) =>
  (context || []).map((widget) => ({
    id: widget.id,
    i: widget.guid || generateId(),
    x: widget.x,
    y: widget.y,
    h: widget.h,
    w: widget.w
  }));

const transformContextForServer = (item) =>
  (item.layout || []).map((widget) => ({
    id: widget.id,
    guid: widget.i || generateId(),
    x: widget.x,
    y: widget.y,
    h: widget.h,
    w: widget.w
  }));

const transform = (item) => ({
  id: item.id,
  name: item.dashboard_name,
  description: item.description,
  layout: transformContext(item.widget_context),
  createdAt: item.created_time,
  createdBy: item.created_by
});

const transformForServer = async (item) => {
  return Promise.resolve({
    id: item.id,
    dashboard_name: item.name,
    description: item.description,
    widget_context: transformContextForServer(item)
  });
};

const searchableColumns = ['dashboard_name', 'description'];

export function getAllDashboardApi(offset, size, sortFilter = {}) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getDashboardApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateDashboard(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getDashboardApi(data.result));
}

export function createDashboard(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getDashboardApi(data.result));
}

export function deleteDashboard(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
