import { Drawer, Form } from 'antd';
import Merge from 'lodash/merge';
import { useEffect, useRef, useState } from 'react';
import Pick from 'lodash/pick';
import GridLayout from 'react-grid-layout';
import GridItemContainer from '@/src/components/widget/GridItemContainer';
import WidgetSelector from './WidgetSelector';
import { getWidgetDefaultSize, calculateNewItemPosition } from '../helpers/dashboard-helper';
import generateId from '@/src/utils/id';
import { Asset } from '@/src/components/pickers/AssetPicker';
import WidgetForm from './WidgetForm';
import { getWidgetApi, updateWidget } from '../widget-api';
import Bus from '@/src/utils/emitter';
import { Department } from '@/src/components/pickers/DepartmentPicker';

export default function Dashboard({
  dashboard,
  timeline,
  onLayoutChange,
  isWidgetDrawerOpen,
  setWidgetDrawerOpen
}) {
  const [isDragging, setDragging] = useState(false);
  const [width, setWidth] = useState(null);
  const [editingWidget, setEditingWidget] = useState(null);
  const containerRef = useRef(null);
  const [widgetPreviewKey, setWidgetPreviewKey] = useState(0);
  const [widgetForm] = Form.useForm();

  const GRID_COLUMNS = 12;
  const ROW_HEIGHT = 80;

  useEffect(() => {
    if (containerRef.current) {
      setWidth(containerRef.current.offsetWidth);
    }
  }, [containerRef]);

  function handleOnDrop(layout, item, _event) {
    const widgetItem = JSON.parse(_event.dataTransfer.getData('text/plain'));
    let widgetSize = getWidgetDefaultSize(widgetItem);
    if (!(widgetSize.h && widgetSize.w)) {
      widgetSize = { h: 3, w: 6 };
    }

    handleLayoutUpdate([
      ...layout,
      {
        ...widgetSize,
        id: widgetItem.id
      }
    ]);

    alert(`Dropped element props:\n${JSON.stringify(item, ['x', 'y', 'w', 'h'], 2)}`);
  }

  function handleLayoutUpdate(layout) {
    const newLayout = layout.map((item) => ({
      ...(dashboard.layout.find((i) => i.i === item.i) || {
        i: generateId(),
        id: item.id
      }),
      ...Pick(item, ['x', 'y', 'w', 'h'])
    }));
    const newStr = newLayout.reduce(
      (prev, item) => `${prev}-${item.id}-${item.h}-${item.w}-${item.x}-${item.y}`,
      ''
    );
    const oldStr = dashboard.layout.reduce(
      (prev, item) => `${prev}-${item.id}-${item.h}-${item.w}-${item.x}-${item.y}`,
      ''
    );
    if (newStr !== oldStr) {
      onLayoutChange(newLayout);
    }
  }

  function handleAppendWidget(widget) {
    let widgetSize = getWidgetDefaultSize(widget);
    if (!(widgetSize.h && widgetSize.w)) {
      widgetSize = { h: 3, w: 6 };
    }
    const widgetPosition = calculateNewItemPosition(widgetSize, dashboard.layout, GRID_COLUMNS);

    handleLayoutUpdate([
      ...dashboard.layout,
      {
        ...widgetPosition,
        ...widgetSize,
        id: widget.id
      }
    ]);
  }

  function handleRemoveItem(item) {
    handleLayoutUpdate(dashboard.layout.filter((w) => w.i !== item.i));
  }

  function handleEditWidget(item) {
    getWidgetApi(item.id).then((widget) => {
      setEditingWidget(widget);
      widgetForm.setFieldsValue(widget);
      setWidgetPreviewKey(1);
    });
  }

  return (
    <Asset.Provider>
      <Department.Provider>
        <div className="flex flex-1 flex-col min-h-0 bg-page-background mb-4" ref={containerRef}>
          {width && (
            <GridLayout
              className="layout"
              containerPadding={[0, 10]}
              layout={dashboard.layout}
              rowHeight={ROW_HEIGHT}
              cols={GRID_COLUMNS}
              width={width}
              margin={[8, 8]}
              compactType="vertical"
              onDrop={handleOnDrop}
              isDroppable={true}
              onLayoutChange={handleLayoutUpdate}>
              {dashboard.layout.map((item) => (
                <div key={item.i} className="widget-container">
                  <GridItemContainer
                    key={item.i}
                    layout={item}
                    timeline={timeline}
                    widgetId={item.id}
                    onEdit={() => handleEditWidget(item)}
                    onRemove={() => handleRemoveItem(item)}
                  />
                </div>
              ))}
            </GridLayout>
          )}
          <Drawer
            title="Add Widgets"
            placement="right"
            width="50%"
            mask={false}
            onClose={() => setWidgetDrawerOpen(false)}
            rootClassName={isDragging ? 'invisible-drawer' : ''}
            open={isWidgetDrawerOpen}>
            <WidgetSelector
              onDragStart={(e) => setDragging(true)}
              onDragEnd={(e) => setDragging(false)}
              onAppendWidget={handleAppendWidget}
            />
          </Drawer>
          <Drawer
            open={Boolean(editingWidget)}
            onClose={() => setEditingWidget(null)}
            width="95%"
            destroyOnClose
            title={'Edit Widget'}
            maskClosable={false}>
            <Form
              layout="vertical"
              className="h-full"
              form={widgetForm}
              onFinish={(data) => {
                updateWidget({ ...editingWidget, ...data }).then((data) => {
                  setEditingWidget(null);
                  setWidgetPreviewKey(0);
                  Bus.emit('widget:updated', data);
                });
              }}
              onValuesChange={(values) => {
                setEditingWidget(Merge({ ...editingWidget }, { ...values }));
              }}
              initialValues={editingWidget}>
              <WidgetForm
                value={editingWidget || {}}
                formItem={editingWidget || {}}
                shouldRenderPreview={widgetPreviewKey > 0}
                previewRenderKey={widgetPreviewKey}
                widgetContextBuilderFn={(data) => {
                  data.timeline = widgetForm.getFieldValue('timeline');
                  return data;
                }}
                onRerenderPreview={(data = {}) => {
                  if (data.query) {
                    setWidgetPreviewKey((key) => key + 1);
                  }
                }}
                onChange={(data) => {
                  setEditingWidget((existingData) => ({ ...existingData, ...data }));
                }}
              />
            </Form>
          </Drawer>
        </div>
      </Department.Provider>
    </Asset.Provider>
  );
}
