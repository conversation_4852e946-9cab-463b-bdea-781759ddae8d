import { <PERSON><PERSON>, <PERSON>, Col } from 'antd';
import { PlusCircleOutlined, EditOutlined } from '@ant-design/icons';
import { CrudProvider } from '@/src/hooks/crud';
import { getAllWidgetsApi, deleteWidget, createWidget, updateWidget } from '../widget-api';
import { useState } from 'react';
import WidgetForm from './WidgetForm';
import WidgetTypePicker from './WidgetTypePicker';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import Bus from '@/src/utils/emitter';

export default function WidgetSelector({ onDragStart, onDragEnd, onAppendWidget, disabled }) {
  let dragImage;
  const [widgetPreviewKey, setWidgetPreviewKey] = useState(0);

  // eslint-disable-next-line
  function handleDragStart(e, widget) {
    e.dataTransfer.setData('text/plain', JSON.stringify(widget));
    dragImage = e.target.closest('.widget-row').cloneNode(true);
    dragImage.style.width = '250px';
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 0, 0);
    setTimeout(() => {
      onDragStart(e);
    });
  }

  // eslint-disable-next-line
  function handleDragEnd(e) {
    if (dragImage) {
      document.body.removeChild(dragImage);
      dragImage = null;
    }
    setTimeout(() => {
      onDragEnd(e);
    });
  }

  return (
    <div className="flex flex-1 flex-col min-h-0 overflow-auto h-full">
      <div className="flex flex-1 flex-col">
        <CrudProvider
          resourceTitle="Widget"
          disableColumnSelection
          columns={[]}
          onSetForm={(item) =>
            widgetPreviewKey === 0 ? setWidgetPreviewKey(item && item.id ? 1 : 0) : null
          }
          defaultPageSize={100}
          onFormSubmitted={() => setWidgetPreviewKey(0)}
          hasSearch
          formDrawerWidth="95%"
          defaultFormItem={{
            widgetDataFetchingType: 'query_based',
            widgetLiveOrOffline: 'Offline',
            timeline: {
              selected: 'Today'
            }
          }}
          disableFormScrolling
          tableSlot={({ rowKey, data, edit }) => (
            <Row className="mt-4">
              {data.map((widget) => (
                <Col span={12} key={rowKey(widget)} className="">
                  <div className="pr-2">
                    <div className="p-2 mb-4 bg-seperator rounded-lg widget-row droppable-element">
                      <div className="flex justify-between">
                        <div className="icon mr-2">
                          <WidgetTypePicker value={widget.type} onlyImage />
                        </div>
                        <div className="flex-1">
                          <h4 className="m-0">{widget.name}</h4>
                          <small
                            className="text-label"
                            style={{
                              overflow: 'hidden',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              lineClamp: 2,
                              WebkitBoxOrient: 'vertical'
                            }}>
                            {widget.description}
                          </small>
                        </div>
                        <div className="flex items-center shrink-0">
                          <PermissionChecker permission={[constants.Update_Dashboard]} hasAny>
                            <span
                              className="cursor-pointer text-lg mr-2"
                              title="Add Widget"
                              onClick={(e) => onAppendWidget(widget)}>
                              <PlusCircleOutlined />
                            </span>
                          </PermissionChecker>
                          {disabled ? null : (
                            <PermissionChecker permission={[constants.Update_Widget]} hasAny>
                              <span
                                className="cursor-pointer text-lg text-primary"
                                title="Edit Widget"
                                onClick={(e) => edit(widget)}>
                                <EditOutlined />
                              </span>
                            </PermissionChecker>
                          )}
                          {/* <span
                        className="cursor-move droppable-element"
                        draggable={true}
                        onDragStart={(e) => handleDragStart(e, widget)}
                        onDragEnd={handleDragEnd}>
                        <Icon name="question" />
                      </span> */}
                        </div>
                      </div>
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          )}
          fetchFn={getAllWidgetsApi}
          deleteFn={deleteWidget}
          createFn={createWidget}
          updateFn={(data) =>
            updateWidget(data).then((response) => {
              Bus.emit('widget:updated', response);
              return response;
            })
          }
          createSlot={(createFn) =>
            disabled ? null : (
              <PermissionChecker permission={[constants.Create_Widget]} hasAny>
                <Button type="primary" onClick={createFn}>
                  Create Widget
                </Button>
              </PermissionChecker>
            )
          }
          formActions={() => <span />}
          formFields={(widget, updateItem, props) => (
            <WidgetForm
              {...props}
              value={widget}
              onChange={updateItem}
              shouldRenderPreview={widgetPreviewKey > 0}
              previewRenderKey={widgetPreviewKey}
              onRerenderPreview={() => {
                if (widget.query) {
                  setWidgetPreviewKey((key) => key + 1);
                }
              }}
            />
          )}
        />
      </div>
    </div>
  );
}
