import Icon from '@/src/components/Icon';

export default function WidgetTypePicker({ value, onChange, disabled, onlyImage, style = {} }) {
  const widgetTypeOptions = [
    { label: 'Grid', value: 'Grid', iconName: 'grid' },
    { label: 'Bar Chart', value: 'BarChart', iconName: 'vertical_bar' },
    { label: 'Stacked Bar Chart', value: 'StackedBarChart', iconName: 'vertical_bar' },
    { label: 'Area Chart', value: 'AreaChart', iconName: 'area' },
    { label: 'Stacked Area Chart', value: 'StackedAreaChart', iconName: 'area' },
    { label: 'Line Chart', value: 'LineChart', iconName: 'line_chart' },
    { label: 'Stacked Line Chart', value: 'StackedLineChart', iconName: 'line_chart' },
    { label: 'Pie Chart', value: 'PieChart', iconName: 'pie' },
    { label: 'Gauge', value: 'Gauge', iconName: 'gauge' },
    { label: 'Vulnerability Matrix', value: 'VulnerabilityMatrix', iconName: 'grid' },
    { label: 'Sankey', value: 'Sankey', iconName: 'sankey' }
  ];

  if (onlyImage) {
    const option = widgetTypeOptions.find((i) => i.value === value);
    if (option) {
      return <Icon name={option.iconName} style={{ fontSize: '2rem', ...style }} />;
    }
  }

  return (
    <div className="flex items-center border-solid border-border rounded">
      {widgetTypeOptions.map((item) => (
        <div
          className={`flex flex-col p-2 my-1 mx-2 cursor-pointer rounded-lg items-center`}
          style={
            item.value === value
              ? {
                  background: 'var(--primary)',
                  color: 'white'
                }
              : {}
          }
          key={item.value}
          onClick={() => onChange(item.value)}>
          <Icon name={item.iconName} style={{ fontSize: '2rem' }} />
          {item.label}
        </div>
      ))}
    </div>
  );
}
