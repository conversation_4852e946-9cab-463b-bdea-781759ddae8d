import Icon from '@/src/components/Icon';
import PermissionChecker from '@/src/components/PermissionChecker';
import { EditOutlined } from '@ant-design/icons';
import constants from '@/src/constants/index';
import { CrudProvider } from '@/src/hooks/crud';
import { Popover, Button, Row, Col, Form, Input } from 'antd';
import { Fragment, useState } from 'react';
import {
  createDashboard,
  deleteDashboard,
  getAllDashboardApi,
  updateDashboard
} from '../dashboard-api';
import { useNavigate, useParams } from 'react-router-dom';

function DashboardCrud({ onNavigate }) {
  const navigate = useNavigate();
  const params = useParams();

  function visitDashboard(dashboard) {
    if (+params.id === dashboard.id) {
      return;
    }
    navigate(`/dashboard/${dashboard.id}`);
    onNavigate();
  }

  return (
    <div className="flex flex-1 flex-col min-h-0 overflow-auto h-full">
      <div className="flex flex-1 flex-col">
        <CrudProvider
          resourceTitle="Dashboard"
          disableColumnSelection
          columns={[]}
          hasSearch
          disableFormScrolling
          drawerProps={{
            zIndex: 1050
          }}
          tableSlot={({ rowKey, data, edit }) => (
            <Row className="mt-4" gutter={16}>
              {data.map((dashboard) => (
                <Col span={24} key={rowKey(dashboard)}>
                  <div
                    className={`p-2 mb-4 rounded-lg bg-seperator dashboard-list-item droppable-element ${
                      +params.id === dashboard.id ? 'active' : 'cursor-pointer'
                    }`}
                    onClick={() => visitDashboard(dashboard)}>
                    <div className="flex justify-between">
                      <div className="flex-1">
                        <h4 className="m-0">{dashboard.name}</h4>
                        <small
                          className="text-label"
                          style={{
                            overflow: 'hidden',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            lineClamp: 2,
                            WebkitBoxOrient: 'vertical'
                          }}>
                          {dashboard.description}
                        </small>
                      </div>
                      <div className="flex items-center shrink-0">
                        <PermissionChecker permission={[constants.Update_Dashboard]} hasAny>
                          <span
                            className="cursor-pointer text-lg text-primary"
                            title="Edit Widget"
                            onClick={(e) => edit(dashboard)}>
                            <EditOutlined />
                          </span>
                        </PermissionChecker>
                        {/* <span
                        className="cursor-move droppable-element"
                        draggable={true}
                        onDragStart={(e) => handleDragStart(e, widget)}
                        onDragEnd={handleDragEnd}>
                        <Icon name="question" />
                      </span> */}
                      </div>
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          )}
          fetchFn={getAllDashboardApi}
          deleteFn={deleteDashboard}
          createFn={createDashboard}
          updateFn={updateDashboard}
          createSlot={(createFn) => (
            <PermissionChecker permission={[constants.Create_Dashboard]} hasAny>
              <Button type="primary" onClick={createFn}>
                Create Dashboard
              </Button>
            </PermissionChecker>
          )}
          formFields={(dashboard) => (
            <Fragment>
              <Row gutter={32}>
                <Col span={12}>
                  <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                    <Input placeholder="Name" />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item label="Description" name="description" rules={[{ required: true }]}>
                    <Input.TextArea placeholder="Description" />
                  </Form.Item>
                </Col>
              </Row>
            </Fragment>
          )}
        />
      </div>
    </div>
  );
}

export default function DashboardSelector() {
  const [open, setOpen] = useState(false);

  return (
    <Popover
      placement="bottomLeft"
      content={<DashboardCrud onNavigate={() => setOpen(false)} />}
      overlayClassName="dashboard-selector"
      arrow={false}
      trigger="click"
      open={open}>
      <div
        className="cursor-pointer icon-box bg-table-header px-2 py-2 rounded-md inline-flex items-center"
        onClick={() => setOpen(!open)}>
        <Icon name="dashboard" className="text-primary-500" style={{ fontSize: '1.5rem' }} />
        <Icon name="chevron-down" className="text-primary-500 ml-2" style={{ fontSize: '1rem' }} />
      </div>
    </Popover>
  );
}
