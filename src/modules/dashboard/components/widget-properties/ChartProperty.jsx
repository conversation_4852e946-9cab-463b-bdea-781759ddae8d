import colorPalettes from '@/src/components/widget/views/chart/chart-colors';
import { Row, Col, InputNumber, Slider, Input, Form, Switch, Select } from 'antd';

export default function ChartProperty({ widget }) {
  const colorOptions = Object.keys(colorPalettes).map((i) => ({
    value: i,
    label: (
      <div className="flex items-center justify-around h-8">
        {colorPalettes[i].map((color) => (
          <div
            key={color}
            style={{
              flex: 1,
              height: '100%',
              borderRadius: '2px',
              background: color
            }}></div>
        ))}
      </div>
    )
  }));
  return (
    <Row gutter={32}>
      <Col span={12}>
        <Form.Item label="Chart Colors" name={['widgetProperties', 'colorPalette']}>
          <Select options={colorOptions} />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Row gutter={32}>
          {['PieChart', 'Sankey'].includes(widget.type) === false && (
            <Col span={12}>
              <Form.Item label="Rotation" name={['widgetProperties', 'rotation']}>
                <InputNumber placeholder="Rotation" precision={0} prefix={'°'} className="w-full" />
              </Form.Item>
            </Col>
          )}
          <Col span={12}>
            <Form.Item label="Transparency" name={['widgetProperties', 'opacity']}>
              <Slider min={0.1} max={1} step={0.1} />
            </Form.Item>
          </Col>
        </Row>
      </Col>
      <Col span={12}>
        <Form.Item
          label="Legend"
          name={['widgetProperties', 'legendEnabled']}
          valuePropName="checked">
          <Switch />
        </Form.Item>
      </Col>
      {widget.type === 'PieChart' && (
        <Col span={12}>
          <Form.Item
            label="Data Labels"
            name={['widgetProperties', 'pieDataLabelsEnabled']}
            valuePropName="checked">
            <Switch />
          </Form.Item>
        </Col>
      )}
      {['PieChart', 'Sankey'].includes(widget.type) === false && (
        <>
          <Col span={12}>
            <Form.Item label="X-Axis Title" name={['widgetProperties', 'xAxisTitle']}>
              <Input placeholder="X-Axis Title" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Y-Axis Title" name={['widgetProperties', 'yAxisTitle']}>
              <Input placeholder="Y-Axis Title" />
            </Form.Item>
          </Col>
        </>
      )}
    </Row>
  );
}
