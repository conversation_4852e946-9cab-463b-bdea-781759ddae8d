import ChartProperty from './ChartProperty';
import GaugeProperty from './GaugeProperty';
import GridProperty from './GridProperty';

export default function WidgetProperty({ widget, columns, onChange }) {
  if (widget.type === 'Grid') {
    return <GridProperty columns={columns} widget={widget} onChange={onChange} />;
  } else if (widget.type === 'Gauge') {
    return <GaugeProperty widget={widget} />;
  } else if (widget.type === 'VulnerabilityMatrix') {
    return null;
  }

  return <ChartProperty widget={widget} />;
}
