import { Row, Col, Form, Input, InputNumber, ColorPicker, Select, Radio, Button } from 'antd';
import Repeater from '@/src/components/Repeater';
import Icon from '@/src/components/Icon';

export default function GaugeProperty({ disabled }) {
  const labels = {
    lt: 'Less Than',
    gt: 'Greater Than',
    eq: 'Equal to',
    gte: 'Greater Than or Equal to',
    lte: 'Less Than or Equal to'
  };

  const operatorOptions = ['lt', 'gt', 'eq', 'gte', 'lte'].map((i) => ({
    label: labels[i],
    value: i
  }));

  const alignOptions = [
    { label: 'Left', value: 'left' },
    { label: 'Center', value: 'center' },
    { label: 'Right', value: 'right' }
  ];

  const vAlignOptions = [
    { label: 'Top', value: 'top' },
    { label: 'Center', value: 'center' },
    { label: 'Bottom', value: 'bottom' }
  ];

  const form = Form.useFormInstance();

  return (
    <Row gutter={32}>
      <Col span={12}>
        <Form.Item label="Horizontal Alignment" name={['widgetProperties', 'align']}>
          <Radio.Group options={alignOptions} optionType="button" buttonStyle="solid" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="Vertical Alignment" name={['widgetProperties', 'valign']}>
          <Radio.Group options={vAlignOptions} optionType="button" buttonStyle="solid" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="Unit" name={['widgetProperties', 'unit']}>
          <Input placeholder="Unit" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="Label" name={['widgetProperties', 'label']}>
          <Input placeholder="Label (Suffix)" />
        </Form.Item>
      </Col>
      <Col span={24}>
        <h5>Color Configuration</h5>
      </Col>
      <Col span={24}>
        <Repeater
          name={['widgetProperties', 'colorConfig']}
          defaultItem={{}}
          addBtnText="Add Color Condition">
          {({ key, name, ...restField }, actions) => (
            <div className="flex flex-col" key={key}>
              <div className="flex items-center">
                <div
                  className={`flex-1 mr-2 px-2 py-1 rounded border-solid border-border
                          ${disabled ? '' : 'bg-border'}`}>
                  <Row gutter={32}>
                    <Col span={5}>
                      <Form.Item
                        {...restField}
                        name={[name, 'color']}
                        label="Background Color"
                        rules={[{ required: true }]}>
                        <ColorPicker
                          size="small"
                          showText
                          presets={[
                            {
                              label: 'Recommended',
                              colors: ['#267ED4', '#89C540', '#EB5758', '#F2994B', '#F2C84C']
                            }
                          ]}
                          format="hex"
                          onChange={(e) => {
                            form.setFieldValue(
                              ['widgetProperties', 'colorConfig', name, 'color'],
                              e.toHexString()
                            );
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={5}>
                      <Form.Item
                        {...restField}
                        name={[name, 'textColor']}
                        label="Foreground Color"
                        rules={[{ required: true }]}>
                        <ColorPicker
                          size="small"
                          showText
                          defaultValue="transparent"
                          presets={[
                            {
                              label: 'Recommended',
                              colors: ['#267ED4', '#89C540', '#EB5758', '#F2994B', '#F2C84C']
                            }
                          ]}
                          format="hex"
                          onChange={(e) => {
                            form.setFieldValue(
                              ['widgetProperties', 'colorConfig', name, 'textColor'],
                              e.toHexString()
                            );
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        {...restField}
                        name={[name, 'operator']}
                        label="Operator"
                        rules={[{ required: true }]}>
                        <Select options={operatorOptions} />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        {...restField}
                        name={[name, 'value']}
                        label="Value"
                        rules={[{ required: true }]}>
                        <InputNumber placeholder="Rotation" precision={2} className="w-full" />
                      </Form.Item>
                    </Col>
                    <Col span={2}>
                      <Button shape="circle" type="danger" onClick={() => actions.remove(name)}>
                        <Icon name="close" className="text-danger" style={{ fontSize: '1.5rem' }} />
                      </Button>
                    </Col>
                  </Row>
                </div>
              </div>
            </div>
          )}
        </Repeater>
      </Col>
    </Row>
  );
}
