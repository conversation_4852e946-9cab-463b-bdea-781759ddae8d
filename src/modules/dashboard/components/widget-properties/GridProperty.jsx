import Icon from '@/src/components/Icon';
import Repeater from '@/src/components/Repeater';
import { ArrowUpOutlined, ArrowDownOutlined, SettingOutlined } from '@ant-design/icons';
import { DragOutlined } from '@ant-design/icons';
import { Row, Col, Checkbox, Input, Divider, Button, Modal, Form, Select, ColorPicker } from 'antd';
import FindIndex from 'lodash/findIndex';
import { useState } from 'react';
import { ReactSortable } from 'react-sortablejs';

export const OPERATOR_MAP = {
  eq: 'Equals',
  neq: 'Not Equals',
  lt: 'Less Than',
  gt: 'Greater Than',
  lte: 'Less than or Equal',
  gte: 'Greater than or Equal',
  contain: 'Contains'
};

export function getOperatorOptions(allowedOptions = []) {
  if (allowedOptions.length) {
    return Object.keys(OPERATOR_MAP)
      .filter((key) => allowedOptions.indexOf(key) >= 0)
      .map((value) => ({ value, label: OPERATOR_MAP[value] }));
  }
  return Object.keys(OPERATOR_MAP).map((value) => ({
    value,
    label: OPERATOR_MAP[value]
  }));
}

export default function GridProperty({ columns, widget, onChange }) {
  const [editingColumnConfiguration, setEditingColumnConfiguration] = useState(null);
  const [columnConfigForm] = Form.useForm();

  function handleColumnChange(column) {
    const index = FindIndex(columns, { key: column.key });
    if (index !== -1) {
      onChange({
        columns: [...columns.slice(0, index), { ...column }, ...columns.slice(index + 1)]
      });
    }
  }
  return (
    <>
      <Row gutter={32}>
        <Col span={6}>Key</Col>
        <Col span={6}>Title</Col>
        <Col span={6}>Ellipsis</Col>
        <Col span={4}>Hidden</Col>
        <Col span={2}></Col>
      </Row>
      <Divider />
      <ReactSortable
        handle=".sort-icon"
        list={columns}
        setList={(columns) => onChange({ columns })}>
        {(columns || []).map((c) => (
          <Row gutter={32} key={c.key} className="mb-2">
            <Col span={6}>
              <DragOutlined className="sort-icon mr-2 cursor-move" />
              {c.key}
            </Col>
            <Col span={6}>
              <Input
                value={c.title}
                onChange={(e) => handleColumnChange({ ...c, title: e.target.value })}
              />
            </Col>
            <Col span={6}>
              <Checkbox
                checked={c.ellipsis}
                onChange={(e) => handleColumnChange({ ...c, ellipsis: e.target.checked })}
              />
            </Col>
            <Col span={4}>
              <Checkbox
                checked={c.hidden}
                onChange={(e) => handleColumnChange({ ...c, hidden: e.target.checked })}
              />
            </Col>
            <Col span={2}>
              <Button type="link" onClick={() => setEditingColumnConfiguration(c)}>
                <SettingOutlined />
              </Button>
            </Col>
          </Row>
        ))}
      </ReactSortable>
      <Modal
        open={Boolean(editingColumnConfiguration)}
        title={editingColumnConfiguration ? `${editingColumnConfiguration.title}` : null}
        onCancel={() => setEditingColumnConfiguration(null)}
        destroyOnClose={true}
        width="65%"
        footer={null}>
        <Form
          layout="vertical"
          className="flex flex-col"
          form={columnConfigForm}
          initialValues={{ ...editingColumnConfiguration }}
          onFinish={(data) => {
            handleColumnChange({ ...editingColumnConfiguration, ...data });
            setEditingColumnConfiguration(null);
          }}
          style={{ height: '50vh' }}>
          <div className="flex flex-1 min-h-0 overflow-y-auto">
            <div className="flex-1">
              <Form.Item label="Color Configuration">
                <Repeater
                  name="colorConfiguration"
                  itemLabel="Color Configuration"
                  addBtnText={`Add New Color Configuration`}
                  defaultItem={() => ({
                    operator: 'eq',
                    colorType: 'background'
                  })}>
                  {({ key, name, index }, actions) => (
                    <div className="flex flex-col mb-4" key={key}>
                      <div className="flex items-start">
                        <div className={`flex-1 px-2 py-1`}>
                          <Row className="items-start" gutter={8}>
                            <Col span={20} className="">
                              <Row gutter={8} className="items-start">
                                <Col span={6}>
                                  <div className="flex flex-1 items-center">
                                    <span className="relative mr-2" style={{ top: '-0.75rem' }}>
                                      {' '}
                                      If value{' '}
                                    </span>
                                    <div className="flex-1">
                                      <Form.Item
                                        label=""
                                        name={[name, 'operator']}
                                        rules={[{ required: true }]}>
                                        <Select options={getOperatorOptions()} />
                                      </Form.Item>
                                    </div>
                                  </div>
                                </Col>
                                <Col span={6}>
                                  <Form.Item
                                    label=""
                                    name={[name, 'value']}
                                    rules={[{ required: true }]}>
                                    <Input placeholder="Threshold" />
                                  </Form.Item>
                                </Col>
                                <Col span={6}>
                                  <div className="flex flex-1 items-center">
                                    <span className="relative mr-2" style={{ top: '-0.75rem' }}>
                                      {' '}
                                      Set{' '}
                                    </span>
                                    <div className="flex-1">
                                      <Form.Item
                                        label=""
                                        name={[name, 'colorType']}
                                        rules={[{ required: true }]}>
                                        <Select
                                          options={[
                                            { value: 'background', label: 'Background' },
                                            { value: 'foreground', label: 'Foreground' }
                                          ]}
                                        />
                                      </Form.Item>
                                    </div>
                                  </div>
                                </Col>
                                <Col span={6}>
                                  <div className="flex flex-1 items-center">
                                    <span className="relative mr-2" style={{ top: '-0.75rem' }}>
                                      {' '}
                                      To{' '}
                                    </span>
                                    <div className="flex-1">
                                      <Form.Item
                                        label=""
                                        name={[name, 'color']}
                                        rules={[{ required: true }]}>
                                        <ColorPicker
                                          size="small"
                                          showText
                                          className="w-full"
                                          presets={[
                                            {
                                              label: 'Recommended',
                                              colors: [
                                                '#267ED4',
                                                '#89C540',
                                                '#EB5758',
                                                '#F2994B',
                                                '#F2C84C'
                                              ]
                                            }
                                          ]}
                                          format="hex"
                                          onChange={(e) => {
                                            columnConfigForm.setFieldValue(
                                              ['colorConfiguration', name, 'color'],
                                              e.toHexString()
                                            );
                                          }}
                                        />
                                      </Form.Item>
                                    </div>
                                  </div>
                                </Col>
                              </Row>
                            </Col>
                            <Col span={4} className="flex items-center">
                              <Button
                                shape="circle"
                                className="mr-1"
                                title="Move Up"
                                disabled={index === 0}
                                onClick={() => actions.move(index, index - 1)}>
                                <ArrowUpOutlined />
                              </Button>
                              <Button
                                shape="circle"
                                className="mr-1"
                                title="Move Down"
                                disabled={index === actions.length - 1}
                                onClick={() => actions.move(index, index + 1)}>
                                <ArrowDownOutlined />
                              </Button>
                              <Button
                                shape="circle"
                                type="danger"
                                onClick={() => actions.remove(name)}>
                                <Icon
                                  name="close"
                                  className="text-danger"
                                  style={{ fontSize: '1.5rem' }}
                                />
                              </Button>
                            </Col>
                          </Row>
                        </div>
                      </div>
                    </div>
                  )}
                </Repeater>
              </Form.Item>
              <Form.Item label="Value Configuration">
                <Repeater
                  name="valueConfiguration"
                  itemLabel="Value Configuration"
                  addBtnText={`Add New Value Configuration`}
                  defaultItem={() => ({
                    operator: 'eq',
                    valueType: 'replace'
                  })}>
                  {({ key, name, index }, actions) => (
                    <div className="flex flex-col mb-4" key={key}>
                      <div className="flex items-start">
                        <div className={`flex-1 px-2 py-1`}>
                          <Row className="items-start" gutter={8}>
                            <Col span={20} className="">
                              <Row gutter={8} className="items-start">
                                <Col span={6}>
                                  <div className="flex flex-1 items-center">
                                    <span className="relative mr-2" style={{ top: '-0.75rem' }}>
                                      {' '}
                                      If value{' '}
                                    </span>
                                    <div className="flex-1">
                                      <Form.Item
                                        label=""
                                        name={[name, 'operator']}
                                        rules={[{ required: true }]}>
                                        <Select options={getOperatorOptions()} />
                                      </Form.Item>
                                    </div>
                                  </div>
                                </Col>
                                <Col span={6}>
                                  <Form.Item
                                    label=""
                                    name={[name, 'comparedValue']}
                                    rules={[{ required: true }]}>
                                    <Input placeholder="Threshold" />
                                  </Form.Item>
                                </Col>
                                <Col span={6}>
                                  <div className="flex flex-1 items-center">
                                    <span className="relative mr-2" style={{ top: '-0.75rem' }}>
                                      {' '}
                                      Set{' '}
                                    </span>
                                    <div className="flex-1">
                                      <Form.Item
                                        label=""
                                        name={[name, 'valueType']}
                                        rules={[{ required: true }]}>
                                        <Select
                                          options={[
                                            { value: 'replace', label: 'Replace' },
                                            { value: 'prefix', label: 'Prefix' },
                                            { value: 'suffix', label: 'Suffix' }
                                          ]}
                                        />
                                      </Form.Item>
                                    </div>
                                  </div>
                                </Col>
                                <Col span={6}>
                                  <div className="flex flex-1 items-center">
                                    <span className="relative mr-2" style={{ top: '-0.75rem' }}>
                                      {' '}
                                      To{' '}
                                    </span>
                                    <div className="flex-1">
                                      <Form.Item
                                        label=""
                                        name={[name, 'value']}
                                        rules={[{ required: true }]}>
                                        <Input placeholder="Value" />
                                      </Form.Item>
                                    </div>
                                  </div>
                                </Col>
                              </Row>
                            </Col>
                            <Col span={4} className="flex items-center">
                              <Button
                                shape="circle"
                                className="mr-1"
                                title="Move Up"
                                disabled={index === 0}
                                onClick={() => actions.move(index, index - 1)}>
                                <ArrowUpOutlined />
                              </Button>
                              <Button
                                shape="circle"
                                className="mr-1"
                                title="Move Down"
                                disabled={index === actions.length - 1}
                                onClick={() => actions.move(index, index + 1)}>
                                <ArrowDownOutlined />
                              </Button>
                              <Button
                                shape="circle"
                                type="danger"
                                onClick={() => actions.remove(name)}>
                                <Icon
                                  name="close"
                                  className="text-danger"
                                  style={{ fontSize: '1.5rem' }}
                                />
                              </Button>
                            </Col>
                          </Row>
                        </div>
                      </div>
                    </div>
                  )}
                </Repeater>
              </Form.Item>
            </div>
          </div>
          <div className="flex-shrink-0 text-right">
            <Button type="primary" htmlType="submit" className="mr-2">
              Update
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
}
