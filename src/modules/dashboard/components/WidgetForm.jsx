/* eslint-disable no-unused-vars */
import { useState } from 'react';
import IsEqual from 'lodash/isEqual';
import { Row, Col, Button, Form, Radio, Input, Select, Divider } from 'antd';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';
import WidgetContainer from '@/src/components/widget/WidgetContainer';
import WidgetProperty from './widget-properties/WidgetProperty';
import WidgetTypePicker from './WidgetTypePicker';
import QueryEditor from '@/src/components/pickers/QueryEditor';
import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';

export default function WidgetForm({
  value,
  onChange,
  onRerenderPreview,
  shouldRenderPreview,
  processingForm,
  formItem,
  resetForm,
  previewRenderKey,
  disabledTimeline,
  hideResetButton,
  submitBtnLabel,
  widgetContextBuilderFn
}) {
  const [widgetColumns, setWidgetColumns] = useState([]);
  const form = Form.useFormInstance();

  const widgetLiveOrOfflineOptions = [
    { label: 'Live', value: 'Live', disabled: true },
    { label: 'Offline', value: 'Offline' }
  ];

  return (
    <div className="flex h-full min-h-0 flex-col min-w-0">
      <Row gutter={32}>
        <Col span={24}>
          <Form.Item label="" name="type" rules={[{ required: true }]}>
            <WidgetTypePicker
              onChange={(v) => {
                onChange({ type: v });
                if (value.type !== 'Grid' && v === 'Grid') {
                  onRerenderPreview();
                } else if (value.type === 'Grid' && v !== 'Grid') {
                  onRerenderPreview();
                }
              }}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={32} className="h-full min-h-0 flex flex-1">
        <Col span={8} className="h-full flex-1 min-h-0 overflow-auto">
          <Form.Item name="widgetDataFetchingType">
            <Radio.Group onChange={onChange} value={value}>
              <Radio value="query_based">Query Based</Radio>
              {/* <Radio value="form_based" disabled>
                Form Based
              </Radio> */}
            </Radio.Group>
          </Form.Item>
          <Form.Item label="" name="widgetLiveOrOffline" rules={[{ required: true }]}>
            <Select placeholder="Widget Operation Type" options={widgetLiveOrOfflineOptions} />
          </Form.Item>
          <Form.Item label="" name="name" rules={[{ required: true }]}>
            <Input placeholder="Name" />
          </Form.Item>
          {disabledTimeline ? null : (
            <Form.Item label="Timeline" name={'timeline'}>
              <TimelinePicker />
            </Form.Item>
          )}
          {/* <Form.Item label=" " name="scope"> */}
          <AssetScopePicker
            label="Scope"
            gutter={16}
            skipProvider={true}
            name={['scope', 'assetFilter']}
            subname={['scope', 'assets']}
          />
          {/* </Form.Item> */}
          <Form.Item label="Description" name="description">
            <Input.TextArea placeholder="Description" />
          </Form.Item>
          <Form.Item name="query" label="Query" rules={[{ required: true }]}>
            <QueryEditor />
          </Form.Item>
          {value?.type !== 'Grid' ? (
            <Row gutter={32}>
              <Col span={12}>
                <Form.Item
                  label={value.type === 'Gauge' ? 'Result Key' : 'X-Axis'}
                  name="xAxis"
                  rules={[{ required: true }]}>
                  <Input placeholder={value.type === 'Gauge' ? 'Result Key' : 'X-Axis'} />
                </Form.Item>
              </Col>
              {value.type !== 'Gauge' ? (
                <Col span={12}>
                  <Form.Item label="Y-Axis" name="yAxis" rules={[{ required: true }]}>
                    <Input placeholder="Y-Axis" />
                  </Form.Item>
                </Col>
              ) : null}
            </Row>
          ) : null}
        </Col>
        <Col
          span={16}
          className="border-solid border-l-1 border-r-0 border-t-0 border-b-0 border-border h-full flex flex-col flex-1 min-h-0 overflow-auto">
          <div className="flex flex-col flex-1 min-h-0">
            <div className="h-1/2 flex flex-col">
              {shouldRenderPreview > 0 ? (
                <WidgetContainer
                  widget={
                    widgetContextBuilderFn
                      ? widgetContextBuilderFn({ ...value, ...form.getFieldsValue() })
                      : { ...value, ...form.getFieldsValue() }
                  }
                  key={previewRenderKey}
                  isPreview={true}
                  onDataReceived={(data) => {
                    if (
                      value &&
                      value.widgetProperties &&
                      !IsEqual(value?.widgetProperties?.columns, data?.columns)
                    ) {
                      onChange({
                        widgetProperties: { ...value.widgetProperties, columns: data?.columns }
                      });
                      setWidgetColumns(data);
                    }
                  }}
                />
              ) : (
                <div className="flex flex-1 items-center justify-center">
                  Click Generate Preview to view preview
                </div>
              )}
            </div>
            <Divider />
            {shouldRenderPreview ? (
              <div className="h-1/2">
                <WidgetProperty
                  widget={value}
                  key={previewRenderKey}
                  columns={
                    ((value.widgetProperties || {}).columns || []).length > 0
                      ? value.widgetProperties.columns
                      : widgetColumns
                  }
                  onChange={(widgetProperties) => {
                    onChange({ widgetProperties });
                    if ('columns' in widgetProperties) {
                      setWidgetColumns(widgetProperties.columns);
                    }
                  }}
                />
              </div>
            ) : null}
          </div>
        </Col>
      </Row>
      <Row gutter={32}>
        <Col span={8} className="my-2 text-right flex justify-evenly">
          <Button type="primary" loading={processingForm} htmlType="submit" className="mr-2 flex-1">
            {submitBtnLabel || (formItem && formItem.id ? 'Update' : 'Create')}
          </Button>
          <Button
            type="primary"
            ghost
            loading={processingForm}
            onClick={() => onRerenderPreview(form.getFieldsValue())}
            htmlType="button"
            className="mr-2 flex-1">
            Generate Preview
          </Button>
          {hideResetButton ? null : (
            <Button type="primary" ghost htmlType="reset" onClick={resetForm} className="flex-1">
              Reset
            </Button>
          )}
        </Col>
      </Row>
    </div>
  );
}
