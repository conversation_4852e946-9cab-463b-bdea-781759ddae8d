import MaxBy from 'lodash/maxBy';

export function getWidgetDefaultSize() {
  // @TODO change size based on widget type
  return {
    h: 3,
    w: 6
  };
}

export function calculateNewItemPosition(itemSize, widgets, gridColumns) {
  if (!widgets.length) {
    return {
      x: 0,
      y: 0
    };
  }
  const maxRowItem = MaxBy(widgets, ({ y }) => y);
  const itemsInLastRow = widgets.filter(({ y }) => y === maxRowItem.y);
  const maxColumnItem = MaxBy(itemsInLastRow, ({ x }) => x);
  const availableColumns = gridColumns - (maxColumnItem.x + maxColumnItem.w);
  if (availableColumns >= itemSize.w) {
    return {
      y: maxRowItem.y,
      x: maxColumnItem.x + maxColumnItem.w
    };
  }
  return {
    y: maxRowItem.y + 1,
    x: 0
  };
}
