import { Spin, Button } from 'antd';
import { useState, useEffect, useRef } from 'react';
import { DownloadOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import Bus from '@/src/utils/emitter';
import PageHeading from '@/src/components/PageHeading';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';
import Dashboard from '../components/Dashboard';
import { getDashboardApi, updateDashboard } from '../dashboard-api';
import exportScrollableElement from '@/src/utils/scrollable-capture';
import Icon from '@/src/components/Icon';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import DashboardSelector from '../components/DashboardSelector';

export default function DashboardPage() {
  const navigate = useNavigate();
  const [isWidgetDrawerOpen, setWidgetDrawerOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [dashboard, setDashboard] = useState(null);
  const [timeline, setTimeline] = useState({});
  const params = useParams();
  const gridContainerDivRef = useRef(null);

  useEffect(() => {
    getDashboardApi(params.id)
      .then((data) => {
        setDashboard(data);
        setLoading(false);
      })
      .catch(() => {
        navigate('/404');
      });
    // eslint-disable-next-line
  }, [params]);

  function handleLayoutUpdated(layout) {
    updateDashboard({ ...dashboard, layout }).then((dashboard) => setDashboard(dashboard));
  }

  async function exportDashboard() {
    document.querySelectorAll('.ant-float-btn-circle').forEach((i) => {
      i.style.display = 'none';
    });
    await exportScrollableElement(gridContainerDivRef.current, dashboard.name);
    document.querySelectorAll('.ant-float-btn-circle').forEach((i) => {
      i.style.display = 'block';
    });
  }

  function handleRefreshDashboard() {
    Bus.emit('refresh:dashboard');
  }

  return loading ? (
    <div className="flex items-center justify-center w-full h-full">
      <Spin spinning={loading} />
    </div>
  ) : (
    <>
      <PageHeading icon={() => <DashboardSelector />} title={dashboard.name} />
      <div className="flex items-center justify-end">
        <PermissionChecker permission={[constants.Update_Dashboard, constants.View_Widget]} hasAny>
          <Button
            type="primary"
            ghost
            className="mr-2"
            onClick={() => setWidgetDrawerOpen(!isWidgetDrawerOpen)}>
            <Icon name="add" />
            Add Widget
          </Button>
        </PermissionChecker>
        <Button type="primary" className="mr-2" ghost onClick={handleRefreshDashboard}>
          <Icon name="refresh" />
          Refresh
        </Button>
        <TimelinePicker value={timeline} onChange={setTimeline} />
        <Button className="ml-2" type="primary" ghost onClick={exportDashboard}>
          <DownloadOutlined />
        </Button>
      </div>
      <div
        className="flex flex-col min-h-0 overflow-auto bg-page-background h-full"
        ref={gridContainerDivRef}>
        <Dashboard
          dashboard={dashboard}
          isWidgetDrawerOpen={isWidgetDrawerOpen}
          setWidgetDrawerOpen={setWidgetDrawerOpen}
          timeline={timeline}
          onLayoutChange={handleLayoutUpdated}
        />
      </div>
    </>
  );
}
