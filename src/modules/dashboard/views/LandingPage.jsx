import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Spin } from 'antd';
import { getAllDashboardApi } from '../dashboard-api';

export default function LandingPage() {
  const navigate = useNavigate();

  useEffect(() => {
    getAllDashboardApi(0, 100).then((response) => {
      if (response) {
        // @TODO check here if user has default dashboard preference
        navigate(`/dashboard/${response.result[0].id}`, { replace: true });
      }
    });
  });

  return (
    <div className="flex flex-col justify-center items-center h-full">
      <Spin spinning={true} />
    </div>
  );
}
