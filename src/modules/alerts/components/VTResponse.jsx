import { Row, Col, Tag, Progress } from 'antd';
import { ReactComponent as CrossIcon } from '@/src/icons/general/cross.svg';
import { ReactComponent as CheckIcon } from '@/src/icons/general/check.svg';
import { WarningOutlined } from '@ant-design/icons';
import ReactCountryFlag from 'react-country-flag';
import { VirusTotalLink } from '@/src/components/VirusTotalLink';
import { useAuth } from '@/src/hooks/auth';

export default function VTResponse({ data }) {
  const { formatDateTime } = useAuth();

  let isIp = data.network ? true : false;

  let malicious = (data.last_analysis_stats || {}).malicious || 0;

  const excludedKeys = ['timeout'];

  let total = Object.keys(data.last_analysis_stats || {}).reduce(
    (prev, key) => prev + (excludedKeys.includes(key) ? 0 : data.last_analysis_stats[key]),
    0
  );

  let keys = {
    ip: [
      {
        key: 'last_analysis_date',
        label: 'Last Analysis Date'
      },
      {
        key: 'country',
        label: 'Country',
        render(value) {
          return (
            <>
              <ReactCountryFlag
                title={value}
                countryCode={value}
                svg
                style={{
                  width: '1em',
                  height: '1em'
                }}
              />
              <span className="ml-2">{value}</span>
            </>
          );
        }
      },
      {
        key: 'continent',
        label: 'Continent'
      },
      {
        key: 'whois_date',
        label: 'Whois Date'
      },
      {
        key: 'last_modification_date',
        label: 'Last Modification Date'
      },
      {
        key: 'regional_internet_registry',
        label: 'Regional Internet Registry'
      },
      {
        key: 'as_owner',
        label: 'Owner'
      },
      {
        key: 'asn',
        label: 'ASN'
      },
      {
        key: 'reputation',
        label: 'Reputation'
      }
    ],
    process: [
      {
        key: 'last_analysis_date',
        label: 'Last Analysis Date',
        render(value) {
          return value ? formatDateTime(value * 1000) : '';
        }
      },
      {
        key: 'last_submission_date',
        label: 'Last Submission Date',
        render(value) {
          return value ? formatDateTime(value * 1000) : '';
        }
      }
    ]
  };

  return (
    <Row>
      <Col span={24}>
        <div className="bg-seperator px-4 py-2 rounded">
          <Row gutter={32}>
            <Col span={4}>
              <Progress
                type="circle"
                percent={Math.ceil((malicious * 100) / total)}
                status="exception"
                format={() => `${malicious}/${total}`}
              />
            </Col>
            <Col span={20} className="flex flex-col justify-evenly">
              <Row className="mb-6">
                <Col span={8}>
                  <div className="text-neutral-light mb-2">Result Source</div>
                  <div className="font-bold">VirusTotal</div>
                </Col>
                <Col span={8}>
                  <div className="text-neutral-light mb-2">IOC Type</div>
                  <div className="font-bold">{isIp ? 'IP/Network' : 'File'}</div>
                </Col>
                {isIp ? null : (
                  <Col span={8}>
                    <div className="text-neutral-light mb-2">Popular Threat Label</div>
                    <div className="font-bold">
                      {(data.popular_threat_classification || {}).suggested_threat_label}
                    </div>
                  </Col>
                )}
              </Row>
              <Row>
                <Col span={isIp ? 8 : 16}>
                  <div className="text-neutral-light mb-2">IOC</div>
                  <div className="font-bold">
                    {isIp ? (
                      data.network
                    ) : (
                      <VirusTotalLink value={data.md5 || data.sha1 || data.sha256 || data.vhash}>
                        {data.md5 || data.sha1 || data.sha256 || data.vhash}
                      </VirusTotalLink>
                    )}
                  </div>
                </Col>
                {isIp ? (
                  <Col span={8}>
                    <div className="text-neutral-light mb-2">Tags</div>
                    <div className="font-bold">
                      {(data.tags || []).length
                        ? data.tags.map((tag) => (
                            <Tag key={tag} color="error">
                              {tag}
                            </Tag>
                          ))
                        : '---'}
                    </div>
                  </Col>
                ) : (
                  <Col span={8}>
                    <div className="text-neutral-light mb-2">Threat Categories</div>
                    <div className="font-bold">
                      {(data.popular_threat_category || []).length
                        ? data.popular_threat_category.map((i) => (
                            <Tag key={i.value} color="error">
                              {i.value}
                            </Tag>
                          ))
                        : '---'}
                    </div>
                  </Col>
                )}
              </Row>
            </Col>
          </Row>
        </div>
        {/* {(data.type_tags || []).length ? (
          <>
            <div className="my-4">
              <div className="flex items-center flex-wrap">
                <span className="mr-2">Type:</span>{' '}
                {data.type_tags.map((tag) => (
                  <Tag key={tag} color="processing">
                    {tag}
                  </Tag>
                ))}
              </div>
            </div>
          </>
        ) : null} */}
        <div className="my-4" />
        {/* dump data */}
        <div className="bg-seperator px-4 py-2 rounded">
          <Row gutter={32}>
            {keys[isIp ? 'ip' : 'process'].map((item, key) => (
              <Col
                span={12}
                key={key}
                className={`${
                  (key + 1) % 2 !== 0
                    ? 'border-solid border-r border-border border-t-0 border-b-0 border-l-0'
                    : ''
                }`}>
                <div key={item.key} className={`flex items-center py-2 flex-1`}>
                  <div className="flex-1 flex-shrink-0">{item.label}</div>
                  <div className="flex-1 flex-shrink-0">
                    {item.render ? item.render(data[item.key]) : data[item.key]}
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        </div>
        <div className="my-4" />
        {/* here goes list */}
        <div className="px-4">
          <Row className="my-4" gutter={32}>
            {Object.keys(data.last_analysis_results || {}).map((key, index) => (
              <Col
                key={key}
                span={12}
                className={`
                  pb-2
                  ${
                    index % 2 === 0
                      ? 'border-solid border-r border-b-0 border-t-0 border-l-0 border-border'
                      : ''
                  }`}>
                <Row key={key} className={`pb-2 flex items-center`}>
                  <Col span={12}>{key}</Col>
                  <Col
                    span={12}
                    className={`flex items-center ${
                      data.last_analysis_results[key].category === 'malicious'
                        ? 'text-danger'
                        : data.last_analysis_results[key].category === 'undetected'
                        ? 'text-neutral-light'
                        : 'text-success'
                    }`}>
                    {data.last_analysis_results[key].category === 'malicious' ? (
                      <div
                        className="text-danger text-lg w-8 h-8 items-center justify-center inline-block"
                        title="Malicious">
                        <CrossIcon className="w-full h-full" />
                      </div>
                    ) : data.last_analysis_results[key].category === 'undetected' ? (
                      <div
                        className="text-neutral-light text-lg w-8 h-8 inline-block items-center justify-center ml-1"
                        title="Undetected">
                        <WarningOutlined className="w-full h-full text-2xl" />
                      </div>
                    ) : (
                      <div
                        className="text-danger text-lg w-8 h-8 inline-block items-center justify-center"
                        title={data.last_analysis_results[key].category}>
                        <CheckIcon className="w-full h-full" />
                      </div>
                    )}
                    <span className="ml-2">{data.last_analysis_results[key].result}</span>
                  </Col>
                  {/* <Divider className="my-1" /> */}
                </Row>
              </Col>
            ))}
          </Row>
        </div>
      </Col>
    </Row>
  );
}
