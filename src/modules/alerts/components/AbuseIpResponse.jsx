import { Table, Tag } from 'antd';
import ReactCountryFlag from 'react-country-flag';

export default function AbuseIpResponse({ data }) {
  let keyRenames = {
    ipAddress: 'IP Address',
    isPublic: 'Is Public?',
    ipVersion: 'IP Version',
    isWhitelisted: 'Is Whitelisted?',
    abuseConfidenceScore: 'Abuse Confidence Score',
    countryCode: 'Country',
    usageType: 'Usage Type',
    isp: 'ISP',
    domain: 'Domain',
    hostnames: 'Hosts',
    isTor: 'Is TOR?',
    totalReports: 'Total Reports',
    numDistinctUsers: 'No. of Distinct Users',
    lastReportedAt: 'Last Reported At'
  };
  let renderFns = {
    domain(value) {
      return (
        <a href={value} target="_blank" rel="noreferrer">
          {value}
        </a>
      );
    },
    hostnames(value) {
      return (
        <>
          {value.map((i) => (
            <Tag color="success" key={i}>
              {i}
            </Tag>
          ))}
        </>
      );
    },
    abuseConfidenceScore(value) {
      return <Tag color="error">{value}</Tag>;
    },
    totalReports(value) {
      return <Tag color="processing">{value}</Tag>;
    },
    numDistinctUsers(value) {
      return <Tag color="processing">{value}</Tag>;
    },
    countryCode(value) {
      return (
        <>
          <ReactCountryFlag
            title={value}
            countryCode={value}
            svg
            style={{
              width: '2em',
              height: '2em'
            }}
          />
          <span className="ml-2">{value}</span>
        </>
      );
    }
  };
  let dataSource = Object.keys(data).map((key) => ({
    key,
    value: data[key]
  }));
  return (
    <Table
      bordered
      showHeader={false}
      dataSource={dataSource}
      pagination={false}
      size="large"
      columns={[
        {
          key: 'key',
          title: '',
          width: '200px',
          dataIndex: 'key',
          render(text) {
            return keyRenames[text] || text;
          }
        },
        {
          key: 'value',
          title: '',
          dataIndex: 'value',
          render(text, record) {
            if (renderFns[record.key]) {
              return renderFns[record.key](text);
            }
            return text;
          }
        }
      ]}
    />
  );
}
