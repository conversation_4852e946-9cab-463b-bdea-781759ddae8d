import { useEffect, useState } from 'react';
import { Menu, Badge } from 'antd';
import { getSeverityWiseCountApi } from '../api/alert-list';

export default function SeverityFilters({ onSelect, timeline, onCountReceived, selectedSeverity }) {
  const [severities, setSeverities] = useState([]);

  useEffect(() => {
    getSeverityWiseCountApi(undefined, { timeline }).then((data) => {
      onCountReceived(data);
      setSeverities(data);
    });
  }, [onCountReceived, timeline]);

  function handleSeveritySelected(data) {
    onSelect(data.key);
  }

  return (
    <Menu
      mode="inline"
      selectedKeys={selectedSeverity ? [selectedSeverity] : undefined}
      defaultSelectedKeys={[selectedSeverity || 'All']}
      className="settings-menu"
      style={{ borderInlineEnd: 'none' }}
      items={severities.map((item) => ({
        key: item.severity,
        label: (
          <div className="flex items-center justify-between">
            {item.severity} <Badge className="ml-2 count-badge" count={item.count} />
          </div>
        )
      }))}
      onSelect={handleSeveritySelected}
    />
  );
}
