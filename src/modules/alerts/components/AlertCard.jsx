import { Col, Tag } from 'antd';
import { Link } from 'react-router-dom';
import Severity from '@/src/components/Severity';
import { useAuth } from '@/src/hooks/auth';
import { ReactComponent as Logo } from '@/src/assets/logo.svg';

export function AlertCard({ alert, span = 6, showAssetLink }) {
  const { formatDateTime } = useAuth();

  return (
    <Col span={span} className="mb-4">
      <div className="bg-seperator rounded-lg shadow flex flex-col px-4 py-2 relative">
        <div className="flex items-center justify-between">
          <Severity severity={alert.severity} useTag />
          <Tag color="lime" className="uppercase">
            {alert.module}
          </Tag>
        </div>
        <div className="text-neutral-light mb-4 mt-2">
          <small>{formatDateTime(+alert.createdAt)}</small>
        </div>
        <div className="mt-2">
          <div className="flex min-w-0 text-ellipsis text-lg font-bold">
            <div className="text-ellipsis">{alert.alert}</div>
          </div>
          {showAssetLink ? (
            <div className="flex min-w-0 text-ellipsis text-sm font-semibold">
              <Link to={`/inventory/endpoints/${alert.assetId}`}>
                <span className="text-neutral-light">{alert.asset}</span>
              </Link>
            </div>
          ) : null}
          <small style={{ lineClamp: 2, whiteSpace: 'pre-line', wordBreak: 'break-all' }}>
            {alert.message}
          </small>
        </div>
        <div className="w-1/2 absolute right-0 top-0 bottom-0 py-2">
          <Logo className="w-full h-full" />
        </div>
      </div>
    </Col>
  );
}
