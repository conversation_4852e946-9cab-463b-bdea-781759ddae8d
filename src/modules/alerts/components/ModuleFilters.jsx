import { useEffect, useState } from 'react';
import { Menu, Badge } from 'antd';
import { getModuleWiseCountApi } from '../api/alert-list';

export default function ModuleFilters({ onSelect, timeline, onCountReceived }) {
  const [severities, setSeverities] = useState([]);

  useEffect(() => {
    getModuleWiseCountApi(undefined, { timeline }).then((data) => {
      onCountReceived(data);
      setSeverities(data);
    });
  }, [onCountReceived, timeline]);

  function handleSeveritySelected(data) {
    onSelect(data.key);
  }

  return (
    <Menu
      mode="inline"
      defaultSelectedKeys={['All']}
      className="settings-menu"
      style={{ borderInlineEnd: 'none' }}
      items={severities.map((item) => ({
        key: item.moduleName,
        label: (
          <div className="flex items-center justify-between">
            {item.moduleName} <Badge className="ml-2 count-badge" count={item.count} />
          </div>
        )
      }))}
      onSelect={handleSeveritySelected}
    />
  );
}
