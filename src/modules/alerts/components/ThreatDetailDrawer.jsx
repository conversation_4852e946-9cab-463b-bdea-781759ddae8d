import { useEffect, useState } from 'react';
import { Drawer, Spin, Row, Col, Button } from 'antd';
import { getAlertThreatContextApi, refreshAlertThreatContextApi } from '../api/alert-list';
import AbuseIpResponse from './AbuseIpResponse';
import VTResponse from './VTResponse';
import Icon from '@/src/components/Icon';

export default function ThreatDetailDrawer({ alert, onClose }) {
  const [loading, setLoading] = useState(true);
  const [width, setWidth] = useState('40%');
  const [processing, setProcessing] = useState(false);
  const [threatContextResponse, setThreatContextResponse] = useState(null);

  useEffect(() => {
    getAlertThreatContextApi(alert.threat_context).then((data) => {
      setThreatContextResponse(data);
      if (data.type !== 'AbuseIPDB') {
        setWidth('70%');
      }
      setLoading(false);
    });
  }, [alert]);

  function refreshData() {
    setProcessing(true);
    refreshAlertThreatContextApi(alert.threat_context)
      .then((data) => {
        setThreatContextResponse(data);
        setProcessing(false);
      })
      .catch(() => {
        setProcessing(false);
      });
  }

  return (
    <Drawer
      title={
        <div className="flex justify-between items-center">
          <div className="flex-1 min-w-0 text-ellipsis">{alert.alert}</div>
          <Button type="primary" loading={processing} className="mr-2" ghost onClick={refreshData}>
            <Icon name="refresh" />
            Refresh
          </Button>
        </div>
      }
      placement={'right'}
      width={width}
      onClose={onClose}
      destroyOnClose
      maskClosable={false}
      open={true}>
      {loading ? (
        <div className="flex-1 flex items-center justify-center h-full">
          <Spin spinning />
        </div>
      ) : (
        <Row>
          <Col span={24}>
            {threatContextResponse.type === 'AbuseIPDB' ? (
              <AbuseIpResponse data={threatContextResponse.context.data} />
            ) : (
              <VTResponse data={threatContextResponse.context} />
            )}
          </Col>
        </Row>
      )}
    </Drawer>
  );
}
