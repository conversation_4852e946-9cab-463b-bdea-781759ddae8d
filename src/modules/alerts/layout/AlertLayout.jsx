import { useState, createContext, useContext, useEffect } from 'react';
import { Divider } from 'antd';
import { Outlet, useSearchParams } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import SeverityFilters from '../components/SeverityFilters';
// import PermissionChecker from '@/src/components/PermissionChecker';
// import constants from '@/src/constants/index';
import SplitPane from '@/src/components/SplitPane';
import ModuleFilters from '../components/ModuleFilters';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';

const AlertLayoutContext = createContext();

export default function VulnerabilityLayout() {
  const [showMenu, setShowMenu] = useState(true);
  const [severityCounts, setSeverityCounts] = useState([]);
  const [moduleCounts, setModuleCounts] = useState([]);
  const [isMenuVisible, setIsMenuVisible] = useState(true);
  const [searchParams] = useSearchParams();
  const [selectedSeverity, setSelectedSeverity] = useState(null);
  const [selectedModule, setSelectedModule] = useState(null);
  const [timeline, setTimeline] = useState({});

  useEffect(() => {
    const availableSeverities = severityCounts.map((i) => i.severity);
    if (availableSeverities.includes(searchParams.get('severity'))) {
      setSelectedSeverity(searchParams.get('severity'));
    } else {
      setSelectedSeverity('All');
    }
  }, [searchParams, severityCounts]);

  return (
    <AlertLayoutContext.Provider
      value={{
        hideMenu: () => setShowMenu(false),
        showMenu: () => setShowMenu(true),
        selectedSeverity,
        selectedModule,
        severityCounts,
        moduleCounts,
        setTimeline,
        timeline
      }}>
      <div className="h-full flex flex-col">
        <PageHeading icon="vulnerability" title="Alerts" />
        <div className="flex-1 min-h-0 flex flex-col">
          <PermissionChecker permission={constants.View_Alerts} redirect>
            <SplitPane
              hasMenu={showMenu}
              isMenuVisible={isMenuVisible}
              onVisibleChange={(i) => setIsMenuVisible(i)}
              leftPane={
                <>
                  <h4 className="font-semibold text-lg">Modules</h4>
                  <ModuleFilters
                    timeline={timeline}
                    onSelect={(moduleName) => setSelectedModule(moduleName)}
                    onCountReceived={setModuleCounts}
                  />
                  <Divider />
                  <h4 className="font-semibold text-lg">Severity</h4>
                  <SeverityFilters
                    timeline={timeline}
                    selectedSeverity={selectedSeverity}
                    onSelect={(severity) => setSelectedSeverity(severity)}
                    onCountReceived={setSeverityCounts}
                  />
                </>
              }
              rightPane={<Outlet />}
            />
          </PermissionChecker>
        </div>
      </div>
    </AlertLayoutContext.Provider>
  );
}

export function useAlertLayout() {
  return useContext(AlertLayoutContext);
}
