import { <PERSON><PERSON>Provider } from '@/src/hooks/crud';
import { Link, useNavigate } from 'react-router-dom';
import { Fragment, useState } from 'react';
import { BarsOutlined, AppstoreOutlined } from '@ant-design/icons';
import { Segmented, Row, Button } from 'antd';
import { getAllAlertsApi } from '../api/alert-list';
import { useAlertLayout } from '../layout/AlertLayout';
import Severity from '@/src/components/Severity';
import { AlertCard } from '../components/AlertCard';
import ThreatDetailDrawer from '../components/ThreatDetailDrawer';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';
import ThreatContext from '@/src/components/ThreatContext';

export default function List() {
  const context = useAlertLayout();
  const [viewType, setViewType] = useState('list');
  const [showThreatContextFor, setThreatContextFor] = useState(null);
  const navigate = useNavigate();

  const columns = [
    {
      title: 'Alert',
      key: 'alert',
      dataIndex: 'alert'
    },
    {
      title: 'Endpoint',
      key: 'asset',
      dataIndex: 'asset',
      render({ record }) {
        return <Link to={`/inventory/endpoints/${record.assetId}`}>{record.asset}</Link>;
      }
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      render({ record }) {
        return <Severity severity={record.severity} useTag />;
      }
    },
    {
      title: 'Module',
      dataIndex: 'module',
      key: 'module'
    },
    {
      title: 'Attribute',
      dataIndex: 'attribute',
      key: 'attribute',
      ellipsis: true
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value'
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
      sortable: false
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    }
  ];

  function navigateToAlert() {
    navigate(`/settings/policy-management/policies?zOperation=create`);
  }

  return (
    <Fragment>
      <div className="flex-1 min-h-0 flex flex-col">
        <CrudProvider
          columns={columns}
          defaultPageSize={30}
          key={`${context.selectedSeverity}-${context.selectedModule}-${JSON.stringify(
            context.timeline
          )}`}
          resourceTitle="Alerts"
          drawerPlacement="bottom"
          hasSearch
          prependColumns={[
            {
              title: ' ',
              resizable: false,
              width: 20,
              key: 'threat_context',
              dataIndex: 'threat_context',
              order: -1,
              sortable: false,
              selectable: false,
              render({ record }) {
                return <ThreatContext record={record} onClick={setThreatContextFor} />;
              }
            }
          ]}
          beforeCreateSlot={() => (
            <div className="mr-2">
              <TimelinePicker value={context.timeline} onChange={context.setTimeline} />
            </div>
          )}
          createSlot={(_, { fetchData }) => (
            <div className="inline-flex items-center">
              <Button className="mr-2" type="primary" ghost onClick={() => navigateToAlert()}>
                Configure Alert
              </Button>
              <Segmented
                value={viewType}
                onChange={(e) => setViewType(e)}
                options={[
                  { value: 'list', icon: <BarsOutlined /> },
                  { value: 'card', icon: <AppstoreOutlined /> }
                ]}
              />
            </div>
          )}
          fetchFn={(...args) =>
            getAllAlertsApi(...args, {
              severity: context.selectedSeverity,
              moduleName: context.selectedModule,
              timeline: context.timeline
            })
          }
          tableSlot={
            viewType === 'card'
              ? ({ data }) => (
                  <div className="px-2">
                    <Row gutter={16}>
                      {data.map((item) => (
                        <AlertCard key={item.id} alert={item} showAssetLink />
                      ))}
                    </Row>
                  </div>
                )
              : null
          }
        />
      </div>
      {showThreatContextFor ? (
        <ThreatDetailDrawer
          alert={showThreatContextFor}
          onClose={() => setThreatContextFor(null)}
        />
      ) : null}
    </Fragment>
  );
}
