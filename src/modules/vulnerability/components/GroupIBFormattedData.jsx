import Score from '@/src/components/Score';
import Severity from '@/src/components/Severity';
import generateId from '@/src/utils/id';
import { Row, Col, Table } from 'antd';
import { useEffect, useState } from 'react';

const columns = {
  cpe: [
    {
      key: 'cpe',
      title: 'CPE',
      dataIndex: 'cpe'
    }
  ],
  reference: [
    {
      key: 'reference',
      title: 'Reference',
      dataIndex: 'reference',
      render(text) {
        return (
          <a href={text} target="_blank" rel="noreferrer">
            {text}
          </a>
        );
      }
    }
  ],
  affected_software: [
    {
      key: 'name',
      title: 'Product',
      dataIndex: 'name',
      sortable: false
    },
    {
      key: 'version',
      title: 'Version',
      dataIndex: 'version',
      sortable: false
    }
  ],
  epss: [
    {
      key: 'cve',
      title: 'CVE',
      dataIndex: 'cve',
      sortable: false
    },
    {
      key: 'epss',
      title: 'EPSS',
      dataIndex: 'epss',
      sortable: false,
      render(_, record) {
        return (
          <Score
            size={50}
            category="epss"
            value={record.epss ? parseFloat(record.epss.toFixed(2)) : record.epss}
            useCircle
          />
        );
      }
    },
    {
      key: 'percentile',
      title: 'Percentile',
      dataIndex: 'percentile',
      sortable: false
    }
  ],
  exploits: [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      sortable: false
    },
    {
      title: 'Bulletin Family',
      dataIndex: 'bulletinFamily',
      key: 'bulletinFamily',
      sortable: false
    },
    {
      title: 'Link',
      dataIndex: 'href',
      key: 'href',
      ellipsis: true,
      sortable: false,
      render(text) {
        return (
          <a href={text} target="_blank" rel="noreferrer">
            {text}
          </a>
        );
      }
    },
    {
      title: 'Reporter',
      dataIndex: 'reporter',
      key: 'reporter',
      sortable: false
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      sortable: false
    },
    {
      title: 'Source',
      dataIndex: 'sourceData',
      key: 'sourceData',
      ellipsis: true,
      sortable: false
    }
  ]
};

function DefaultTable({ data, columnKey, ...props }) {
  let [currentData, setCurrentData] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showTotal: (total, range) => `showing ${range[0]}-${range[1]} of ${total} items`,
    total: (data || []).length
  });
  const dataChange = (updatedPagination, filters, sorter) => {
    setPagination({ ...pagination, ...updatedPagination });
  };
  useEffect(() => {
    let offset = (pagination.current - 1) * pagination.pageSize;
    setCurrentData((data || []).slice(offset, offset + pagination.pageSize));
    // eslint-disable-next-line
  }, [JSON.stringify(pagination)]);
  return (
    <Table
      dataSource={currentData}
      columns={columns[columnKey] || []}
      rowKey={(i) => i.id}
      onChange={dataChange}
      pagination={pagination}
      {...props}
    />
  );
}

export default function GroupIBFormattedData({ data }) {
  return (
    <Row gutter={16}>
      <Col span={10}>
        <div className="flex flex-col bg-seperator rounded-lg p-4">
          <table className="info-table table-fixed">
            <tr>
              <td className="text-neutral-light py-1">Vulnerability ID</td>
              <td className="py-2">
                <a href={data.href} target="_blank" rel="noreferrer">
                  {data.id}
                </a>
              </td>
            </tr>
            <tr>
              <td className="text-neutral-light py-1">Published</td>
              <td className="py-2">{data.published}</td>
            </tr>
            <tr>
              <td className="text-neutral-light py-1">Modified</td>
              <td className="py-2">{data.modified}</td>
            </tr>
            <tr>
              <td className="text-neutral-light py-1">Reporter</td>
              <td className="py-2">{data.reporter}</td>
            </tr>
            <tr>
              <td className="text-neutral-light py-1">Provider</td>
              <td className="py-2">{data.provider}</td>
            </tr>
            <tr>
              <td className="text-neutral-light py-1">Type</td>
              <td className="py-2 uppercase">{data.type}</td>
            </tr>
            <tr>
              <td className="text-neutral-light py-1">CVSS Attack Vector</td>
              <td className="py-2 uppercase break-words">
                <Severity severity={data.cvssAttackVector} />
              </td>
            </tr>
            <tr>
              <td className="text-neutral-light py-1">CVSS v3 Vector</td>
              <td className="py-2 uppercase break-words">{data.cvss?.vector}</td>
            </tr>
          </table>
        </div>
        <div className="flex flex-col bg-seperator rounded-lg p-4 my-4">
          <div className="flex flex-col">
            <div className="flex items-center justify-between">
              <h4>CVSS Base Score</h4>
              <h2>{data.mergedCvss}</h2>
            </div>
            <Score size="small" value={data.mergedCvss} useLineProgress allowZero />
          </div>
          <div className="flex flex-col">
            <div className="flex items-center justify-between">
              <h4>Impact Subscore</h4>
              <h2>{data.extCvss?.impact || 0}</h2>
            </div>
            <Score size="small" value={data.extCvss?.impact || 0} useLineProgress allowZero />
          </div>
          <div className="flex flex-col">
            <div className="flex items-center justify-between">
              <h4>Exploitability Subscore</h4>
              <h2>{data.extCvss?.exploitability || 0}</h2>
            </div>
            <Score
              size="small"
              value={data.extCvss?.exploitability || 0}
              useLineProgress
              allowZero
            />
          </div>
        </div>
        <div className="flex flex-col bg-seperator rounded-lg p-4 my-4">
          <table className="w-full table-fixed">
            <tr>
              <td className="text-neutral-light py-1">TLP</td>
              <td className="py-2 text-right">
                <div
                  className="w-3 h-3 rounded-full inline-block"
                  style={{ backgroundColor: data.evaluation?.tlp }}></div>
              </td>
            </tr>
            <tr>
              <td className="text-neutral-light py-1">Admiralty Code</td>
              <td className="py-2 text-right">{data.evaluation?.admiraltyCode}</td>
            </tr>
            <tr>
              <td className="text-neutral-light py-1">Severity</td>
              <td className="py-2 text-right">
                <div className="inline-flex justify-end -mr-[16px]">
                  <Severity severity={data.severity} />
                </div>
              </td>
            </tr>
            <tr>
              <td colSpan={2}>
                <div className="flex flex-col">
                  <div className="flex items-center justify-between">
                    <h4>Reliability</h4>
                    <h2>{data.evaluation?.reliability}</h2>
                  </div>
                  <Score
                    size="small"
                    value={data.evaluation?.reliability}
                    type="risk"
                    useLineProgress
                    allowZero
                  />
                </div>
              </td>
            </tr>
            <tr>
              <td colSpan={2}>
                <div className="flex flex-col">
                  <div className="flex items-center justify-between">
                    <h4>Credibility</h4>
                    <h2>{data.evaluation?.credibility}</h2>
                  </div>
                  <Score
                    size="small"
                    value={data.evaluation?.credibility}
                    type="risk"
                    useLineProgress
                    allowZero
                  />
                </div>
              </td>
            </tr>
          </table>
        </div>
        <div className="flex flex-col bg-seperator rounded-lg p-4 my-4">
          <h2>Intelligence</h2>
          <table className="info-table table-fixed">
            <tr>
              <td className="text-neutral-light py-1">Exploits</td>
              <td className="py-2 text-right">{data.exploitCount || 0}</td>
            </tr>
            <tr>
              <td className="text-neutral-light py-1">Seen in the wild</td>
              <td className="py-2 text-right">{data.seenInTheWild ? 'Yes' : 'No'}</td>
            </tr>
          </table>
        </div>
        <div className="flex flex-col bg-seperator rounded-lg p-4 my-4">
          <h2>CPE</h2>
          <DefaultTable
            data={(data.cpe || []).map((i) => ({ cpe: i, id: generateId() }))}
            columnKey={'cpe'}
          />
        </div>
      </Col>
      <Col span={14}>
        <div className="mb-4">
          <h2 className="text-neutral-light">Description</h2>
          <div className="text-sm">{data.description}</div>
        </div>
        <div className="my-4">
          <h2 className="text-neutral-light">Affected Softwares</h2>
          <div className="text-sm">
            <DefaultTable
              data={(data.affectedSoftware || []).map((i) => ({ ...i, id: generateId() }))}
              rowKey={(i) => i.id}
              columnKey={'affected_software'}
            />
          </div>
        </div>
        <div className="my-4">
          <h2 className="text-neutral-light">EPSS</h2>
          <DefaultTable columnKey={'epss'} rowKey={(i) => i.cve} data={data.cveListEpss} />
        </div>
        <div className="my-4">
          <h2 className="text-neutral-light">Exploits</h2>
          <DefaultTable columnKey={'exploits'} data={data.exploitList} />
        </div>
        <div className="my-4">
          <h2 className="text-neutral-light">References</h2>
          <DefaultTable
            data={(data.references || []).map((i) => ({ reference: i, id: generateId() }))}
            columnKey={'reference'}
          />
        </div>
        <div className="my-4">
          <h2 className="text-neutral-light">Github</h2>
          <DefaultTable
            data={(data.githubLinkList || []).map((i) => ({ reference: i, id: generateId() }))}
            columnKey={'reference'}
          />
        </div>
      </Col>
    </Row>
  );
}
