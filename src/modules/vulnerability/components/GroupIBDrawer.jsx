import Loading from '@/src/components/Loading';
import React<PERSON><PERSON> from 'react-json-view';
import { Drawer, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { getGroupIBResponseByCveApi } from '../api/vulnerability-list';
import GroupIBFormattedData from './GroupIBFormattedData';

export default function GroupIBDrawer({ vulnerability, onClose }) {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState(null);
  const [activeTab, setActiveTab] = useState('formatted');
  useEffect(() => {
    getGroupIBResponseByCveApi(vulnerability.cve).then((data) => {
      setData(data);
      setLoading(false);
    });
  }, [vulnerability.cve]);
  console.log(data);
  const tabs = [
    {
      label: 'Formatted',
      key: 'formatted'
    },
    {
      label: 'JSON',
      key: 'json'
    }
  ];

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <span className="ml-2 font-semibold">{vulnerability.cve}</span>
          <img src="/groupib.png" alt="Group IB" />
        </div>
      }
      placement="right"
      width="70%"
      onClose={onClose}
      destroyOnClose
      open={true}>
      {loading ? (
        <Loading />
      ) : (
        <div className="flex flex-col flex-1 min-h-0 h-full">
          <Tabs
            items={tabs}
            activeKey={activeTab}
            onChange={(key) => {
              setActiveTab(key);
            }}></Tabs>
          <div className="flex flex-1 min-h-0 flex-col overflow-y-auto overflow-x-hidden">
            {activeTab === 'json' ? (
              <ReactJson
                src={data}
                theme="monokai"
                displayObjectSize={true}
                enableClipboard={true}
                indentWidth={4}
              />
            ) : (
              <GroupIBFormattedData data={{ ...data, severity: vulnerability.severity }} />
            )}
          </div>
        </div>
      )}
    </Drawer>
  );
}
