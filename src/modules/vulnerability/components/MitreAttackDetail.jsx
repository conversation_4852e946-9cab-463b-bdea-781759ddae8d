import { Row, Col, Divider } from 'antd';
import CytoscapeGraph from '@/src/components/CytoscapeGraph';
import generateId from '@/src/utils/id';
import { useState } from 'react';
import { useLayout } from '@/src/layouts/Layout';

function convertMitreDetailToTree(cve, data) {
  let nodes = {
    [cve.cve]: {
      data: {
        id: cve.cve,
        label: `${cve.cve}`,
        disabled: true
      }
    }
  };
  let edges = [];
  let activeNode = null;
  Object.keys(data).forEach((tactic) => {
    let tacticId = nodes[tactic] ? nodes[tactic].data.id : generateId();
    nodes[tactic] = {
      data: {
        id: tacticId,
        label: tactic,
        disabled: true
      }
    };
    edges.push({
      data: {
        source: cve.cve,
        target: tacticId
      }
    });
    Object.keys(data[tactic]).forEach((technique) => {
      let techniques = data[tactic][technique] || [];
      techniques.forEach((item) => {
        let techniqueNodeId = nodes[technique] ? nodes[technique].data.id : generateId();
        let technique_parts = item.technique_id.split('.');
        nodes[technique] = {
          data: {
            id: techniqueNodeId,
            label: technique,
            disabled: technique_parts.length > 1,
            tactic,
            technique,
            ...(technique_parts.length > 1 ? {} : item || {})
          }
        };
        if (technique_parts.length <= 1) {
          activeNode = { ...nodes[technique].data };
        }
        edges.push({
          data: {
            source: tacticId,
            target: techniqueNodeId
          }
        });
        if (technique_parts.length > 1) {
          let subTechniqueNodeId = nodes[item.name] ? nodes[item.name].data.id : generateId();
          nodes[item.name] = {
            data: {
              id: subTechniqueNodeId,
              label: item.name,
              tactic,
              technique,
              subTechnique: item.name,
              disabled: false,
              ...(item || {})
            }
          };
          if (!activeNode) {
            activeNode = { ...nodes[item.name].data };
          }
          edges.push({
            data: {
              source: techniqueNodeId,
              target: subTechniqueNodeId
            }
          });
        }
      });
    });
  });
  return { nodes: Array.from(Object.values(nodes)), edges, activeNode };
}

export default function MitreAttackDetail({ cve, data }) {
  const [treeData, setTreeData] = useState({ nodes: [], edges: [] });
  const [activeNode, setActiveNode] = useState(null);
  let { theme } = useLayout();

  useState(() => {
    let { edges, nodes, activeNode } = convertMitreDetailToTree(cve, data);
    setTreeData({ edges, nodes });
    setActiveNode(activeNode);
  }, [data]);

  function handleNodeClick(e) {
    let data = e.target.data();
    if (!data.disabled) {
      // if (activeNode && activeNode.id === data.id) {
      //   setActiveNode(null);
      // } else {
      setActiveNode(data);
      // }
    }
  }

  return (
    <Row style={{ height: '350px' }}>
      <Col
        span={activeNode ? 14 : 24}
        className="border-l-0 border-solid border-border border-t-0 border-r-1 border-b-0 h-full">
        <CytoscapeGraph
          graphStyle={[
            {
              selector: 'edge',
              style: {
                width: 1.2,
                'line-style': 'solid',
                'line-color': theme === 'light' ? '#364658' : 'rgba(53, 132, 220, 0.40)',
                'target-arrow-color': theme === 'light' ? '#364658' : 'rgba(53, 132, 220, 0.40)'
              }
            }
          ]}
          hierarchy={treeData}
          activeNode={activeNode}
          onNodeClick={handleNodeClick}
          tooltipFn={(data) => {
            let parts = data.label.split(':');
            return `<div class="text-center" title="${data.label}">
                <p class="text-xs mb-0 text-white text-ellipsis whitespace-pre">
                ${parts[0]}
                </p>
                <p class="mb-0">
                ${parts[1] || ''}</p></div>`;
          }}
          dataLabelFn={(data) => {
            let parts = data.label.split(':');
            return `<div class="text-center max-w-[95px]" title="${data.label}">
                <p class="text-xs mb-0 text-white text-ellipsis whitespace-pre">
                ${parts[0]}
                </p>
                <p  class="mb-0 text-white text-ellipsis">
                ${parts[1] || ''}</p></div>`;
          }}
        />
      </Col>
      {activeNode ? (
        <Col span={10} className="flex flex-col min-h-0 overflow-auto h-full">
          <div className="px-2">
            <div className="group-item mb-4">
              <div className="font-bold">Tactic</div>
              <div>{activeNode.tactic}</div>
            </div>
            <div className="group-item mb-4">
              <div className="font-bold">Technique</div>
              <div>{activeNode.technique}</div>
            </div>
            <div className="group-item mb-4">
              <div className="font-bold">Sub-Technique</div>
              <div>{activeNode.subTechnique}</div>
            </div>
            <div className="group-item mb-4">
              <div className="font-bold">Description</div>
              <div>{activeNode.description}</div>
            </div>
            <Divider className="font-semibold">Mitigations</Divider>
            {(activeNode.mitigations || []).map((item) => (
              <div key={item.name} className="mb-4">
                <div className="font-bold">{item.name}</div>
                <div>{item.description}</div>
              </div>
            ))}
          </div>
        </Col>
      ) : null}
    </Row>
  );
}
