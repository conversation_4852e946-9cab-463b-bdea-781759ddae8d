import Score from '@/src/components/Score';
import Chunk from 'lodash/chunk';
import { ExportOutlined } from '@ant-design/icons';
import Severity from '@/src/components/Severity';
import { Row, Col, Button, Segmented, Tag, Divider } from 'antd';
import { useEffect, useState } from 'react';
import VulnerabilityItem from './VulnerabilityItem';
import { getVulnerabilityByCVEApi } from '../api/vulnerability-list';
import Loading from '@/src/components/Loading';
import GroupIBDrawer from './GroupIBDrawer';
import MitreAttackDetail from './MitreAttackDetail';
// import { assetPatchCveRelation } from '../api/vulnerability-list';

const cvss3Items = [
  {
    label: 'Severity',
    icon: '/images/vulnerability/severity.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss3_base_severity} />;
    }
  },
  {
    label: 'Impact Score',
    icon: '/images/vulnerability/impact.png',
    children(vulnerability) {
      return <Score value={vulnerability.cvss3_impact_score} size={50} />;
    }
  },
  {
    label: 'Attack Vector (AV)',
    icon: '/images/vulnerability/attack_vector.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss3_attack_vector} />;
    }
  },
  {
    label: 'Attack Complexity (AC)',
    icon: '/images/vulnerability/attack_complexity.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss3_attack_complexity} />;
    }
  },
  {
    label: 'Privileges Required (PR)',
    icon: '/images/vulnerability/priviledged.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss3_privileges_required} />;
    }
  },
  {
    label: 'Scope',
    icon: '/images/vulnerability/remediation_level.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss3_scope} />;
    }
  },
  {
    label: 'Availability Impact',
    icon: '/images/vulnerability/Time_to_fix.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss3_availability_impact} />;
    }
  },
  {
    label: 'Exploitability Score',
    icon: '/images/vulnerability/Exploit_available.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss3_exploitability_score} />;
    }
  },
  {
    label: 'Confidentiality Impact',
    icon: '/images/vulnerability/asset_visi.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss3_confidentiality_impact} />;
    }
  },
  {
    label: 'Integrity Impact',
    icon: '/images/vulnerability/Exploibility_factor.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss3_integrity_impact} />;
    }
  }
];

const cvss4Items = [
  {
    label: 'Severity',
    icon: '/images/vulnerability/severity.png',
    children(vulnerability) {
      return <Severity severity={vulnerability['cvss40_base_severity']} />;
    }
  },
  {
    label: 'Base Score',
    icon: '/images/vulnerability/impact.png',
    children(vulnerability) {
      return <Score value={vulnerability['cvss40_base_score']} size={50} />;
    }
  },
  // {
  //   label: 'Vector String',
  //   icon: '/images/vulnerability/attack_vector.png',
  //   children(vulnerability) {
  //     return <Severity severity={vulnerability['cvss40_vector_string']} />;
  //   }
  // },
  {
    label: 'Type',
    icon: '/images/vulnerability/attack_complexity.png',
    children(vulnerability) {
      return <Severity severity={vulnerability['cvss40_type']} />;
    }
  },
  {
    label: 'Source',
    icon: '/images/vulnerability/attack_complexity.png',
    children(vulnerability) {
      return <Severity severity={vulnerability['cvss40_source']} />;
    }
  },
  {
    label: 'Threat Severity',
    icon: '/images/vulnerability/severity.png',
    children(vulnerability) {
      return <Severity severity={vulnerability['cvss40_threat_severity']} />;
    }
  },
  {
    label: 'Threat Score',
    icon: '/images/vulnerability/impact.png',
    children(vulnerability) {
      return <Score value={vulnerability['cvss40_threat_score']} size={50} />;
    }
  },
  {
    label: 'Environmental Severity',
    icon: '/images/vulnerability/severity.png',
    children(vulnerability) {
      return <Severity severity={vulnerability['cvss40_environmental_severity']} />;
    }
  },
  {
    label: 'Environmental Score',
    icon: '/images/vulnerability/impact.png',
    children(vulnerability) {
      return <Score value={vulnerability['cvss40_environmental_score']} size={50} />;
    }
  }
];

const cvss2Items = [
  {
    label: 'Severity',
    icon: '/images/vulnerability/severity.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss2_severity} />;
    }
  },
  {
    label: 'Impact Score',
    icon: '/images/vulnerability/impact.png',
    children(vulnerability) {
      return <Score category="cvss2" value={vulnerability.cvss2_impact_score} size="sm" />;
    }
  },
  {
    label: 'Attack Vector (AV)',
    icon: '/images/vulnerability/attack_vector.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss2_access_vector} />;
    }
  },
  {
    label: 'Attack Complexity (AC)',
    icon: '/images/vulnerability/attack_complexity.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss2_access_complexity} />;
    }
  },
  {
    label: 'Inetgrity Impact',
    icon: '/images/vulnerability/Exploibility_factor.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss2_integrity_impact} />;
    }
  },
  {
    label: 'Availability Impact',
    icon: '/images/vulnerability/Time_to_fix.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss2_availability_impact} />;
    }
  },
  {
    label: 'Exploitability Score',
    icon: '/images/vulnerability/Exploit_available.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss2_exploitability_score} />;
    }
  },
  {
    label: 'Authentication',
    icon: '/images/vulnerability/priviledged.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss2_authentication} />;
    }
  },
  {
    label: 'Confidentiality Impact',
    icon: '/images/vulnerability/asset_visi.png',
    children(vulnerability) {
      return <Severity severity={vulnerability.cvss2_confidentiality_impact} />;
    }
  }
];

export default function VulnerabilityDetail({ vulnerability, onReceived, category = 'software' }) {
  const [viewType, setViewType] = useState('cvss3');
  const [fullDetail, setFullDetail] = useState(null);
  const [showGroupIBDrawer, setShowGroupIBDrawer] = useState(false);
  const [viewAllReferences, setViewAllReferences] = useState(false);
  // const [assetPatchCveRelationContext, setassetPatchCveRelationContext] = useState({});

  useEffect(() => {
    getVulnerabilityByCVEApi(category, vulnerability.cve).then((res) => {
      setFullDetail(res);
      if (onReceived) {
        onReceived(res);
      }
    });
    // eslint-disable-next-line
  }, [vulnerability.cve]);

  return !fullDetail?.cve ? (
    <Loading />
  ) : (
    <Row gutter={32} className="text-base">
      <Col span={24}>
        <Row gutter={32}>
          <Col span={24}>
            <Row gutter={32}>
              <Col span={16} className="flex flex-col">
                <div className="p-2 rounded text-xs bg-seperator">{fullDetail.description}</div>
              </Col>
              <Col span={4}>
                <div className="flex justify-around">
                  <div className="flex flex-col">
                    <div className="font-semibold mb-4 text-center">EPSS</div>
                    <div>
                      <Score
                        useCircle
                        category="epss"
                        value={fullDetail.epss_probability}
                        size={50}
                      />
                    </div>
                  </div>
                  <div className="flex flex-col">
                    <div className="font-semibold mb-4 text-center">Exploitable</div>
                    <div className="flex flex-1 justify-center flex-col">
                      <div className="flex items-center justify-center">
                        <div
                          className={`flex items-center full-circle vulnerability-score-card rounded  text circle-score text-base px-2 py-1' justify-center vulnerability-${
                            fullDetail.cisa_known_exploit === 'yes'
                              ? 'critical critical'
                              : 'low low'
                          }`}>
                          {fullDetail.cisa_known_exploit === 'yes' ? 'Yes' : 'No'}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Col>
              <Col span={4} className="mb-4">
                <Row gutter={16}>
                  <Col span={12} className="mt-4">
                    {/* {fullDetail.assetCount ? (
                  <div className="flex flex-col bg-seperator rounded p-6">
                    <div className="font-semibold text-base flex items-center justify-center flex-col">
                      Affected Assets <br />
                      <span className="text-primary text-2xl">{fullDetail.assetCount}</span>
                    </div>
                  </div>
                ) : null} */}
                    <Button
                      type="primary"
                      size="large"
                      block
                      className="mr-4 flex-1 gradient-btn"
                      disabled={false}
                      onClick={() => {
                        window.open(`https://nvd.nist.gov/vuln/detail/${fullDetail.cve}`, '_blank');
                      }}>
                      NVD <ExportOutlined />
                    </Button>
                  </Col>
                  <Col span={12} className="mt-4">
                    <Button
                      type="primary"
                      className="flex-1 gradient-btn"
                      size="large"
                      block
                      disabled={false}
                      onClick={() => {
                        window.open(`https://www.cvedetails.com/cve/${fullDetail.cve}/`, '_blank');
                      }}>
                      CVE <ExportOutlined />
                    </Button>
                  </Col>
                  {/* <Col span={12} className="mt-4">
                    <div className="flex items-center">
                      <Button
                        type="primary"
                        className="flex-1 gradient-btn"
                        size="large"
                        disabled={false}
                        onClick={() => {
                          setShowGroupIBDrawer(true);
                        }}>
                        GroupIB
                      </Button>
                    </div>
                  </Col> */}
                </Row>
              </Col>
            </Row>

            <Row gutter={32}>
              <Col span={24} className="flex justify-between items-center">
                <Segmented
                  value={viewType}
                  onChange={(e) => setViewType(e)}
                  options={[
                    { value: 'cvss3', label: 'CVSS3' },
                    ...(fullDetail['cvss2_base_score'] ? [{ value: 'cvss2', label: 'CVSS2' }] : []),
                    ...(fullDetail['cvss40_base_score']
                      ? [{ value: 'cvss40', label: 'CVSS4' }]
                      : [])
                  ]}
                />
              </Col>
              {viewType === 'cvss40' && (
                <Col span={14} className="mt-4">
                  <div className="flex items-center">
                    <Score value={fullDetail['cvss40_base_score']} />
                    <div className="text-primary mb-0 ml-3 font-medium">
                      CVSSv40: <br />
                      <small>{fullDetail['cvss40_vector_string']}</small>
                    </div>
                  </div>
                  <Row gutter={32}>
                    {Chunk(cvss4Items, 4).map((items, index) => (
                      <Col span={12} key={index}>
                        {items.map((item) => (
                          <VulnerabilityItem
                            key={item.label}
                            label={item.label}
                            icon={<img alt={item.label} src={item.icon} height="20" width="20" />}>
                            {item.children(fullDetail)}
                          </VulnerabilityItem>
                        ))}
                      </Col>
                    ))}
                  </Row>
                </Col>
              )}
              {viewType === 'cvss3' && (
                <Col span={14} className="mt-4">
                  <div className="flex items-center">
                    <Score value={fullDetail.cvss3_base_score} />
                    <div className="text-primary mb-0 ml-3 font-medium">
                      CVSSv3: <br />
                      <small>{fullDetail.cvss3_vector_string}</small>
                    </div>
                  </div>
                  <Row gutter={32}>
                    {Chunk(cvss3Items, 5).map((items, index) => (
                      <Col span={12} key={index}>
                        {items.map((item) => (
                          <VulnerabilityItem
                            key={item.label}
                            label={item.label}
                            icon={<img alt={item.label} src={item.icon} height="20" width="20" />}>
                            {item.children(fullDetail)}
                          </VulnerabilityItem>
                        ))}
                      </Col>
                    ))}
                  </Row>
                </Col>
              )}
              {viewType === 'cvss2' && (
                <Col span={14} className="mt-4">
                  <div className="flex items-center">
                    <Score category="cvss2" value={fullDetail['cvss2_base_score']} />
                    <div className="text-primary mb-0 ml-3 font-medium">
                      CVSSv2: <br />
                      <small>{fullDetail.cvss2_vector}</small>
                    </div>
                  </div>
                  <Row gutter={32}>
                    {Chunk(cvss2Items, 5).map((items, index) => (
                      <Col span={12} key={index}>
                        {items.map((item) => (
                          <VulnerabilityItem
                            key={item.label}
                            label={item.label}
                            icon={<img alt={item.label} src={item.icon} height="20" width="20" />}>
                            {item.children(fullDetail)}
                          </VulnerabilityItem>
                        ))}
                      </Col>
                    ))}
                  </Row>
                </Col>
              )}
              {(fullDetail.references || []).length ? (
                <Col
                  span={5}
                  className="mt-4 flex flex-col  px-12 bordered-x"
                  style={{ padding: '0 50px' }}>
                  <h3 className="text-center my-0">References</h3>
                  <Divider className="my-2" />
                  {fullDetail.references

                    .slice(0, viewAllReferences ? fullDetail.references.length : 2)
                    .map((item) => (
                      <div key={item.ref_url} className="w-full ">
                        <div className="w-full flex flex-col">
                          <span> {item.source}:</span>
                          <span
                            className="w-full"
                            style={{
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            }}
                            title={item.ref_url}>
                            <a href={item.ref_url} target="_blank" rel="noreferrer" className=" ">
                              {item.ref_url}
                            </a>
                          </span>
                        </div>
                      </div>
                    ))}
                  {fullDetail.references.length > 2 ? (
                    <div>
                      <Button
                        type="link"
                        disabled={false}
                        onClick={() => setViewAllReferences((s) => !s)}>
                        {viewAllReferences ? 'View Less' : 'View More'}
                      </Button>
                    </div>
                  ) : null}
                </Col>
              ) : null}

              <Col span={5} className="mt-4 flex flex-col  px-12 " style={{ padding: '0 50px' }}>
                <h3 className="text-center my-0">Fix Recommendation</h3>
                <Divider className="my-2" />
                {fullDetail.kb_article && fullDetail.kb_article !== 'null' ? (
                  <Row
                    gutter={32}
                    style={{ maxHeight: '200px', minHeight: '200px', overflow: 'scroll' }}>
                    <a
                      href={fullDetail.kb_article_url}
                      target="_blank"
                      rel="noreferrer"
                      className=" ">
                      {fullDetail.kb_article}
                    </a>
                  </Row>
                ) : (
                  <>
                    {fullDetail.resolved_version ? (
                      <div className="w-full flex justify-center items-center">
                        <Tag>{fullDetail.resolved_version}</Tag>
                      </div>
                    ) : (
                      <h4 className="text-center">No Data Available!</h4>
                    )}
                  </>
                )}
                {showGroupIBDrawer ? (
                  <GroupIBDrawer
                    onClose={() => setShowGroupIBDrawer(false)}
                    vulnerability={vulnerability}
                  />
                ) : null}
              </Col>
            </Row>

            {fullDetail.threat_intel_details &&
            Object.keys(fullDetail.threat_intel_details).length > 0 ? (
              <>
                <Divider>Taxonomy Mappings With MITRE ATTACK</Divider>
                <MitreAttackDetail cve={fullDetail} data={fullDetail.threat_intel_details} />
              </>
            ) : null}
          </Col>
        </Row>
      </Col>
    </Row>
  );
}
