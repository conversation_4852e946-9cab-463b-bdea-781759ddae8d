import { useFilters } from './Filters';
import { Button } from 'antd';
export default function FilterContainer() {
  const { setIsDrawerOpen, appliedFiltersCount } = useFilters();

  return (
    <div className="ml-2">
      <Button type="link" size="large" onClick={() => setIsDrawerOpen(true)}>
        Advanced Filters {appliedFiltersCount > 0 ? `(${appliedFiltersCount})` : ''}
      </Button>
    </div>
  );
}
