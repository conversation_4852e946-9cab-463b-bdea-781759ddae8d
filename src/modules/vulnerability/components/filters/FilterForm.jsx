import { Form, Select, Button } from 'antd';
import { useFilters } from './Filters';
import { useMemo } from 'react';
import ValueSelection from './ValueSelection';

export default function FilterForm() {
  const { currentCondition, updateCurrentCondition, allAvailableOperands } = useFilters();
  const [form] = Form.useForm();
  const selectableOperands = useMemo(() => {
    return allAvailableOperands.map((operand) => ({ label: operand.label, value: operand.value }));
  }, [allAvailableOperands]);

  const numericOperators = [
    'Equal',
    'NotEqual',
    'GreaterThan',
    'GreaterThanEqual',
    'LessThan',
    'LessThanEqual'
  ];

  const stringOperators = ['Contains', 'NotContains', 'StartWith', 'EndWith'];

  const operatorMap = {
    severity: ['In', 'NotIn'],
    dropdown: ['In', 'NotIn'],
    string: stringOperators,
    numeric: numericOperators,
    boolean: ['Equal', 'NotEqual']
  };

  const selectableOperators = useMemo(() => {
    if (currentCondition?.column) {
      let operand = allAvailableOperands.find(
        (operand) => operand.value === currentCondition.column
      );
      if (operand) {
        return (operatorMap[operand.dataType] || []).map((i) => ({ label: i, value: i }));
      }
    }
    return [];
    // eslint-disable-next-line
  }, [currentCondition?.column, allAvailableOperands]);

  const selectedFullOperand = useMemo(() => {
    if (currentCondition?.column) {
      return (
        allAvailableOperands.find((operand) => operand.value === currentCondition.column) || {}
      );
    }
    return {};
    // eslint-disable-next-line
  }, [currentCondition?.column, allAvailableOperands]);

  function submitForm(data) {
    updateCurrentCondition({ ...currentCondition, ...data });
  }

  return (
    <Form
      layout="vertical"
      form={form}
      initialValues={currentCondition}
      onFinish={submitForm}
      onValuesChange={(data) => {
        if (data.value) {
          if (['string', 'numeric'].includes(selectedFullOperand.dataType) === false) {
            updateCurrentCondition({ ...currentCondition, ...data });
          }
        } else {
          updateCurrentCondition({ ...currentCondition, ...data });
        }
      }}>
      {currentCondition?.column ? (
        <>
          <Form.Item name="operator" dependencies={['column']}>
            <Select placeholder="Select Operator" options={selectableOperators} />
          </Form.Item>
          <Form.Item name="value" rules={[{ required: true }]}>
            <ValueSelection operand={selectedFullOperand} />
          </Form.Item>
        </>
      ) : (
        <Form.Item name="column">
          <Select
            onChange={(v) => {
              form.setFieldValue('column', v);
              setTimeout(() => {
                form.setFieldValue(
                  'operator',
                  (operatorMap[allAvailableOperands.find((i) => i.value === v).dataType] || [])[0]
                );
              }, 100);
            }}
            showSearch
            optionFilterProp="label"
            placeholder="Select Filter"
            options={selectableOperands}
          />
        </Form.Item>
      )}
      {selectedFullOperand.dataType === 'string' || selectedFullOperand.dataType === 'numeric' ? (
        <div className="text-right">
          <Button htmlType="submit">Apply</Button>
        </div>
      ) : null}
    </Form>
  );
}
