import { Button, Divider } from 'antd';
import FilterCondition from './FilterCondition';
import { useFilters } from './Filters';
import Icon from '@/src/components/Icon';

export default function FilterGroup({ group, index, onDelete }) {
  const {
    currentCondition,
    addNewConditionGroup,
    deleteConditionFromGroup,
    setCurrentEditingCondition
  } = useFilters();
  const lastCondition = group.conditions[group.conditions.length - 1];
  return (
    <div className="border-solid border-border rounded p-4">
      <div className="flex justify-between">
        <div className="text-base font-bold mb-2">Group {index}</div>
        <Button type="link" size="large" onClick={onDelete}>
          <Icon name="delete" className="text-danger" />
        </Button>
      </div>
      {group.conditions.map((c) => (
        <>
          <FilterCondition
            key={c.guid}
            condition={c.guid === currentCondition?.guid ? currentCondition : c}
            onDelete={() => {
              deleteConditionFromGroup(group.guid, c.guid);
            }}
            onClick={() => {
              setCurrentEditingCondition(group.guid, c.guid);
            }}
          />
          {c.column && c.operator && c.value ? (
            <Divider orientation="start" variant="dashed" key={`${c.guid}-divider`}>
              AND
            </Divider>
          ) : null}
        </>
      ))}
      {!lastCondition || (lastCondition.column && lastCondition.operator && lastCondition.value) ? (
        <div className="border-solid rounded border-border p-4 flex items-center justify-center bg-seperator">
          <Button onClick={() => addNewConditionGroup(group.guid)}>Add Filter</Button>
        </div>
      ) : null}
    </div>
  );
}
