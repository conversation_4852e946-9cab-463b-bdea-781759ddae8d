import { Input, Radio, Checkbox, Row, Col, InputNumber } from 'antd';

export default function ValueSelection({ operand, ...props }) {
  if (operand.dataType === 'severity') {
    return (
      <Checkbox.Group size="large" style={{ width: '100%' }} {...props}>
        <Row>
          {['Critical', 'High', 'Medium', 'Low'].map((s) => (
            <Col span={24} key={s}>
              <Checkbox size="large" value={s}>
                {s}
              </Checkbox>
            </Col>
          ))}
        </Row>
      </Checkbox.Group>
    );
  } else if (operand.dataType === 'dropdown') {
    return (
      <Checkbox.Group size="large" style={{ width: '100%' }} {...props}>
        <Row>
          {operand.options.map((s) => (
            <Col span={24} key={s}>
              <Checkbox size="large" value={s}>
                {s}
              </Checkbox>
            </Col>
          ))}
        </Row>
      </Checkbox.Group>
    );
  } else if (operand.dataType === 'boolean' || operand.dataType === 'nullable') {
    return (
      <Radio.Group size="large" style={{ width: '100%' }} {...props}>
        <Row>
          {['Yes', 'No'].map((s) => (
            <Col span={24} key={s}>
              <Radio size="large" value={s.toLowerCase()}>
                {s}
              </Radio>
            </Col>
          ))}
        </Row>
      </Radio.Group>
    );
  } else if (operand.dataType === 'numeric') {
    return <InputNumber className="w-full" autoFocus placeholder="Value" {...props} />;
  }
  return <Input placeholder="Value" autoFocus {...props} />;
}
