import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { Drawer, Row, Col, Divider, Select, Button } from 'antd';
import FilterGroup from './FilterGroup';
import FilterForm from './FilterForm';
import generateId from '@/src/utils/id';

const FilterContext = createContext();

export default function FiltersProvider({ children, onReset, onChange, disabledFilters }) {
  const conditionOptions = [
    { label: 'AND', value: 'and' },
    { label: 'OR', value: 'or' }
  ];
  const [currentCondition, setCurrentCondition] = useState();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [currentFilterGroups, updateCurrentFilterGroups] = useState([
    {
      guid: generateId(),
      operator: 'AND',
      conditions: [
        {
          guid: generateId()
        }
      ]
    }
  ]);
  const [currentAcceptingConditionGuid, setCurrentAcceptingConditionGuid] = useState(
    `${currentFilterGroups[0].guid}@${currentFilterGroups[0].conditions[0].guid}`
  );

  useEffect(() => {
    const [acceptingGroupGuid, acceptingConditionGuid] = currentAcceptingConditionGuid.split('@');
    let group = currentFilterGroups.find((g) => g.guid === acceptingGroupGuid);
    if (group) {
      let condition = group.conditions.find((c) => c.guid === acceptingConditionGuid);
      if (condition) {
        setCurrentCondition(condition);
      } else {
        setCurrentCondition(group.conditions[0]);
        setCurrentAcceptingConditionGuid(`${group.guid}@${group.conditions[0].guid}`);
      }
    } else {
      setCurrentCondition(currentFilterGroups[0].conditions[0]);
      setCurrentAcceptingConditionGuid(
        `${currentFilterGroups[0].guid}@${currentFilterGroups[0].conditions[0].guid}`
      );
    }
  }, [currentAcceptingConditionGuid, currentFilterGroups]);

  useEffect(() => {
    if (onChange) {
      let groups = currentFilterGroups.filter((group) =>
        (group.conditions || []).some((c) => c.column && c.operator && c.value)
      );
      if (groups.length > 0) {
        onChange(groups);
      } else {
        if (onReset) {
          onReset();
        }
      }
    }
    // eslint-disable-next-line
  }, [currentFilterGroups]);

  const allAvailableOperands = [
    {
      label: 'Severity',
      value: 'severity',
      dataType: 'severity'
    },
    {
      label: 'Platform',
      value: 'platform',
      dataType: 'dropdown',
      options: ['Windows', 'Linux', 'Mac']
    },
    {
      label: 'Mitre Threat',
      value: 'mitre_threat',
      dataType: 'boolean'
    },
    {
      label: 'CVE',
      value: 'cve',
      dataType: 'string'
    },
    {
      label: 'Score',
      value: 'score',
      dataType: 'numeric'
    },
    {
      label: 'EPSS',
      value: 'epss_probability',
      dataType: 'numeric'
    },
    {
      label: 'Exploitable',
      value: 'cisa_known_exploit',
      dataType: 'boolean'
    },
    {
      label: 'Description',
      value: 'description',
      dataType: 'string'
    },
    {
      label: 'KB Article',
      value: 'kb_article',
      dataType: 'string'
    },
    {
      label: 'Zero Day',
      value: 'zero_day',
      dataType: 'boolean'
    },
    {
      label: 'CVSS40 Source',
      value: 'cvss40_source',
      dataType: 'string'
    },
    {
      label: 'CVSS40 Type',
      value: 'cvss40_type',
      dataType: 'string'
    },
    {
      label: 'CVSS40 Vector',
      value: 'cvss40_vector_string',
      dataType: 'string'
    },
    {
      label: 'CVSS40 Severity',
      value: 'cvss40_base_severity',
      dataType: 'severity'
    },
    {
      label: 'CVSS40 Threat Score',
      value: 'cvss40_threat_score',
      dataType: 'numeric'
    },
    {
      label: 'CVSS40 Threat Severity',
      value: 'cvss40_threat_severity',
      dataType: 'severity'
    },
    {
      label: 'CVSS40 Environmental Score',
      value: 'cvss40_environmental_score',
      dataType: 'numeric'
    },
    {
      label: 'CVSS40 Environmental Severity',
      value: 'cvss40_environmental_severity',
      dataType: 'severity'
    },
    {
      label: 'CVSS3 Vector',
      value: 'cvss3_vector_string',
      dataType: 'string'
    },
    {
      label: 'CVSS3 Attack Vector',
      value: 'cvss3_attack_vector',
      dataType: 'string'
    },
    {
      label: 'CVSS3 Attack Complexity',
      value: 'cvss3_attack_complexity',
      dataType: 'string'
    },
    {
      label: 'CVSS3 Privileges Required',
      value: 'cvss3_privileges_required',
      dataType: 'string'
    },
    {
      label: 'CVSS3 Scope',
      value: 'cvss3_scope',
      dataType: 'string'
    },
    {
      label: 'CVSS3 Confidentiality Impact',
      value: 'cvss3_confidentiality_impact',
      dataType: 'string'
    },
    {
      label: 'CVSS3 Integrity Impact',
      value: 'cvss3_integrity_impact',
      dataType: 'string'
    },
    {
      label: 'CVSS3 Availability Impact',
      value: 'cvss3_availability_impact',
      dataType: 'string'
    },
    {
      label: 'CVSS3 Base Score',
      value: 'cvss3_base_score',
      dataType: 'numeric'
    },
    {
      label: 'CVSS3 Severity',
      value: 'cvss3_base_severity',
      dataType: 'severity'
    },
    {
      label: 'CVSS3 Exploitability Score',
      value: 'cvss3_exploitability_score',
      dataType: 'numeric'
    },
    {
      label: 'CVSS3 Impact Score',
      value: 'cvss3_impact_score',
      dataType: 'numeric'
    },
    {
      label: 'CVSS2 Base Score',
      value: 'cvss2_base_score',
      dataType: 'numeric'
    },
    {
      label: 'CVSS2 Vector',
      value: 'cvss2_vector',
      dataType: 'string'
    },
    {
      label: 'CVSS2 Access Vector',
      value: 'cvss2_access_vector',
      dataType: 'string'
    },
    {
      label: 'CVSS2 Access Complexity',
      value: 'cvss2_access_complexity',
      dataType: 'string'
    },
    {
      label: 'CVSS2 Authentication',
      value: 'cvss2_authentication',
      dataType: 'string'
    },
    {
      label: 'CVSS2 Confidentiality Impact',
      value: 'cvss2_confidentiality_impact',
      dataType: 'string'
    },
    {
      label: 'CVSS2 Integrity Impact',
      value: 'cvss2_integrity_impact',
      dataType: 'string'
    },
    {
      label: 'CVSS2 Availability Impact',
      value: 'cvss2_availability_impact',
      dataType: 'string'
    },
    {
      label: 'CVSS2 Severity',
      value: 'cvss2_severity',
      dataType: 'severity'
    },
    {
      label: 'CVSS2 Exploitability Score',
      value: 'cvss2_exploitability_score',
      dataType: 'numeric'
    },
    {
      label: 'CVSS2 Impact Score',
      value: 'cvss2_impact_score',
      dataType: 'numeric'
    }
  ].filter((o) => !disabledFilters?.includes(o.value));

  function updateCurrentConditionInGroup(condition) {
    updateCurrentFilterGroups((prev) => {
      const [acceptingGroupGuid, acceptingConditionGuid] = currentAcceptingConditionGuid.split('@');
      // add new condition to current filter groups
      const currentGroupIndex = prev.findIndex((g) => g.guid === acceptingGroupGuid);
      let conditionIndex = prev[currentGroupIndex].conditions.findIndex(
        (i) => i.guid === acceptingConditionGuid
      );
      return [
        ...prev.slice(0, currentGroupIndex),
        {
          ...prev[currentGroupIndex],
          conditions: [
            ...prev[currentGroupIndex].conditions.slice(0, conditionIndex),
            condition,
            ...prev[currentGroupIndex].conditions.slice(conditionIndex + 1)
          ]
        },
        ...prev.slice(currentGroupIndex + 1)
      ];
    });
  }

  function updateCurrentCondition(condition) {
    if (condition.column && !condition.operator) {
      let dataType = allAvailableOperands.find((o) => o.value === condition.column)?.dataType;
      if (dataType === 'severity' || dataType === 'dropdown') {
        condition.operator = 'In';
      } else {
        condition.operator = 'Equal';
      }
    }
    if (condition.column && condition.operator && condition.value) {
      updateCurrentConditionInGroup(condition);
    }
    setCurrentCondition(condition);
  }

  function addNewConditionGroup(groupGuid) {
    let newConditionId = generateId();
    updateCurrentFilterGroups((prev) => {
      const currentGroupIndex = prev.findIndex((g) => g.guid === groupGuid);
      setCurrentAcceptingConditionGuid((prev) => `${groupGuid}@${newConditionId}`);
      return [
        ...prev.slice(0, currentGroupIndex),
        {
          ...prev[currentGroupIndex],
          conditions: [...prev[currentGroupIndex].conditions, { guid: newConditionId }]
        },
        ...prev.slice(currentGroupIndex + 1)
      ];
    });
  }

  function deleteConditionFromGroup(groupGuid, conditionGuid) {
    updateCurrentFilterGroups((prev) => {
      const currentGroupIndex = prev.findIndex((g) => g.guid === groupGuid);
      let group = {
        ...prev[currentGroupIndex],
        conditions: prev[currentGroupIndex].conditions.filter((c) => c.guid !== conditionGuid)
      };
      if (group.conditions.length === 0) {
        group.conditions = [{ guid: generateId() }];
      }
      return [...prev.slice(0, currentGroupIndex), group, ...prev.slice(currentGroupIndex + 1)];
    });
  }

  function addNewFilterGroup() {
    updateCurrentFilterGroups((prev) => {
      return [...prev, { guid: generateId(), operator: 'AND', conditions: [] }];
    });
  }

  function handleUpdateLogicUpdate(groupGuid, operator) {
    updateCurrentFilterGroups((prev) => {
      const index = prev.findIndex((g) => g.guid === groupGuid);
      return [...prev.slice(0, index), { ...prev[index], operator }, ...prev.slice(index + 1)];
    });
  }

  function deleteFilterGroup(groupGuid) {
    if (currentFilterGroups.length > 1) {
      updateCurrentFilterGroups((prev) => {
        return prev.filter((g) => g.guid !== groupGuid);
      });
    }
  }

  function setCurrentEditingCondition(groupGuid, conditionGuid) {
    setCurrentAcceptingConditionGuid(`${groupGuid}@${conditionGuid}`);
  }

  const appliedFiltersCount = useMemo(() => {
    let counts = 0;
    currentFilterGroups.forEach((group) =>
      (group.conditions || []).forEach((c) => {
        if (c.column && c.operator && c.value) {
          counts++;
        }
      })
    );
    return counts;
  }, [currentFilterGroups]);

  function resetFilters() {
    updateCurrentFilterGroups([
      {
        guid: generateId(),
        operator: 'AND',
        conditions: [
          {
            guid: generateId()
          }
        ]
      }
    ]);
  }

  const value = useMemo(
    () => ({
      allAvailableOperands,
      currentFilterGroups,
      addNewConditionGroup,
      currentAcceptingConditionGuid,
      setCurrentAcceptingConditionGuid,
      updateCurrentFilterGroups,
      currentCondition,
      updateCurrentCondition,
      deleteConditionFromGroup,
      setCurrentEditingCondition,
      deleteFilterGroup,
      handleUpdateLogicUpdate,
      addNewFilterGroup,
      isDrawerOpen,
      setIsDrawerOpen,
      appliedFiltersCount,
      resetFilters
    }),
    // eslint-disable-next-line
    [
      allAvailableOperands,
      currentFilterGroups,
      currentAcceptingConditionGuid,
      isDrawerOpen,
      appliedFiltersCount
    ]
  );

  return (
    <FilterContext.Provider value={value}>
      {children}
      <Drawer
        title="Filter Vulnerabilities"
        onClose={() => {
          setIsDrawerOpen(false);
        }}
        destroyOnClose
        width="50%"
        open={isDrawerOpen}>
        <Row gutter={32} className="h-full">
          <Col
            span={12}
            className="border-r border-border border-solid border-l-0 border-t-0 border-b-0 h-full overflow-auto">
            <h3 className="items-center flex justify-between">
              Filters
              <Button onClick={resetFilters}>Discard</Button>
            </h3>
            {currentFilterGroups.map((group, index) => (
              <>
                <FilterGroup
                  key={group.guid}
                  group={group}
                  index={index + 1}
                  acceptingGUID={currentAcceptingConditionGuid}
                  onDelete={() => {
                    deleteFilterGroup(group.guid);
                  }}
                />
                {index < currentFilterGroups.length ? (
                  <>
                    <Divider className="my-1" key={`${group.guid}-divider`}>
                      <Select
                        value={group.operator}
                        options={conditionOptions}
                        onChange={(value) => {
                          handleUpdateLogicUpdate(group.guid, value);
                        }}
                        placeholder="Condition Type"
                      />
                    </Divider>
                    {index === currentFilterGroups.length - 1 ? (
                      <div className="text-center">
                        <Button onClick={addNewFilterGroup}>Add Group</Button>
                      </div>
                    ) : null}
                  </>
                ) : null}
              </>
            ))}
          </Col>
          {currentCondition ? (
            <Col span={12} key={currentCondition.guid} className="h-full overflow-auto">
              <h3>Add Filter</h3>
              <FilterForm key={currentAcceptingConditionGuid} />
            </Col>
          ) : null}
        </Row>
      </Drawer>
    </FilterContext.Provider>
  );
}

export function useFilters() {
  return useContext(FilterContext);
}
