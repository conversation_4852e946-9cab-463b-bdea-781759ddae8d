import { Button } from 'antd';
import Icon from '@/src/components/Icon';
import { useFilters } from './Filters';
import { useMemo } from 'react';

export default function FilterCondition({ condition, onDelete, onClick }) {
  const { allAvailableOperands } = useFilters();
  const selectedOperand = useMemo(() => {
    if (condition?.column) {
      let operand = allAvailableOperands.find((operand) => operand.value === condition.column);
      if (operand) {
        return operand.label;
      }
    }
    return [];
    // eslint-disable-next-line
  }, [condition?.column, allAvailableOperands]);
  return (
    <div
      className="font-semibold border-solid rounded border-border p-4 flex items-center justify-center bg-seperator mb-2"
      onClick={onClick}>
      {condition.column ? (
        <div className="flex flex-1 justify-between">
          <div>
            <div>
              {selectedOperand} {condition.operator}
            </div>
            <div className="text-base font-bold">
              {Array.isArray(condition.value) ? condition.value.join(', ') : condition.value}
            </div>
          </div>
          <Button type="link" size="large" onClick={onDelete}>
            <Icon name="delete" className="text-danger" />
          </Button>
        </div>
      ) : (
        'Your Filter Will Appear Here'
      )}
    </div>
  );
}
