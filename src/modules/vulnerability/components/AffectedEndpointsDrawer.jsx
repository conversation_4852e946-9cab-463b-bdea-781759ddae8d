import { Drawer } from 'antd';
import { useNavigate } from 'react-router-dom';
import { CrudProvider } from '@/src/hooks/crud';
import { getAssetsForSoftwareId, getDevicesForCVE } from '../api/vulnerability-list';

export function AffectedEndpointsDrawer({ currentCategory, vulnerability, onClose }) {
  const navigate = useNavigate();
  const assetColumns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   key: 'id'
    // },
    {
      title: 'Host Name',
      dataIndex: 'host_name',
      key: 'host_name',
      render({ record }) {
        return (
          <div className="flex items-center">
            {/* eslint-disable-next-line */}
            <a
              className="cursor-pointer"
              onClick={() =>
                navigate(
                  currentCategory === 'software'
                    ? `/inventory/endpoints/${record.id}`
                    : `/inventory/network-devices/${record.id}`
                )
              }>
              {record.hostname}
            </a>
          </div>
        );
      }
    },
    ...(currentCategory === 'software'
      ? [
          {
            title: 'Platform Version',
            dataIndex: 'platform_version',
            key: 'platform_version'
          },
          {
            title: 'Hardware Model',
            dataIndex: 'hardware_model',
            key: 'hardware_model'
          }
        ]
      : [
          {
            title: 'Device Model',
            dataIndex: 'device_model',
            key: 'device_model'
          },
          {
            title: 'Device Type',
            dataIndex: 'device_type',
            key: 'device_type'
          }
        ])
  ];

  return (
    <Drawer
      title={`
          ${currentCategory === 'software' ? 'Endpoints' : 'Devices'} for Vulnerability ${
        vulnerability ? vulnerability.cve : null
      }`}
      placement="right"
      width="50%"
      onClose={onClose}
      destroyOnClose
      open={Boolean(vulnerability)}>
      {Boolean(vulnerability) && (
        <CrudProvider
          resourceTitle="Assets"
          hasSearch
          fetchFn={(...args) =>
            currentCategory === 'software'
              ? getAssetsForSoftwareId(-1, ...args, vulnerability.cve, vulnerability.zero_day)
              : getDevicesForCVE(...args, vulnerability)
          }
          columns={assetColumns}
        />
      )}
    </Drawer>
  );
}
