import { Row, Col } from 'antd';
import WidgetContainer from '@/src/components/widget/WidgetContainer';
import Chart from '@/src/components/widget/views/chart/Chart';
import { useEffect, useState } from 'react';
import { getSeverityWiseCountApi } from '../api/vulnerability-list';

export default function VulnerabilityWidgets({ category, isZeroDay }) {
  const zero_day_published_date_query = {
    software: `SELECT COUNT(DISTINCT sc.cve) AS TOTAL,CASE WHEN cvd.severity = '' THEN 'Unknown' ELSE cvd.severity END AS severity,CASE  WHEN DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) > 90 THEN '> 90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) > 60 AND DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) < 90 THEN '60-90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) > 30 AND DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) < 60 THEN '30-60 days' WHEN DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) < 30 THEN '< 30 days'  END Days FROM tbl_software_cves as sc LEFT JOIN tbl_asset_software as asw ON sc.software_id = asw.software_id LEFT JOIN tbl_cve_details as cvd ON sc.cve = cvd.cve LEFT JOIN tbl_vulnerability_cve_exception as vce ON sc.cve = vce.cve AND asw.asset_id = vce.asset_id WHERE sc.zero_day = 'yes' and asw.asset_id in (ASSETS) AND asw.archived = 0 AND severity IS NOT NULL AND (vce.cve IS NULL OR vce.asset_id IS NULL) group by severity,days;`,
    network: `SELECT COUNT(DISTINCT nc.CVE) AS TOTAL,CASE WHEN cvd.severity = '' THEN 'Unknown' ELSE cvd.severity END AS severity,CASE WHEN DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) > 90 THEN '> 90 days' WHEN DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) > 60 AND DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) < 90 THEN '60-90 days' WHEN DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) > 30 AND DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) < 60 THEN '30-60 days' WHEN DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) < 30 THEN '< 30 days' END DAYS FROM tbl_network_cves as nc left join tbl_cve_details as cvd ON nc.cve = cvd.cve WHERE nc.zero_day = 'yes' GROUP BY SEVERITY,DAYS;`
  };
  const zero_day_discovered_date_query = {
    software: `SELECT COUNT(DISTINCT sc.cve) AS TOTAL,CASE WHEN cvd.severity = '' THEN 'Unknown' ELSE cvd.severity END AS severity,CASE  WHEN DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) > 90 THEN '> 90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) > 60 AND DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) < 90 THEN '60-90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) > 30 AND DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) < 60 THEN '30-60 days' WHEN DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) < 30 THEN '< 30 days'  END Days FROM tbl_software_cves as sc LEFT JOIN tbl_asset_software as asw ON sc.software_id = asw.software_id LEFT JOIN tbl_cve_details as cvd ON sc.cve = cvd.cve LEFT JOIN tbl_vulnerability_cve_exception as vce ON sc.cve = vce.cve AND asw.asset_id = vce.asset_id WHERE sc.zero_day = 'yes' and asw.asset_id in (ASSETS) AND asw.archived = 0 AND severity IS NOT NULL AND (vce.cve IS NULL OR vce.asset_id IS NULL) group by severity,days;`,
    network: `SELECT COUNT(DISTINCT nc.cve) AS TOTAL,CASE WHEN cvd.severity = '' THEN 'Unknown' ELSE cvd.severity END AS severity,CASE  WHEN DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) > 90 THEN '> 90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) > 60 AND DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) < 90 THEN '60-90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) > 30 AND DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) < 60 THEN '30-60 days' WHEN DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) < 30 THEN '< 30 days'  END Days FROM tbl_network_cves as nc left join tbl_cve_details as cvd ON nc.cve = cvd.cve WHERE nc.zero_day = 'yes' group by severity,days;`
  };
  const published_date_query = {
    software: `SELECT COUNT(DISTINCT sc.cve) AS TOTAL,CASE WHEN cvd.severity = '' THEN 'Unknown' ELSE cvd.severity END AS severity,CASE  WHEN DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) > 90 THEN '> 90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) > 60 AND DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) < 90 THEN '60-90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) > 30 AND DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) < 60 THEN '30-60 days' WHEN DATE_PART('day',NOW() - to_timestamp(cvd.published_date/1000)) < 30 THEN '< 30 days'  END Days FROM tbl_software_cves as sc LEFT JOIN tbl_asset_software as asw ON sc.software_id = asw.software_id LEFT JOIN tbl_cve_details as cvd ON sc.cve = cvd.cve LEFT JOIN tbl_vulnerability_cve_exception as vce ON sc.cve = vce.cve AND asw.asset_id = vce.asset_id WHERE asw.asset_id in (ASSETS) AND asw.archived = 0 AND severity IS NOT NULL AND (vce.cve IS NULL OR vce.asset_id IS NULL) group by severity,days`,
    network: `SELECT COUNT(DISTINCT nc.CVE) AS TOTAL,CASE WHEN cvd.severity = '' THEN 'Unknown' ELSE cvd.severity END AS severity,CASE WHEN DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) > 90 THEN '> 90 days' WHEN DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) > 60 AND DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) < 90 THEN '60-90 days' WHEN DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) > 30 AND DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) < 60 THEN '30-60 days' WHEN DATE_PART('day',NOW() - TO_TIMESTAMP(cvd.PUBLISHED_DATE / 1000)) < 30 THEN '< 30 days' END DAYS FROM tbl_network_cves as nc left join tbl_cve_details as cvd ON nc.cve = cvd.cve GROUP BY SEVERITY,DAYS;`
  };
  const discovered_date_query = {
    software: `SELECT COUNT(DISTINCT sc.cve) AS TOTAL,CASE WHEN cvd.severity = '' THEN 'Unknown' ELSE cvd.severity END AS severity,CASE  WHEN DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) > 90 THEN '> 90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) > 60 AND DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) < 90 THEN '60-90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) > 30 AND DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) < 60 THEN '30-60 days' WHEN DATE_PART('day',NOW() - to_timestamp(sc.discovered_time/1000)) < 30 THEN '< 30 days'  END Days FROM tbl_software_cves as sc LEFT JOIN tbl_asset_software as asw ON sc.software_id = asw.software_id LEFT JOIN tbl_cve_details as cvd ON sc.cve = cvd.cve LEFT JOIN tbl_vulnerability_cve_exception as vce ON sc.cve = vce.cve AND asw.asset_id = vce.asset_id WHERE asw.asset_id in (ASSETS) AND asw.archived = 0 AND severity IS NOT NULL AND (vce.cve IS NULL OR vce.asset_id IS NULL) group by severity,days`,
    network: `SELECT COUNT(DISTINCT nc.cve) AS TOTAL,CASE WHEN cvd.severity = '' THEN 'Unknown' ELSE cvd.severity END AS severity,CASE  WHEN DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) > 90 THEN '> 90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) > 60 AND DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) < 90 THEN '60-90 days'   WHEN DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) > 30 AND DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) < 60 THEN '30-60 days' WHEN DATE_PART('day',NOW() - to_timestamp(nc.discovered_time/1000)) < 30 THEN '< 30 days'  END Days FROM tbl_network_cves as nc left join tbl_cve_details as cvd ON nc.cve = cvd.cve group by severity,days`
  };

  const [severityCounts, setSeverityCounts] = useState([]);
  useEffect(() => {
    getSeverityWiseCountApi(category, isZeroDay).then((res) => {
      setSeverityCounts(res);
    });
  }, [category, isZeroDay]);

  return (
    <div className="mt-2" style={{ height: '100%' }} key={`${category}-${isZeroDay}`}>
      <Row gutter={4} className="h-full">
        <Col span={8} className="h-full">
          <div className="px-4 flex flex-col bg-seperator py-2 rounded h-full">
            <h3 className="text-primary">Infra. Vulnerabilities</h3>
            <div className="flex-1 min-h-0 flex flex-col">
              <Chart
                widget={{
                  type: 'BarChart',
                  'x-axis': 'severity',
                  'y-axis': 'total'
                }}
                data={{
                  'x-axis': 'severity',
                  'y-axis': 'total',
                  result: severityCounts
                    .filter((f) => f.severity !== 'All')
                    .map((i) => ({ ...i, total: i.count }))
                }}
              />
            </div>
          </div>
        </Col>
        <Col span={8} className="h-full">
          <div className="px-4 bg-seperator py-2 rounded h-full flex flex-col flex-1">
            <h3 className="text-primary">Published Date</h3>
            <div className="flex-1 min-h-0 flex flex-col">
              <WidgetContainer
                isPreview={true}
                widget={{
                  widgetProperties: {
                    labelSpan: 6
                  },
                  type: 'VulnerabilityMatrix',
                  xAxis: 'days',
                  yAxis: 'severity,total',
                  scope: {
                    assets: [],
                    assetFilter: 1
                  },
                  query: isZeroDay
                    ? zero_day_published_date_query[category]
                    : published_date_query[category]
                }}
              />
            </div>
          </div>
        </Col>
        <Col span={8} className="h-full">
          <div className="px-4 bg-seperator py-2 rounded h-full flex flex-col flex-1">
            <h3 className="text-primary">Discovered Date</h3>
            <div className="flex-1 min-h-0 flex flex-col">
              <WidgetContainer
                isPreview={true}
                widget={{
                  widgetProperties: {
                    labelSpan: 6
                  },
                  type: 'VulnerabilityMatrix',
                  xAxis: 'days',
                  yAxis: 'severity,total',
                  scope: {
                    assets: [],
                    assetFilter: 1
                  },
                  query: isZeroDay
                    ? zero_day_discovered_date_query[category]
                    : discovered_date_query[category]
                }}
              />
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
}
