import { Drawer } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { getAffectedPackagesForCVE } from '../api/vulnerability-list';

export function AffectedPackagesDrawer({ currentCategory, vulnerability, onClose, endpointId }) {
  const assetColumns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   key: 'id'
    // },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorting: false
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version',
      sorting: false
    },
    {
      title: 'Release',
      dataIndex: 'release',
      key: 'release',
      sorting: false
    },
    {
      title: 'Fixed',
      dataIndex: 'resolved_version',
      key: 'resolved_version',
      sorting: false
    },
    {
      title: 'Vendor',
      dataIndex: 'vendor',
      key: 'vendor',
      sorting: false
    }
  ];

  return (
    <Drawer
      title={`
          Affected ${currentCategory === 'software' ? 'Software' : 'Devices'} for Vulnerability ${
        vulnerability ? vulnerability.cve : null
      }`}
      placement="right"
      width="50%"
      onClose={onClose}
      destroyOnClose
      open={Boolean(vulnerability)}>
      {Boolean(vulnerability) && (
        <CrudProvider
          resourceTitle="Affected Packages"
          fetchFn={(...args) =>
            currentCategory === 'software'
              ? getAffectedPackagesForCVE(
                  vulnerability.cve,
                  ...args,
                  +endpointId,
                  vulnerability.zero_day
                )
              : getAffectedPackagesForCVE(
                  vulnerability.cve,
                  ...args,
                  +endpointId,
                  vulnerability.zero_day
                )
          }
          columns={assetColumns}
        />
      )}
    </Drawer>
  );
}
