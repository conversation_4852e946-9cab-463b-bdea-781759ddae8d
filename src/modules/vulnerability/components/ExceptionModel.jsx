import { Modal, Form, Input, Col, Row, Radio } from 'antd';

import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';

export const ExceptionForm = ({ forPatch, category, disabled }) => (
  <div className="flex flex-col min-h-0 h-full overflow-y-auto overflow-x-hidden">
    <Row gutter={32}>
      {!forPatch ? (
        <Col span={24}>
          <Form.Item label=" " noStyle name="scope" rules={[{ required: true }]}>
            <AssetScopePicker
              label="Scope"
              gutter={16}
              name={['scope', 'assetFilter']}
              subname={['scope', 'assets']}
              disabled={disabled}
              disabledScopeOptions={category === 'network' ? [1, 2, 3, 4, 5, 6] : undefined}
            />
          </Form.Item>
        </Col>
      ) : null}

      <Col span={24}>
        <Form.Item label="Exception Type" name="exception_type" rules={[{ required: true }]}>
          <Radio.Group>
            <Radio value="Acceptable Risk">Acceptable Risk</Radio>
            <Radio value="False Positive">Not Applicable</Radio>
          </Radio.Group>
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="Reason For Execution" name="reason_for_exclusion">
          <Input.TextArea placeholder="Reason For Execution" />
        </Form.Item>
      </Col>
    </Row>
  </div>
);

export default function ExceptionModel({ open, category, onAdd, onCancel, forPatch }) {
  const [form] = Form.useForm();
  return (
    <Modal
      open={open}
      title="Add Exception"
      okText="Save"
      cancelText="Cancel"
      onCancel={onCancel}
      centered={true}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            form.resetFields();
            onAdd(values);
          })
          .catch((info) => {
            console.log('Validate Failed:', info);
          });
      }}>
      <Form
        form={form}
        layout="vertical"
        name="form_in_modal"
        initialValues={{
          exception_type: 'Acceptable Risk'
        }}>
        <ExceptionForm category={category} forPatch={forPatch} />
      </Form>
    </Modal>
  );
}
