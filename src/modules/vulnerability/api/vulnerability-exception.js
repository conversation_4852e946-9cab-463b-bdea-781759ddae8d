import api from '@api';
import {
  transformAssetScope,
  transformAssetScopeForServer
} from '@/src/components/pickers/AssetScopePicker';

const END_POINT = `/software/vulnerability-exception`;

const transform = (item, forServer) => {
  return {
    cve: item.cve,
    name: item.cve,
    exception_type: item.exception_type,
    reason_for_exclusion: item.reason_for_exclusion,
    created_by: item.created_by,
    assets: item.assets,
    ...(!forServer
      ? {
          id: item.id,
          created_time: item.created_time,
          modified_time: item.modified_time,
          ...transformAssetScope(item)
        }
      : {
          ...transformAssetScopeForServer(item)
        })
  };
};

const sortKeyMap = {
  cve: 'cve',
  exception_type: 'exception_type',
  reason_for_exclusion: 'reason_for_exclusion'
};

const searchableColumns = ['cve', 'exception_type', 'reason_for_exclusion'];

export function getAllVulnerabilityExceptionApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map((item) => transform(item))
      };
    });
}

export function getVulnerabilityExceptionApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateVulnerabilityExceptionApi(item) {
  return api
    .put(`${END_POINT}/${item.id}`, transform(item, true))
    .then((data) => getVulnerabilityExceptionApi(data.result));
}

export function createVulnerabilityExceptionApi(item) {
  return api
    .post(`${END_POINT}`, transform(item, true))
    .then((data) => getVulnerabilityExceptionApi(data.result));
}

export function deleteVulnerabilityExceptionApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
