import Moment from 'moment';
import api from '@api';
import generateId from '@/src/utils/id';

const END_POINT = `/software/vulnerability`;
const NETWORK_END_POINT = `/network/vulnerability`;

export const transform = (item) => ({
  ...item,
  id: generateId(),
  name: item.name,
  version: item.version,
  severity: item.Severity || item.severity,
  assetCount: item.asset_count,
  device_count: item.device_count,
  software_count: item.software_count,
  description: item.description || item.Description,
  score: item.Score || item.score,
  cve: item.cve,
  cvss3: {
    severity: item.cvss3_base_severity || item.cvss3_severity,
    baseScore: item.cvss3_base_score,
    impactScore: item.cvss3_impact_score,
    attackComplexity: item.cvss3_attack_complexity,
    scope: item.cvss3_scope,
    availabilityImpact: item.cvss3_availability_impact,
    attackVector: item.cvss3_attack_vector,
    vector: item.cvss3_vector_string,
    confidentialityImpact: item.cvss3_confidentiality_impact,
    exploitabilityScore: item.cvss3_exploitability_score,
    integrityImpact: item.cvss3_integrity_impact,
    privilegesRequired: item.cvss3_privileges_required
  },
  cvss2: {
    severity: item.cvss2_severity,
    baseScore: item.cvss2_base_score,
    impactScore: item.cvss2_impact_score,
    attackComplexity: item.cvss2_access_complexity,
    scope: item.cvss2_scope,
    availabilityImpact: item.cvss2_availability_impact,
    attackVector: item.cvss2_access_vector,
    vector: item.cvss2_vector,
    confidentialityImpact: item.cvss2_confidentiality_impact,
    exploitabilityScore: item.cvss2_exploitability_score,
    integrityImpact: item.cvss2_integrity_impact,
    privilegesRequired: item.cvss2_privileges_required
  },
  publishedDate: Moment(item.PublishedDate).unix() * 1000,
  resolved_version: item['resolved_version'] || item['resolvedversion'],
  kb_article: item['kb_article'] || item['kbArticle'],
  kb_article_url: item['kb_article_url'] || item['kbArticleUrl'],
  cisa_known_exploit: item['cisa_known_exploit'] || item.cisaKnownExploit,
  cvss3_base_score: item.cvss3_base_score || item.cvss3BaseScore,
  cvss2_base_score: item.cvss2_base_score || item.cvss2BaseScore,
  references: item.references || []
});

const sortKeyMap = {
  severity: 'Severity',
  score: 'Score',
  description: 'Description',
  assetCount: 'asset_count',
  softwareId: 'software_id',
  publishedDate: 'PublishedDate'
};

const searchableColumns = [
  'cve',
  'severity',
  'score',
  'cvss3_base_score',
  'cvss2_base_score',
  'epss_probability'
];

export function getVulnerabilityByCVEApi(category, cve) {
  return api
    .post(`${category === 'software' ? END_POINT : NETWORK_END_POINT}/cve`, {
      cve
    })
    .then((data) => {
      return transform(data.result);
    });
}

export function getAllVulnerabilityApi(offset, size, sortFilter, filter = {}) {
  return api
    .post(
      `${filter.category === 'software' ? END_POINT : NETWORK_END_POINT}${
        filter.zeroDayVulnerability ? '/zero/day' : ''
      }`,
      {
        offset,
        size,
        ...(sortFilter && sortFilter.sort && sortFilter.sort.field
          ? {
              sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
                sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
              }`
            }
          : {}),
        // ...(filter.severity && filter.severity !== 'All' ? { severity: filter.severity } : {}),
        // ...(filter.includeExploitOnly ? { exploit: 'yes' } : {}),
        ...(filter.zeroDayVulnerability ? { zero_day: 'yes' } : {}),
        ...(filter.filters ? { filter: filter.filters } : {}),
        qualification: sortFilter.searchTerm
          ? searchableColumns.map((c) => ({
              operator: 'Contains',
              column: c,
              value: sortFilter.searchTerm
            }))
          : []
      }
    )
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getVulnerabilityApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

const affectedPackageSearchColumns = ['name', 'version', 'release', 'resolved_version', 'vendor'];
export function getAffectedPackagesForCVE(cve, offset, size, sortFilter, assetId, ziroDay) {
  return api
    .post(`/software/vulnerability/cve/affected/packages`, {
      ...(cve ? { cve } : {}),
      ...(assetId ? { assetId } : {}),
      ...(ziroDay ? { zero_day: ziroDay } : {}),
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${sortFilter.sort.field}`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? affectedPackageSearchColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then(({ result, totalCount }) => {
      return {
        totalCount: totalCount,
        result: result.map((item) => ({
          id: item.id,
          name: item.name,
          version: item.version,
          resolved_version: item.resolved_version,
          release: item.release,
          vendor: item.vendor
        }))
      };
    });
}

const searchableColumnsForAsset = ['platform_version', 'host_name'];
export function getAssetsForSoftwareId(id, offset, size, sortFilter, cve, ziroDay) {
  return api
    .post(`/software/vulnerability/assets/${id}`, {
      ...(cve ? { cve } : {}),
      ...(ziroDay ? { zero_day: ziroDay } : {}),
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${sortFilter.sort.field}`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumnsForAsset.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then(({ result, totalCount }) => {
      return {
        totalCount: totalCount,
        result: result.map((item) => ({
          id: item.id,
          platform_version: item.platform_version,
          hostname: item.host_name
        }))
      };
    });
}

const searchableColumnsForDevices = ['device_model', 'host_name'];
export function getDevicesForCVE(offset, size, sortFilter, vulnerability) {
  return api
    .post(`/network/vulnerability/devices`, {
      ...(vulnerability
        ? { cve: vulnerability.cve, name: vulnerability.name, version: vulnerability.version }
        : {}),
      ...(vulnerability.zero_day ? { zero_day: vulnerability.zero_day } : {}),
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${sortFilter.sort.field}`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumnsForDevices.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then(({ result, totalCount }) => {
      return {
        totalCount: totalCount,
        result: result.map((item) => ({
          id: item.id,
          device_model: item.device_model,
          device_type: item.device_type,
          hostname: item.host_name
        }))
      };
    });
}
export function getSeverityWiseCountApi(category = 'software', isZeroDay = false) {
  return api
    .get(`/${category}/vulnerability/severities${isZeroDay ? '/zero/day' : ''}`)
    .then((data) => {
      const total = Object.keys(data.result || {})
        .filter((key) => key !== 'total_count')
        .reduce((total, key) => total + data.result[key], 0);
      return [{ severity: 'All', count: total }].concat(
        Object.keys(data.result || {})
          .filter((key) => key !== 'total_count')
          .map((key) => ({
            severity: key,
            count: data.result[key]
          }))
      );
    });
}

export function assetPatchCveRelation(cve) {
  return api.get(`/patch/asset-patch-cve-relation/${cve}`).then((res) => {
    return res.result || {};
  });
}

export function getCVEbyCVEIds(offset, size, sortFilter, filters = {}) {
  const cve = (filters.cveNumber || '').split(',').filter((c) => c !== '');

  if (!cve.length) {
    return Promise.resolve({
      totalCount: 0,
      result: []
    });
  }
  return api
    .post(`/patch/asset-patch-cve-relation/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: (sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
      ).concat(
        filters.cveNumber
          ? [
              {
                operator: 'in',
                column: 'cve',
                value: cve
                // condition: 'and'
              }
            ]
          : [],
        filters.patchName
          ? [
              {
                operator: 'Contains',
                column: 'patch_name',
                value: filters.patchName,
                condition: 'and'
              }
            ]
          : []
      )
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function vulnerabilityScanApi(assetId) {
  return api.get(`/software/vulnerability/scan/${assetId ? assetId : -1}`);
}

const vulnerabilitySearchableColumns = [
  'cve',
  'severity',
  'score',
  'cvss3_base_score',
  'cvss2_base_score',
  'epss_probability'
];

const vulnerabilitySortKeyMap = {
  cvss3BaseScore: 'cvss3_base_score',
  cvss2BaseScore: 'cvss2_base_score',
  description: 'description',
  version: 'version'
};

export function getVulnerabilityByNetworkDevice(offset, size, sortFilter, filter = {}) {
  return api
    .post(`/inventory/device/vulnerabilities/${filter.deviceId}`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              vulnerabilitySortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      ...(filter.zeroDayVulnerability ? { zero_day: 'yes' } : {}),
      ...(filter.filters ? { filter: filter.filters } : {}),
      qualification: sortFilter.searchTerm
        ? vulnerabilitySearchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getGroupIBResponseByCveApi(cve) {
  // return Promise.resolve({
  //   affectedSoftware: [
  //     {
  //       name: 'npmjs npm',
  //       operator: 'le',
  //       version: '7.24.2'
  //     },
  //     {
  //       name: 'npmjs npm',
  //       operator: 'le',
  //       version: '8.1.3'
  //     }
  //   ],
  //   bulletinFamily: 'cve',
  //   cpe: ['cpe:/a:npmjs:npm::::'],
  //   cpeTable: [
  //     {
  //       edition: '',
  //       language: '',
  //       other: '',
  //       part: 'a',
  //       prefix: '',
  //       product: 'npm',
  //       string: 'cpe:/a:npmjs:npm',
  //       string23: 'cpe:2.3:a:npmjs:npm:*:*:*:*:*:*:*:*',
  //       swEdition: '',
  //       targetHw: '',
  //       type: 'software',
  //       update: '',
  //       vendor: 'npmjs',
  //       version: ''
  //     }
  //   ],
  //   cveList: ['CVE-2021-43616'],
  //   cveListEpss: [
  //     {
  //       cve: 'CVE-2021-43616',
  //       date: '2024-07-29T00:00:00Z',
  //       epss: 0.02462,
  //       percentile: 0.90167
  //     }
  //   ],
  //   cvss: {
  //     score: 9.8,
  //     vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'
  //   },
  //   cvssAttackVector: 'network',
  //   darkweb: [],
  //   dateLastSeen: '2024-08-04T06:58:05+05:30',
  //   dateModified: '2024-08-04T06:46:49+05:30',
  //   datePublished: '2021-11-13T20:45:07+05:30',
  //   description:
  //     'A vulnerability classified as critical has been found in NPM up to 8.1.3 (Software Library). This affects an unknown part of the file package-lock.json of the component CI Command Handler. There is no information about possible countermeasures known. It may be suggested to replace the affected object with an alternative product.',
  //   displayOptions: {
  //     favouriteForCompanies: [],
  //     hideForCompanies: [],
  //     isFavourite: false,
  //     isHidden: false
  //   },
  //   epss: {
  //     cve: 'CVE-2021-43616',
  //     date: '2024-07-29T00:00:00Z',
  //     epss: 0.02462,
  //     percentile: 0.90167
  //   },
  //   evaluation: {
  //     admiraltyCode: 'A1',
  //     credibility: 100,
  //     reliability: 100,
  //     severity: 'red',
  //     tlp: 'green',
  //     ttl: 30
  //   },
  //   exploitCount: 1,
  //   exploitList: [
  //     {
  //       bulletinFamily: 'exploit',
  //       cvelist: ['CVE-2021-43616'],
  //       href: 'https://github.com/icatalina/CVE-2021-43616',
  //       id: '68C9985B-7F88-5986-95D2-11DCE186BBFB',
  //       modified: '2022-04-12T02:59:18+03:00',
  //       reporter: 'icatalina',
  //       sourceData:
  //         "Repo demonstrating CVE-2021-43616 / https://github.com/npm/cli/issues/2701\n\nRemove the `node_modules` folder and run `npx npm@8 ci`, you can see how\nnpm will install version 2.2.x (2.2.16 at the time of this commit) even though\npackage-lock.json requires 2.0.0\n\n```\ncat node_modules/shortid/package.json\n```\n\nI've commited the `node_modules` from the original install so the issue is obvious\nafter running `npm ci`\n",
  //       sourceHref: '',
  //       title: 'Exploit for Insufficient Verification of Data Authenticity in Npmjs Npm',
  //       type: 'githubexploit'
  //     }
  //   ],
  //   exploitation: [],
  //   extCvss: {
  //     base: 9.8,
  //     environmental: 0,
  //     exploitability: 3.9,
  //     impact: 5.9,
  //     mImpact: 0,
  //     overall: 9.8,
  //     temporal: 0,
  //     vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'
  //   },
  //   extDescription:
  //     'The npm ci command in npm 7.x and 8.x through 8.1.3 proceeds with an installation even if dependency information in package-lock.json differs from package.json. This behavior is inconsistent with the documentation, and makes it easier for attackers to install malware that was supposed to have been blocked by an exact version match requirement in package-lock.json. NOTE: The npm team believes this is not a vulnerability. It would require someone to socially engineer package.json which has different dependencies than package-lock.json. That user would have to have file system or write access to change dependencies. The npm team states preventing malicious actors from socially engineering or gaining file system access is outside the scope of the npm CLI.',
  //   githubLinkList: ['https://github.com/icatalina/CVE-2021-43616'],
  //   hasExploit: true,
  //   href: 'https://web.nvd.nist.gov/view/vuln/detail?vulnId=CVE-2021-43616',
  //   id: 'CVE-2021-43616',
  //   lastseen: '2024-08-04T06:58:05+05:30',
  //   mergedCvss: 9.8,
  //   modified: '2024-08-04T06:46:49+05:30',
  //   portalLink: '',
  //   provider: 'vulners.com',
  //   published: '2021-11-13T20:45:07+05:30',
  //   references: [
  //     'https://github.com/npm/cli/commit/457e0ae61bbc55846f5af44afa4066921923490f',
  //     'https://github.com/npm/cli/issues/2701#issuecomment-979054224',
  //     'https://github.com/npm/cli/issues/2701',
  //     'https://docs.npmjs.com/cli/v8/commands/npm-ci',
  //     'https://medium.com/cider-sec/this-time-we-were-lucky-85c0dcac94a0',
  //     'https://security.netapp.com/advisory/ntap-20211210-0002/',
  //     'https://github.com/icatalina/CVE-2021-43616',
  //     'https://docs.npmjs.com/cli/v7/commands/npm-ci',
  //     'https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/NXNVFKOF5ZYH5NIRWHKN6O6UBCHDV6FE/',
  //     'https://github.com/npm/cli/issues/2701#issuecomment-972900511'
  //   ],
  //   reporter: '<EMAIL>',
  //   seenInTheWild: false,
  //   seqUpdate: 17229698780102,
  //   softwareMixed: [
  //     {
  //       hardwareName: '',
  //       hardwareVendor: '',
  //       hardwareVersion: '',
  //       os: '',
  //       osVendor: '',
  //       osVersion: '',
  //       prepared: false,
  //       softwareFileName: '',
  //       softwareName: ['npm'],
  //       softwareVendor: '',
  //       softwareVersion: [''],
  //       softwareVersionOperator: ''
  //     }
  //   ],
  //   threats: [],
  //   threatsList: [],
  //   timeLineData: [],
  //   title: 'CVE-2021-43616',
  //   twitter: [
  //     {
  //       author: 'threatintelctr',
  //       author_photo:
  //         'https://pbs.twimg.com/profile_images/904224973987840000/dMy1x9Ho_400x400.jpg',
  //       link: 'https://twitter.com/threatintelctr/status/1582124841959727104',
  //       text: ' NEW: CVE-2021-43616  ** DISPUTED ** The npm ci command in npm 7.x and 8.x through 8.1.3 proceeds with an installation even if dependency information in package-lock.json differs from package.json. This behavior ... (click for more) Severity: CRITICAL https://t.co/7kgIYratlt'
  //     }
  //   ],
  //   type: 'cve'
  // });
  return api
    .post(`/software/vulnerability/cve/groupib`, {
      cve
    })
    .then(({ result }) => result);
}
