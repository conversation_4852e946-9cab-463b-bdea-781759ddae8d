import { createContext, useContext } from 'react';
import { Outlet } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';

const VulnerabilityLayoutContext = createContext();

export default function VulnerabilityLayout() {
  return (
    <VulnerabilityLayoutContext.Provider>
      <div className="h-full flex flex-col">
        <PageHeading icon="vulnerability" title="Vulnerability" />
        <div className="flex-1 min-h-0 flex flex-col">
          <PermissionChecker permission={constants.View_Vulnerability} redirect>
            <Outlet />
          </PermissionChecker>
        </div>
      </div>
    </VulnerabilityLayoutContext.Provider>
  );
}

export function useVulnerabilityLayout() {
  return useContext(VulnerabilityLayoutContext);
}
