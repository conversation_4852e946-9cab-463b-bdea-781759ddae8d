import { CrudProvider } from '@/src/hooks/crud';
import UniqBy from 'lodash/uniqBy';
import { <PERSON><PERSON>, <PERSON><PERSON>, Divider } from 'antd';
import { Fragment, useState } from 'react';
import {
  getAllVulnerabilityApi,
  getCVEbyCVEIds,
  vulnerabilityScanApi,
  getVulnerabilityByNetworkDevice
} from '../api/vulnerability-list';
import VulnerabilityDetail from '../components/VulnerabilityDetail';
import SeverityBorder from '@/src/components/SeverityBorder';
import Severity from '@/src/components/Severity';
import Status from '@/src/components/Status';
import Score, { VulnerabilityScore } from '@/src/components/Score';
import { useLayout } from '@/src/layouts/Layout';
import ExceptionModel from '../components/ExceptionModel';
import { createVulnerabilityExceptionApi } from '../api/vulnerability-exception';
import { networkDeviceVulnerabilityScanApi } from '@modules/inventory/api/network-devices';
import { AffectedEndpointsDrawer } from '../components/AffectedEndpointsDrawer';
import { AffectedPackagesDrawer } from '../components/AffectedPackagesDrawer';
import GroupIBDrawer from '../components/GroupIBDrawer';
import { getVulnerabilitiesForAssetApi } from '../../inventory/api/hosts';
import VulnerabilityWidgets from '../components/VulnerabilityWidgets';
import FiltersProvider from '../components/filters/Filters';
import FilterContainer from '../components/filters/FilterContainer';

export default function List({ filters, hideOverview, zeroDayVulnerability, parentPage }) {
  const [assetsForVulnerability, setShowAssetForVulnerability] = useState(null);
  const [packagesForVulnerability, setShowPackagesForVulnerability] = useState(null);
  const [groupIBItem, setViewGroupIB] = useState(null);
  const [currentCategory, setCurrentCategory] = useState(
    parentPage && parentPage === 'network' ? 'network' : 'software'
  );
  const [selectedItems, setSelectedItems] = useState([]);
  const [isModalopen, setIsModalopen] = useState(false);
  const [vulnerability, setvulnerability] = useState({});
  const [updateKey, setUpdateKey] = useState(0); // Key to force re-renders
  const [appliedFilters, setCurrentFilters] = useState(null);
  const layout = useLayout();
  function onVulnerabilityExceptionAdd(data) {
    return createVulnerabilityExceptionApi({
      ...data,
      cve: selectedItems.map((id) => vulnerability[id]).filter(Boolean)
    }).then(() => {
      setIsModalopen(false);
      setUpdateKey((prevKey) => prevKey + 1);

      return layout.message.success(`Vulnerability Exception Added.`);
    });
  }

  function triggerScan() {
    if (currentCategory === 'network') {
      return networkDeviceVulnerabilityScanApi(filters?.id).then((res) => {
        return layout.message.success(`Vulnerability Scan has been initiated.`);
      });
    }
    return vulnerabilityScanApi(filters?.id).then((res) => {
      return layout.message.success(`Vulnerability Scan has been initiated.`);
    });
  }
  const columns = [
    // {
    //   title: 'Name',
    //   key: 'name',
    //   dataIndex: 'name'
    // },
    // {
    //   title: 'Version',
    //   key: 'version',
    //   dataIndex: 'version'
    // },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      render({ record }) {
        return <Severity severity={record.severity} useTag />;
      }
    },
    {
      title: 'CVE',
      dataIndex: 'cve',
      key: 'cve',
      type: 'view_link'
      // render({ record, view }) {
      //   return (
      //     <div className="flex min-w-0 text-ellipsis items-center">
      //       <Button title="Group IB" type="link" onClick={() => setViewGroupIB(record)}>
      //         <ExportOutlined />
      //       </Button>
      //       <Button type="link" onClick={() => view(record)} className="text-ellipsis ">
      //         {record.cve}
      //       </Button>
      //     </div>
      //   );
      // }
    },
    {
      title: 'EPSS',
      dataIndex: 'epss_probability',
      key: 'epss_probability',
      sortable: true,
      align: 'center',
      render({ record }) {
        if (!record.epss_probability) {
          return null;
        }
        return (
          <div className="flex items-center justify-center">
            <Score size={50} category="epss" value={record.epss_probability} useCircle />
          </div>
        );
      }
    },
    {
      title: 'Exploitable',
      dataIndex: 'cisa_known_exploit',
      key: 'cisa_known_exploit',
      sortable: true,
      align: 'center',
      render({ record }) {
        return (
          <div className="flex items-center justify-center">
            <Status
              useTag
              ignoreMinWidth
              status={record.cisa_known_exploit}
              color={record.cisa_known_exploit === 'no' ? 'success' : 'error'}
            />
          </div>
        );
      }
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      sortable: false
    },
    {
      title: 'ZiroScore',
      dataIndex: 'score',
      key: 'score',
      align: 'center',
      width: 150,
      render({ record }) {
        return <VulnerabilityScore score={record.score} isPercentage />;
      }
    },
    {
      title: 'CVSS3 Base Score',
      dataIndex: 'cvss3_base_score',
      key: 'cvss3_base_score',
      align: 'center',
      width: 150,
      render({ record }) {
        return <VulnerabilityScore score={record.cvss3_base_score} />;
      }
    },
    {
      title: 'CVSS2 Base Score',
      dataIndex: 'cvss2_base_score',
      key: 'cvss2_base_score',
      align: 'center',
      width: 150,
      render({ record }) {
        return <VulnerabilityScore score={record.cvss2_base_score} />;
      }
    },
    ...(currentCategory === 'software'
      ? filters?.id
        ? []
        : [
            {
              title: 'Endpoints',
              dataIndex: 'asset_count',
              key: 'asset_count',
              render({ record }) {
                return (
                  <Button type="link" onClick={() => setShowAssetForVulnerability(record)}>
                    {record.asset_count}
                  </Button>
                );
              },
              align: 'center'
            }
          ]
      : parentPage === 'network'
      ? []
      : [
          {
            title: 'Devices',
            dataIndex: 'device_count',
            key: 'device_count',
            render({ record }) {
              return (
                <Button type="link" onClick={() => setShowAssetForVulnerability(record)}>
                  {record.device_count}
                </Button>
              );
            },
            align: 'center'
          }
        ]),
    ...(currentCategory === 'software'
      ? [
          {
            title: 'Affected Softwares',
            dataIndex: 'software_count',
            key: 'software_count',
            render({ record }) {
              return (
                <Button type="link" onClick={() => setShowPackagesForVulnerability(record)}>
                  {record.software_count}
                </Button>
              );
            },
            align: 'center'
          }
        ]
      : []),
    // {
    //   title: 'CVSSv2',
    //   dataIndex: 'cvssv2',
    //   key: 'cvssv2'
    // },
    // {
    //   title: 'CVSSv3',
    //   dataIndex: 'cvssv3',
    //   key: 'cvssv3'
    // },
    {
      title: 'Published',
      dataIndex: 'published_date',
      key: 'published_date',
      type: 'datetime',
      sortable: false
    },

    {
      title: 'CVSS3 Impact Score',
      dataIndex: 'cvss3_impact_score',
      key: 'cvss3_impact_score',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Attack Complexity',
      dataIndex: 'cvss3_attack_complexity',
      key: 'cvss3_attack_complexity',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Scope',
      dataIndex: 'cvss3_scope',
      key: 'cvss3_scope',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Confidentiality Impact',
      dataIndex: 'cvss3_confidentiality_impact',
      key: 'cvss3_confidentiality_impact',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Availability Impact',
      dataIndex: 'cvss3_availability_impact',
      key: 'cvss3_availability_impact',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Attack Vector',
      dataIndex: 'cvss3_attack_vector',
      key: 'cvss3_attack_vector',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Integrity Impact',
      dataIndex: 'cvss3_integrity_impact',
      key: 'cvss3_integrity_impact',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Privileges Required',
      dataIndex: 'cvss3_privileges_required',
      key: 'cvss3_privileges_required',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Exploitability Score',
      dataIndex: 'cvss3_exploitability_score',
      key: 'cvss3_exploitability_score',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS3 Vector',
      dataIndex: 'cvss3_vector_string',
      key: 'cvss3_vector_string',
      hidden: true,
      sortable: false
    },
    {
      title: 'CWE',
      dataIndex: 'cwes',
      key: 'cwes',
      hidden: true,
      exportFormatter(record) {
        return UniqBy(record.cwes || [], 'cwe_id')
          .map((i) => i.cwe_id)
          .join(',');
      },
      render({ record }) {
        return (
          <>
            {UniqBy(record.cwes || [], 'cwe_id').map((i) => (
              <>
                <a
                  key={i.cwe_id}
                  target="_blank"
                  href={`https://cwe.mitre.org/data/definitions/${i.cwe_id.split('-')[1]}.html`}
                  rel="noreferrer">
                  {i.cwe_id}
                </a>{' '}
              </>
            ))}
          </>
        );
      }
    },
    {
      title: 'CVSS4 Vector',
      dataIndex: 'cvss40_source',
      key: 'cvss40_source',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS4 Type',
      dataIndex: 'cvss40_type',
      key: 'cvss40_type',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS4 Vector String',
      dataIndex: 'cvss40_vector_string',
      key: 'cvss40_vector_string',
      hidden: true,
      sortable: false
    },
    {
      title: 'CVSS4 Base Score',
      dataIndex: 'cvss40_base_score',
      key: 'cvss40_base_score',
      hidden: true,
      sortable: false,
      render({ record }) {
        return <VulnerabilityScore score={record.cvss40_base_score} />;
      }
    },
    {
      title: 'CVSS4 Base Severity',
      dataIndex: 'cvss40_base_severity',
      key: 'cvss40_base_severity',
      hidden: true,
      sortable: false,
      render({ record }) {
        return <Severity severity={record.cvss40_base_severity} useTag />;
      }
    },
    {
      title: 'CVSS4 Threat Score',
      dataIndex: 'cvss40_threat_score',
      key: 'cvss40_threat_score',
      hidden: true,
      sortable: false,
      render({ record }) {
        return <VulnerabilityScore score={record.cvss40_threat_score} />;
      }
    },
    {
      title: 'CVSS4 Threat Severity',
      dataIndex: 'cvss40_threat_severity',
      key: 'cvss40_threat_severity',
      hidden: true,
      sortable: false,
      render({ record }) {
        return <Severity severity={record.cvss40_threat_severity} useTag />;
      }
    },
    {
      title: 'CVSS4 Environmental Score',
      dataIndex: 'cvss40_environmental_score',
      key: 'cvss40_environmental_score',
      hidden: true,
      sortable: false,
      render({ record }) {
        return <VulnerabilityScore score={record.cvss40_environmental_score} />;
      }
    },
    {
      title: 'CVSS4 Environmental Severity',
      dataIndex: 'cvss40_environmental_severity',
      key: 'cvss40_environmental_severity',
      hidden: true,
      sortable: false,
      render({ record }) {
        return <Severity severity={record.cvss40_environmental_severity} useTag />;
      }
    }
  ];

  return (
    <Fragment>
      {parentPage ? null : (
        <div className="pr-2">
          <div className="bg-seperator py-2 pl-2 rounded-lg mb-2 flex justify-between min-w-0">
            <div className="flex-1 min-w-0">
              <Tabs
                activeKey={currentCategory}
                onChange={(key) => {
                  setCurrentFilters(null);
                  setCurrentCategory(key);
                }}
                items={[
                  { key: 'software', label: 'Endpoints Vulnerabilities' },
                  { key: 'network', label: 'Network Vulnerabilities' }
                ]}
                className="sticky-tabs transparent no-border no-margin"
              />
            </div>
          </div>
        </div>
      )}
      {hideOverview ? null : (
        <>
          <div style={{ height: '30%' }} className="flex flex-col">
            <VulnerabilityWidgets category={currentCategory} isZeroDay={zeroDayVulnerability} />
          </div>
          <Divider className="my-1" />
        </>
      )}
      <FiltersProvider
        key={currentCategory}
        disabledFilters={filters?.id ? ['platform'] : []}
        onReset={() => {
          setCurrentFilters(null);
          setUpdateKey((key) => key + 1);
        }}
        onChange={(filters) => {
          setCurrentFilters(filters);
          setUpdateKey((key) => key + 1);
        }}>
        <div className="flex-1 min-h-0 flex flex-col min-w-0">
          <CrudProvider
            columns={columns}
            key={`${updateKey}`}
            afterSearchSlot={() => (parentPage === 'patch' ? null : <FilterContainer />)}
            beforeCreateSlot={() => (
              <>
                {parentPage !== 'patch' ? (
                  <>
                    <Button
                      type="primary"
                      className="mr-2"
                      onClick={triggerScan}
                      iconPosition={'end'}>
                      Scan Now
                    </Button>

                    <Button
                      type="primary"
                      className="mr-2"
                      onClick={() => {
                        if (selectedItems?.length) {
                          setIsModalopen(true);
                        } else {
                          layout.message.error(
                            `Please Select One or more Vulnerability to add Exception`
                          );
                        }
                      }}>
                      Add Exceptions
                    </Button>
                  </>
                ) : null}
              </>
            )}
            defaultPageSize={30}
            resourceTitle={`Vulnerability${
              parentPage === 'network'
                ? ' for network device'
                : parentPage === 'patch'
                ? ' for patch'
                : ''
            }`}
            hasSearch
            formDrawerWidth={'95%'}
            onChange={setSelectedItems}
            allowSelection={parentPage !== 'patch'}
            selectedItems={selectedItems}
            fetchFn={(...args) => {
              if (parentPage === 'patch') {
                return getCVEbyCVEIds(...args, filters);
              } else if (parentPage === 'network') {
                return getVulnerabilityByNetworkDevice(...args, {
                  deviceId: filters?.id,
                  filters: appliedFilters
                }).then((data) => {
                  setvulnerability(() =>
                    (data.result || []).reduce(
                      (acc, v) => ({
                        ...acc,
                        [v.id]: v.cve
                      }),
                      {}
                    )
                  );
                  return data;
                });
              } else if (filters?.id) {
                return getVulnerabilitiesForAssetApi(...args, {
                  endpointId: filters?.id,
                  filters: appliedFilters,
                  zeroDayVulnerability
                }).then((data) => {
                  setvulnerability(() =>
                    (data.result || []).reduce(
                      (acc, v) => ({
                        ...acc,
                        [v.id]: v.cve
                      }),
                      {}
                    )
                  );
                  return data;
                });
              } else {
                return getAllVulnerabilityApi(...args, {
                  category: currentCategory,
                  filters: appliedFilters,
                  zeroDayVulnerability
                }).then((data) => {
                  setvulnerability(() =>
                    (data.result || []).reduce(
                      (acc, v) => ({
                        ...acc,
                        [v.id]: v.cve
                      }),
                      {}
                    )
                  );
                  return data;
                });
              }
            }}
            drawerTitle={(item) => (
              <SeverityBorder severity={item.severity || item.cvss3_base_severity}>
                <div className="flex justify-between">
                  <div>{item.cve}</div>
                </div>
              </SeverityBorder>
            )}
            formFields={(item) => (
              <VulnerabilityDetail category={currentCategory} vulnerability={item} />
            )}
            className={parentPage === 'patch' ? 'px-0' : ''}
          />
        </div>
      </FiltersProvider>

      <AffectedEndpointsDrawer
        currentCategory={currentCategory}
        vulnerability={assetsForVulnerability}
        onClose={() => setShowAssetForVulnerability(null)}
      />
      {packagesForVulnerability?.id ? (
        <AffectedPackagesDrawer
          currentCategory={currentCategory}
          vulnerability={packagesForVulnerability}
          endpointId={filters?.id}
          onClose={() => setShowPackagesForVulnerability(null)}
        />
      ) : null}

      {groupIBItem ? (
        <GroupIBDrawer onClose={() => setViewGroupIB(null)} vulnerability={groupIBItem} />
      ) : null}

      {isModalopen && (
        <ExceptionModel
          open={true}
          category={currentCategory}
          onAdd={onVulnerabilityExceptionAdd}
          onCancel={() => {
            setIsModalopen(false);
          }}
        />
      )}
    </Fragment>
  );
}
