// import { Fragment, useState } from 'react';
import { useState } from 'react';
import { But<PERSON>, Drawer } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { User } from '@/src/components/pickers/UserPicker';
import { Asset } from '@/src/components/pickers/AssetPicker';
import { EyeOutlined } from '@ant-design/icons';
import PageHeading from '@/src/components/PageHeading';
import {
  deleteVulnerabilityExceptionApi,
  getAllVulnerabilityExceptionApi
} from '../api/vulnerability-exception';
import constants from '@/src/constants/index';
import VulnerabilityDetail from '../components/VulnerabilityDetail';
import SeverityBorder from '@/src/components/SeverityBorder';
import PermissionChecker from '@/src/components/PermissionChecker';
import { ExceptionForm } from '../components/ExceptionModel';

export default function VulnerabilityExceptions() {
  const [vulnerabilityItem, setVulenrabilityItem] = useState(null);
  const columns = [
    {
      title: 'CVE',
      dataIndex: 'cve',
      key: 'cve',
      render({ record }) {
        return (
          <Button type="link" onClick={() => setVulenrabilityItem({ cve: record.cve })}>
            {record.cve}
          </Button>
        );
      }
    },
    {
      title: 'Exception Type',
      dataIndex: 'exception_type',
      key: 'exception_type'
    },
    {
      title: 'Reason For Exclusion',
      dataIndex: 'reason_for_exclusion',
      key: 'reason_for_exclusion'
    },
    // {
    //   title: 'Endpoint',
    //   dataIndex: 'assets',
    //   key: 'assets',
    //   width: '20%',
    //   render({ record }) {
    //     return <Asset.Picker value={record?.scope?.assets?.[0] || []} disabled textOnly />;
    //   }
    // },
    {
      title: 'Created By',
      dataIndex: 'created_by',
      key: 'created_by',
      sortable: false,
      render({ record }) {
        return <User.Picker textOnly value={record.created_by} disabled />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'created_time',
      key: 'created_time',
      type: 'datetime',
      hidden: true
    },

    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      buttons: ['delete'],
      deletePermissions: [constants.View_Manage_Exceptions],
      prependAction({ record, view }) {
        return (
          <PermissionChecker permission={constants.View_Manage_Exceptions}>
            <Button type="link" onClick={() => view(record)} className="mr-2">
              <EyeOutlined style={{ fontSize: '1.1rem' }} />
            </Button>
          </PermissionChecker>
        );
      }
    }
  ];
  return (
    <>
      <Asset.Provider>
        <User.Provider>
          <PageHeading icon="vulnerability" title={`Manage Exceptions`} />
          <CrudProvider
            columns={columns}
            defaultFormItem={{}}
            resourceTitle="Manage Exceptions"
            hasSearch
            fetchFn={getAllVulnerabilityExceptionApi}
            deleteFn={deleteVulnerabilityExceptionApi}
            formFields={() => <ExceptionForm disabled={true} />}
          />
          {vulnerabilityItem ? (
            <Drawer
              width="95%"
              onClose={() => setVulenrabilityItem(null)}
              destroyOnClose
              maskClosable={false}
              open={true}
              title={
                <SeverityBorder severity={vulnerabilityItem.severity}>
                  <div className="flex justify-between">
                    <div>{vulnerabilityItem.cve}</div>
                  </div>
                </SeverityBorder>
              }>
              <VulnerabilityDetail
                onReceived={(v) => setVulenrabilityItem(v)}
                vulnerability={vulnerabilityItem}
              />
            </Drawer>
          ) : null}
        </User.Provider>
      </Asset.Provider>
    </>
  );
}
