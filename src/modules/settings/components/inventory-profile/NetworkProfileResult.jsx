import { Tag } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { getNetworkProfileResultApi } from '../../api/inventory-profile/network-profile';
import Status from '@/src/components/Status';

export default function NetworkProfileResult({ id }) {
  const columns = [
    {
      title: 'Host',
      dataIndex: 'host',
      key: 'host',
      ellipsis: true
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render({ record }) {
        return <Status status={record.status} useTag />;
      },
      width: '50px',
      align: 'center'
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    },
    {
      title: 'Credential Profile',
      dataIndex: 'credential',
      key: 'credential',
      width: '80px',
      align: 'center',
      render({ record }) {
        return record.credential ? (
          <Tag color="processing" className="uppercase">
            {record.credential}
          </Tag>
        ) : null;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    }
  ];

  return (
    <CrudProvider
      columns={columns}
      resourceTitle="Network Profile Result"
      hasSearch
      disableColumnSelection
      fetchFn={(...args) => getNetworkProfileResultApi(id, ...args)}
    />
  );
}
