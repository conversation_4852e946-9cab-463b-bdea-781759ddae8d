import { CredentialProfile } from '@/src/components/pickers/CredentialProfilePicker';
import { Row, Col, Form, Input, Select, InputNumber, Radio } from 'antd';
import { Organization } from '@/src/components/pickers/OrganizationPicker';
import { Department } from '@/src/components/pickers/DepartmentPicker';

export default function NetworkProfileForm() {
  const ip_type_options = [
    { value: 'IP', label: 'IP' },
    { value: 'IP-RANGE', label: 'IP Range' },
    { value: 'CIDR', label: 'CIDR' }
  ];
  let form = Form.useFormInstance();

  return (
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item label="Name" name="name" rules={[{ required: true }]}>
          <Input placeholder="Name" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="Network Profile Type" name="discovery_type" rules={[{ required: true }]}>
          <Select
            placeholder="Network Profile Type"
            options={[{ label: 'SNMP', value: 'SNMP' }]}
            disabled
          />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="Description" name="description">
          <Input.TextArea placeholder="Description" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="Organization" name="organization" rules={[{ required: true }]}>
          <Organization.Picker />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="Department" name="department" rules={[{ required: true }]}>
          <Department.Picker organization={form.getFieldValue('organization')} />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="IP Type" name="ip_type" rules={[{ required: true }]}>
          <Radio.Group
            size="medium"
            options={ip_type_options}
            optionType="button"
            buttonStyle="solid"
          />
        </Form.Item>
      </Col>

      {form.getFieldValue('ip_type') ? (
        form.getFieldValue('ip_type') === 'IP' ? (
          <>
            <Col span={12}>
              <Form.Item label="IP" name="host" rules={[{ required: true }]}>
                <Input placeholder="IP ex. ***********" />
              </Form.Item>
            </Col>
            {/* <Col span={12}>
              <Form.Item label="Subnet" name="subnet" rules={[{ required: true }]}>
                <Input placeholder="Subnet ex. ***********/32" />
              </Form.Item>
            </Col> */}
          </>
        ) : form.getFieldValue('ip_type') === 'CIDR' ? (
          <>
            <Col span={12}>
              <Form.Item label="CIDR" name="cidr" rules={[{ required: true }]}>
                <Input placeholder="CIDR ex. ***********/24" />
              </Form.Item>
            </Col>
          </>
        ) : (
          <>
            <Col span={12}>
              <Form.Item label="IP Range" name="range" rules={[{ required: true }]}>
                <Input placeholder="IP Range ex. ***********-255" />
              </Form.Item>
            </Col>
          </>
        )
      ) : null}
      <Col span={12}>
        <Form.Item
          label="Credential Profiles"
          name="credential_profile"
          rules={[{ required: true }]}>
          <CredentialProfile.Picker mode="multiple" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="Port" name="port" rules={[{ required: true }]}>
          <InputNumber placeholder="Port" className="w-full" precision={0} max={65536} min={1} />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="Timeout" name="timeout" rules={[{ required: true }]}>
          <Input placeholder="Timeout" type="number" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="Retry Count" name="retryCount" rules={[{ required: true }]}>
          <InputNumber placeholder="Retry Count" precision={0} className="w-full" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          label="Polling Interval (Seconds)"
          name="polling_interval"
          rules={[{ required: true }]}>
          <InputNumber placeholder="Polling Interval" className="w-full" precision={0} min={60} />
        </Form.Item>
      </Col>
    </Row>
  );
}
