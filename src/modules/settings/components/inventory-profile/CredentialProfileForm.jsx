import { Row, Col, Form, Input, Select } from 'antd';

export default function CredentialProfileForm() {
  const snmp_version_options = [
    { value: 'v1', label: 'V1' },
    { value: 'v2c', label: 'V2C' },
    { value: 'v3', label: 'V3' }
  ];
  const security_level_options = [
    { value: 'no_auth_no_privacy', label: 'No Authentication No Privacy' },
    { value: 'auth_no_privacy', label: 'Authentication No Privacy' },
    { value: 'auth_privacy', label: 'Authentication Privacy' }
  ];
  const auth_protocol_options = [
    {
      value: 'MD5',
      label: 'MD5'
    },
    {
      value: 'SHA',
      label: 'SHA'
    },
    {
      value: 'SHA224',
      label: 'SHA224'
    },
    {
      value: 'SHA256',
      label: 'SHA256'
    },
    {
      value: 'SHA384',
      label: 'SHA384'
    },
    {
      value: 'SHA512',
      label: 'SHA512'
    }
  ];

  const privacy_protocol_options = [
    {
      value: 'DES',
      label: 'DES'
    },
    {
      value: 'AES',
      label: 'AES'
    },
    {
      value: 'AES192C',
      label: 'AES192C'
    },
    {
      value: 'AES192',
      label: 'AES192'
    },
    {
      value: 'AES256C',
      label: 'AES256C'
    },
    {
      value: 'AES256',
      label: 'AES256'
    }
  ];

  let form = Form.useFormInstance();

  return (
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item label="Name" name="name" rules={[{ required: true }]}>
          <Input placeholder="Name" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          label="Credential Profile Type"
          name="credential_type"
          rules={[{ required: true }]}>
          <Select
            placeholder="Credential Profile Type"
            options={[{ label: 'SNMP', value: 'SNMP' }]}
            disabled
          />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="Description" name="description">
          <Input.TextArea placeholder="Description" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="SNMP Version" name="snmp_version" rules={[{ required: true }]}>
          <Select placeholder="Select SNMP Version" options={snmp_version_options} />
        </Form.Item>
      </Col>
      {['v1', 'v2c'].includes(form.getFieldValue('snmp_version')) ? (
        <Col span={12}>
          <Form.Item label="Community" name="snmp_community" rules={[{ required: true }]}>
            <Input placeholder="Community" />
          </Form.Item>
        </Col>
      ) : ['v3'].includes(form.getFieldValue('snmp_version')) ? (
        <>
          <Col span={12}>
            <Form.Item label="Username" name="snmp_security_username" rules={[{ required: true }]}>
              <Input placeholder="Username" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Security Level"
              name="snmp_security_level"
              rules={[{ required: true }]}>
              <Select placeholder="Security Level" options={security_level_options} />
            </Form.Item>
          </Col>
          <Col span={12} />
          {['auth_no_privacy', 'auth_privacy'].includes(
            form.getFieldValue('snmp_security_level')
          ) ? (
            <>
              <Col span={12}>
                <Form.Item
                  label="Authentication Protocol"
                  name="snmp_authentication_protocol"
                  rules={[{ required: true }]}>
                  <Select placeholder="Security Level" options={auth_protocol_options} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Authentication Password"
                  name="snmp_authentication_password"
                  rules={[{ required: true }]}>
                  <Input type="password" placeholder="Password" />
                </Form.Item>
              </Col>
            </>
          ) : null}
          {['auth_privacy'].includes(form.getFieldValue('snmp_security_level')) ? (
            <>
              <Col span={12}>
                <Form.Item
                  label="Privacy Protocol"
                  name="snmp_privacy_protocol"
                  rules={[{ required: true }]}>
                  <Select placeholder="Privacy Protocol" options={privacy_protocol_options} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Privacy Password"
                  name="snmp_private_password"
                  rules={[{ required: true }]}>
                  <Input type="password" placeholder="Password" />
                </Form.Item>
              </Col>
            </>
          ) : null}
        </>
      ) : null}
    </Row>
  );
}
