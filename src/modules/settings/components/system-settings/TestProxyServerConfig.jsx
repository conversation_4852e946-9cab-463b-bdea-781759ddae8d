import { Modal, Form, Input } from 'antd';

export default function TestProxyServerConfig({ open, onCreate, onCancel }) {
  const [form] = Form.useForm();
  return (
    <Modal
      open={open}
      title="Test Proxy Server Configuration"
      okText="Test"
      cancelText="Cancel"
      onCancel={onCancel}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            form.resetFields();
            onCreate(values);
          })
          .catch((info) => {
            console.log('Validate Failed:', info);
          });
      }}>
      <Form form={form} layout="vertical" name="form_in_modal">
        <Form.Item name="target" label="Target URL" rules={[{ required: true }]}>
          <Input placeholder="Enter URL" />
        </Form.Item>
      </Form>
    </Modal>
  );
}
