import Uploader from '@/src/components/Uploader';
import { Form, Row, Col, Input, Button, Radio } from 'antd';
import { useState } from 'react';
import {
  addMaliciousHashApi,
  reloadMaliciousHashApi
} from '../../api/system-settings/threat-database';
import { useLayout } from '@/src/layouts/Layout';

export default function MaliciousHashForm() {
  const [processing, setProcessing] = useState(false);
  const [isReloading, setIsReloading] = useState(false);
  const [type, setType] = useState('single_hash');
  const [form] = Form.useForm();
  const [operationStatus, setOperationStatus] = useState({});
  const layout = useLayout();

  const handleReload = () => {
    setIsReloading(true);
    reloadMaliciousHashApi()
      .then(({ result }) => {
        layout.notification.success({
          message: 'Success!',
          description: result
        });
      })
      .finally(() => {
        setIsReloading(false);
      });
  };

  const submitForm = (values) => {
    setProcessing(true);
    addMaliciousHashApi(values)
      .then(({ result }) => {
        form.resetFields();
        setOperationStatus({
          status: 'success',
          message: result
        });
        setProcessing(false);
      })
      .catch((e) => {
        setOperationStatus({
          status: 'error',
          message: e.response.data['response-message']
        });
        setProcessing(false);
      });
  };

  return (
    <div className="flex flex-col min-h-0">
      <Form
        layout="vertical"
        form={form}
        className="h-full"
        onFinish={submitForm}
        onValuesChange={(values) => setType(values.type)}
        initialValues={{ type: 'single_hash' }}>
        <Row gutter={32}>
          <Col span={24}>
            <Form.Item name="type" label="">
              <Radio.Group
                options={[
                  { label: 'Add Hash', value: 'single_hash' },
                  { label: 'Upload CSV File', value: 'csv_file' }
                ]}
              />
            </Form.Item>
          </Col>
          {(form.getFieldValue('type') || type) === 'single_hash' ? (
            <>
              <Col span={12}>
                <Form.Item label="Hash Name" name="hash_name" rules={[{ required: true }]}>
                  <Input placeholder="Ex. VT1_exe.exe malicious process" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Hash" name="hash" rules={[{ required: true }]}>
                  <Input placeholder="Ex. f4f58f34e4cda8f5d440f533e50e7542" />
                </Form.Item>
              </Col>
            </>
          ) : (
            <Col span={24}>
              <div>
                <h4>Download Sample CSV</h4>
                <a href="/sample_hash.csv">sample_hash.csv</a>
              </div>
              <Form.Item
                required={false}
                label=" "
                name="csv_file"
                className="flex-1"
                rules={[{ required: true }]}>
                <Uploader
                  message="Please select CSV file containing Malicious Hashes"
                  accept="text/csv"
                />
              </Form.Item>
            </Col>
          )}
          {operationStatus.status ? (
            <Col span={24}>
              <div className={operationStatus.status === 'error' ? 'text-danger' : 'text-success'}>
                {operationStatus.message}
              </div>
            </Col>
          ) : null}
          <Col span={24} className="text-left">
            <Button type="primary" loading={isReloading} className="mr-2" onClick={handleReload}>
              Reload
            </Button>
            <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
              {(form.getFieldValue('type') || type) === 'single_hash' ? 'Add Hash' : 'Upload'}
            </Button>
            <Button type="primary" ghost htmlType="reset">
              Reset
            </Button>
          </Col>
        </Row>
      </Form>
    </div>
  );
}
