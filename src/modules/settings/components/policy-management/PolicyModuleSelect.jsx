import { Select, Form } from 'antd';

export default function PolicyModuleSelect({ ...props }) {
  const policyModuleOptions = [
    { label: 'Endpoint', value: 'Endpoint' },
    { value: 'vulnerability', label: 'Vulnerability' },
    { label: 'FIM', value: 'FIM' },
    { value: 'compliance', label: 'Compliance', disabled: true }
  ];

  const form = Form.useFormInstance();

  return (
    <Form.Item label="Module" name="module" rules={[{ required: true }]} {...props}>
      <Select
        placeholder="Module"
        options={policyModuleOptions}
        onChange={(moduleName) => {
          form.setFieldValue('context', [{ join: 'and' }]);
          form.setFieldValue('module', moduleName);
        }}
      />
    </Form.Item>
  );
}
