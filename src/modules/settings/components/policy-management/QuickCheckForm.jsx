import AiScriptGenerator from '@/src/components/ai-script-generator/Container';
import Icon from '@/src/components/Icon';
import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';
import { Configuration } from '@/src/components/pickers/ConfigurationPicker';
import { IntegrationAction } from '@/src/components/pickers/IntegrationActionPicker';
import { Severity } from '@/src/components/pickers/SeverityPicker';
import { useTestResourceCreation } from '@/src/components/TestResourceCreationTask';
import ComplianceRuleOutputConditions from '@/src/modules/compliance/components/ComplianceRuleOutputConditions';
import { Form, Input, Row, Col, Switch, Select } from 'antd';
// import TestResourceCreationTask from '@/src/components/TestResourceCreationTask';

export default function QuickCheckForm({ value }) {
  const form = Form.useFormInstance();

  const commandTypeOptions = [
    { label: 'Cmd', value: 'cmd' },
    { label: 'Powershell', value: 'ps' }
  ];

  const testResourceContext = useTestResourceCreation();

  return (
    <>
      <Row gutter={32}>
        <Col span={24}>
          <Form.Item label="Name" name="name" rules={[{ required: true }]}>
            <Input placeholder="Name" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Description" name="description" rules={[{ required: true }]}>
            <Input.TextArea placeholder="Description" rows={5} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Severity" name="severity" rules={[{ required: true }]}>
            <Severity.Picker placeholder="Severity" />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="Raise Alert" valuePropName="checked" name="raise_alert">
            <Switch />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="Enabled" valuePropName="checked" name="status">
            <Switch />
          </Form.Item>
        </Col>
        {form.getFieldValue('raise_alert') ? (
          <>
            <Col span={24}>
              <Form.Item label="Add Actions" name="actions">
                <IntegrationAction.Picker
                  mode="multiple"
                  excludedOptions={['ThreatIntelligence', 'AI']}
                />
              </Form.Item>
            </Col>
          </>
        ) : null}
      </Row>
      <AssetScopePicker
        label="Scope"
        gutter={16}
        skipProvider={true}
        name={['scope', 'assetFilter']}
        subname={['scope', 'assets']}
      />
      <Row>
        <Col span={12}>
          <Form.Item label="Command Type" name="command_type" rules={[{ required: true }]}>
            <Select
              options={commandTypeOptions}
              placeholder="Command Type"
              onChange={(v) => {
                form.setFieldValue('command_type', v);
                form.setFieldValue('command', undefined);
                form.setFieldValue('expected_output', [
                  {
                    condition: 'and',
                    operator: 'Equal'
                  }
                ]);
              }}
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <div className="flex flex-1">
            <Form.Item
              name="command"
              className="flex-1"
              label={'Command'}
              rules={[{ required: true }]}>
              <Input.TextArea rows={3} placeholder={'Command'} />
            </Form.Item>
            <Form.Item name="command" label=" " className="inline-flex ml-2">
              <AiScriptGenerator
                type="command"
                platform={form.getFieldValue('os')}
                script_type={
                  form.getFieldValue('os') === 'windows'
                    ? form.getFieldValue('command_type') === 'ps'
                      ? 'powershell'
                      : 'batch'
                    : 'bash'
                }>
                <Icon name="ai" className="text-lg" />
              </AiScriptGenerator>
            </Form.Item>
          </div>
        </Col>
        <Col span={12}>{testResourceContext.testControl()}</Col>
        <Col span={24}>
          <ComplianceRuleOutputConditions name={['expectedOutput']} />
        </Col>
      </Row>

      {/* <TestResourceCreationTask testApiFn={testQuickCheck} /> */}

      {form.getFieldValue('raise_alert') ? (
        <Row>
          <Col span={24}>
            <Form.Item label="Add Remidiations" name="remediations" valuePropName={'targetKeys'}>
              <Configuration.Picker mode="multiple" />
            </Form.Item>
          </Col>
        </Row>
      ) : null}
      {testResourceContext.result()}
    </>
  );
}
