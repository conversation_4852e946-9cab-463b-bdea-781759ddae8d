import { Row, Col, Form, Input, Switch } from 'antd';
import PolicyModuleSelect from './PolicyModuleSelect';
import { Severity } from '@/src/components/pickers/SeverityPicker';
import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';
import AttributeCondition from './AttributeCondition';
import { IntegrationAction } from '@/src/components/pickers/IntegrationActionPicker';
import { Configuration } from '@/src/components/pickers/ConfigurationPicker';

export default function PolicyForm() {
  return (
    <div className="flex flex-col min-h-0 overflow-auto overflow-x-hidden">
      <Row gutter={32}>
        <Col span={24}>
          <Form.Item label="Name" name="name" rules={[{ required: true }]}>
            <Input placeholder="Name" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Description" name="description" rules={[{ required: true }]}>
            <Input.TextArea placeholder="Description" rows={5} />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="Status" name="status" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Col>
        <Col span={12}>
          <PolicyModuleSelect />
        </Col>
        <Col span={12}>
          <Form.Item label="Severity" name="severity" rules={[{ required: true }]}>
            <Severity.Picker placeholder="Severity" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <AssetScopePicker
            label="Scope"
            gutter={16}
            skipProvider={true}
            name={['scope', 'assetFilter']}
            subname={['scope', 'assets']}
          />
        </Col>
      </Row>
      <Form.Item label="Conditions" rules={[{ required: true }]}>
        <AttributeCondition name={'context'} />
      </Form.Item>
      <Row gutter={32}>
        <Col span={24}>
          <Form.Item label="Add Actions" name="actions">
            <IntegrationAction.Picker
              mode="multiple"
              excludedOptions={['ThreatIntelligence', 'AI']}
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Add Remidiations" name="remediations" valuePropName={'targetKeys'}>
            <Configuration.Picker mode="multiple" />
          </Form.Item>
        </Col>
      </Row>
    </div>
  );
}
