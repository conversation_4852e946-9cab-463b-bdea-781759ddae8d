import { Row, Col, Form, InputNumber, Select, Divider, Input, Button } from 'antd';
import { AssetPolicyAttributes } from '@/src/components/pickers/AssetPolicyAttributePicker';
import { VulnerabilityPolicyAttributes } from '@/src/components/pickers/VulnerabilityAttributePicker';
import { FimPolicyAttributes } from '@/src/components/pickers/FimPolicyAttributePicker';
import Repeater from '@/src/components/Repeater';
import Icon from '@/src/components/Icon';

export default function AttributeCondition({ name, disabled }) {
  const policyAttributeConditionOptions = [
    'Equal',
    'NotEqual',
    'GreaterThan',
    'GreaterThanEqual',
    'LessThan',
    'LessThanEqual',
    'Contains',
    'NotContains',
    'StartWith',
    'EndWith'
  ].map((i) => ({ value: i, label: i }));

  let stringOperators = ['Contains', 'NotContains', 'StartWith', 'EndWith'];

  let booleanOperators = ['IsExist'];

  const form = Form.useFormInstance();

  let ProviderToUse =
    form.getFieldValue('module') === 'Endpoint'
      ? AssetPolicyAttributes
      : form.getFieldValue('module') === 'FIM'
      ? FimPolicyAttributes
      : VulnerabilityPolicyAttributes;

  let { allOptions } = ProviderToUse.contextHook();

  let joinOptions = [
    {
      label: 'AND',
      value: 'and'
    },
    {
      label: 'OR',
      value: 'or'
    }
  ];

  return (
    <Repeater
      name={name}
      disabled={disabled}
      canAdd={form.getFieldValue('module') !== 'fim' ? false : true}
      defaultItem={{ attribute: undefined, condition: undefined, value: undefined, join: 'and' }}
      addBtnText={'Add Condition'}>
      {({ key, name: innerName, ...restField }, actions) => (
        <Row key={key}>
          <Col span={24}>
            <div className="flex flex-col">
              <div className="flex items-center">
                <div
                  className={`flex-1 mr-2 px-2 py-1 rounded border-solid border-border
       ${disabled ? '' : 'bg-border'}`}>
                  <Row gutter={32}>
                    <Col span={8}>
                      <Form.Item
                        label="Attribute"
                        dependencies={['module']}
                        name={[innerName, 'attribute']}
                        rules={[{ required: true }]}>
                        <ProviderToUse.Picker
                          onChange={(attribute) => {
                            form.setFieldValue([innerName, 'attribute'], attribute);
                            form.setFieldValue([innerName, 'condition'], undefined);
                            form.setFieldValue([innerName, 'value'], undefined);
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="Condition"
                        name={[innerName, 'condition']}
                        dependencies={['module']}
                        rules={[{ required: true }]}>
                        <Select
                          placeholder="Condition"
                          options={
                            (allOptions.get(form.getFieldValue([innerName, 'attribute'])) || {})
                              .dataType === 'boolean'
                              ? booleanOperators.map((i) => ({ value: i, label: i }))
                              : policyAttributeConditionOptions
                          }
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      {booleanOperators.includes(
                        form.getFieldValue([name, innerName, 'condition'])
                      ) ? null : (
                        <Form.Item
                          label="Value"
                          name={[innerName, 'value']}
                          dependencies={['module']}
                          rules={[{ required: true }]}>
                          {stringOperators.includes(
                            form.getFieldValue([name, innerName, 'condition'])
                          ) ? (
                            <Input placeholder="Value" />
                          ) : (
                            <InputNumber placeholder="Value" precision={0} className="w-full" />
                          )}
                        </Form.Item>
                      )}
                    </Col>
                  </Row>
                </div>
                <div className="flex-shrink-0 flex items-center">
                  {!disabled && (
                    <Button
                      shape="circle"
                      type="danger"
                      style={{ visibility: innerName === 0 ? 'hidden' : 'visible' }}
                      onClick={() => actions.remove(innerName)}>
                      <Icon name="close" className="text-danger" style={{ fontSize: '1.5rem' }} />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </Col>
          {(form.getFieldValue(name) || []).length > 1 &&
            innerName < (form.getFieldValue(name) || []).length - 1 && (
              <Col span={24}>
                <Divider>
                  <Form.Item
                    name={[innerName, 'join']}
                    dependencies={['module']}
                    style={{ margin: 0 }}
                    rules={[{ required: true }]}>
                    <Select options={joinOptions} />
                  </Form.Item>
                </Divider>
              </Col>
            )}
        </Row>
      )}
    </Repeater>
  );
}
