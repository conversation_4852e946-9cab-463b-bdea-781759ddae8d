import IntervalInput from '@/src/components/IntervalInput';
import Capitalize from 'lodash/capitalize';
import { Form, Input, Row, Col, Select, TimePicker } from 'antd';

import { refModelOptions } from '../../api/software-deployment/deployment-policy';

export default function DeployemntPolicyForm() {
  const policyTypeOptions = [
    { label: 'Instant', value: 'instant' },
    { label: 'Scheduled', value: 'schedule' }
  ];

  const initiationOptions = [
    { label: 'On System Start-up', value: 'on_system_start_up' },
    { label: 'On Next Cycle', value: 'on_next_cycle' },
    { label: 'Recurring', value: 'recurring' }
  ];

  const daysOptions = [
    { label: 'Monday', value: 'monday' },
    { label: 'Tuesday', value: 'tuesday' },
    { label: 'Wednesday', value: 'wednesday' },
    { label: 'Thursday', value: 'thursday' },
    { label: 'Friday', value: 'friday' },
    { label: 'Saturday', value: 'saturday' },
    { label: 'Sunday', value: 'sunday' }
  ];

  const restartTypeOptions = [
    { label: 'Warn Restart', value: 'warn_restart' },
    { label: 'No Restart', value: 'no_restart' },
    { label: 'Force Restart', value: 'force_restart' },
    { label: 'Prompt Restart', value: 'prompt_restart' }
  ];

  const unitOptions = ['hour', 'day', 'month', 'year'].map((i) => ({
    value: i,
    label: Capitalize(i)
  }));

  const form = Form.useFormInstance();

  return (
    <div className="flex flex-col min-h-0 h-full overflow-y-auto overflow-x-hidden">
      <Row gutter={32}>
        <Col span={24}>
          <Form.Item label="Policy Name" name="displayName" rules={[{ required: true }]}>
            <Input placeholder="displayName" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Description" name="description" rules={[{ required: true }]}>
            <Input.TextArea placeholder="Description" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Supported module" name="refModel" rules={[{ required: true }]}>
            <Select options={refModelOptions} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Type" name="type" rules={[{ required: true }]}>
            <Select options={policyTypeOptions} />
          </Form.Item>
        </Col>
        {form.getFieldValue('type') === 'schedule' ? (
          <Col span={12}>
            <Form.Item
              label="Initiate Deployment On"
              name="initiateDeploymentOn"
              rules={[{ required: true }]}>
              <Select options={initiationOptions} placeholder="Please Select" />
            </Form.Item>
          </Col>
        ) : null}
        {form.getFieldValue('type') === 'schedule' ? (
          <>
            {form.getFieldValue('initiateDeploymentOn') === 'recurring' ? (
              <Col span={12}>
                <Form.Item label="Interval" name="executionInterval">
                  <IntervalInput unitOptions={unitOptions} parentName="executionInterval" />
                </Form.Item>
              </Col>
            ) : (
              <>
                <Col span={12}>
                  <Form.Item
                    label="Deployment Day"
                    name="deploymentDays"
                    rules={[
                      { required: form.getFieldValue('initiateDeploymentOn') === 'on_next_cycle' }
                    ]}>
                    <Select
                      options={daysOptions}
                      mode="multiple"
                      disabled={form.getFieldValue('initiateDeploymentOn') !== 'on_next_cycle'}
                      placeholder="Please select"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Deployment Time From"
                    name="deploymentTimeFrom"
                    rules={[
                      { required: form.getFieldValue('initiateDeploymentOn') === 'on_next_cycle' }
                    ]}>
                    <TimePicker
                      className="w-full"
                      disabled={form.getFieldValue('initiateDeploymentOn') !== 'on_next_cycle'}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Deployment Time To"
                    name="deploymentTimeTo"
                    rules={[
                      { required: form.getFieldValue('initiateDeploymentOn') === 'on_next_cycle' }
                    ]}>
                    <TimePicker
                      className="w-full"
                      disabled={form.getFieldValue('initiateDeploymentOn') !== 'on_next_cycle'}
                    />
                  </Form.Item>
                </Col>
              </>
            )}
          </>
        ) : null}

        {['patch', 'all'].includes(form.getFieldValue('refModel')) ? (
          <Col span={12}>
            <Form.Item label="Restart Type" name="restartType" rules={[{ required: true }]}>
              <Select options={restartTypeOptions} placeholder="Please select" />
            </Form.Item>
          </Col>
        ) : null}
      </Row>
    </div>
  );
}
