import { useState } from 'react';
import { Form, Input, Col, Row, Radio } from 'antd';

import { ComputerGroups } from '@/src/components/pickers/ComputerGroupsPicker';
import PlatformPicker from '@/src/components/pickers/PlatformPicker';

import ApplicationPatches from '@/src/components/pickers/ApplicationPatches';

const PatchDeclinePolicyForm = ({ item = {} }) => {
  const form = Form.useFormInstance();

  const [updateKey, setUpdateKey] = useState(0); // Key to force re-renders

  return (
    <>
      <Row gutter={32}>
        <Col span={12}>
          <Form.Item label="Name" name="name" rules={[{ required: true }]}>
            <Input placeholder="Name" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Description" name="description" rules={[{ required: true }]}>
            <Input.TextArea placeholder="Description" rows={5} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="computer_filter_type" label="Computer Groups">
            <Radio.Group
              options={[
                { label: 'All Computers', value: 'all_cg' },
                { label: 'Specific Groups', value: 'specific_cg' }
              ]}
            />
          </Form.Item>
        </Col>

        {item.computer_filter_type !== 'all_cg' ? (
          <>
            <Col span={24}>
              <Form.Item label="Computer Groups" name="computer_group_ids">
                <ComputerGroups.Picker
                  mode="multiple"
                  value={item.computer_group_ids}
                  onChange={(computer_group_ids) => form.setFieldValue(item, computer_group_ids)}
                />
              </Form.Item>
            </Col>
          </>
        ) : null}

        <Col span={24}>
          <Form.Item label="OS Platform" name="platform" rules={[{ required: true }]}>
            <PlatformPicker
              disabled={item.id}
              onChange={(value) => {
                form.setFieldValue('platform', value);
                form.setFieldValue('application_patch_setting', []);
                setUpdateKey((prevKey) => prevKey + 1);
              }}
            />
          </Form.Item>
        </Col>

        <Col span={24}>
          <Form.Item
            label="Application Patches"
            name="application_patch_setting"
            rules={[{ required: true }]}>
            <ApplicationPatches platform={form.getFieldValue('platform')} key={updateKey} />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

export default PatchDeclinePolicyForm;
