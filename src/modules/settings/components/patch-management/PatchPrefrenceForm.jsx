import { Form, Row, Col, Radio } from 'antd';
import { PATCH_PREFRENCE_FORM_TYPE } from '../../api/patch-management/patch-prefrence';

export default function PatchPrefrenceForm({ formType }) {
  return (
    <div className="flex flex-col min-h-0 p-4">
      <Row gutter={32}>
        {formType === PATCH_PREFRENCE_FORM_TYPE.PATCH_APPROVAL_POLICY ? (
          <Col span={12}>
            <Form.Item
              name="patchApprovalPolicy"
              label="Patch Approval Policy
"
              rules={[{ required: true }]}>
              <Radio.Group
                options={[
                  { label: 'Pre Approved', value: 'pre_approved' },
                  { label: 'Manually Approve', value: 'manual_approved' },
                  { label: ' Test and Approve', value: 'test_and_approve' }
                ]}
              />
            </Form.Item>
          </Col>
        ) : formType === PATCH_PREFRENCE_FORM_TYPE.SYSTEM_HEALTH ? (
          <Col span={12}></Col>
        ) : formType === '' ? (
          <Col span={12}></Col>
        ) : null}
      </Row>
    </div>
  );
}
