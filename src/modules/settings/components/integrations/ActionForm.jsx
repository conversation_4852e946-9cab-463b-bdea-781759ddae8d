import RecipientsPicker from '@/src/components/pickers/RecipientsPicker';
import { Form, Input, Row, Col, Switch, Select, Divider } from 'antd';

export default function ActionForm({ item = {} }) {
  const policyAttributeConditionOptions = [
    'Equal',
    'NotEqual',
    'GreaterThan',
    'GreaterThanEqual',
    'LessThan',
    'LessThanEqual'
  ].map((i) => ({ value: i, label: i }));
  const typeOptions = [
    { value: 'Email', label: 'Email' },
    { value: 'Incident', label: 'Incident' },
    { value: 'ThreatIntelligence', label: 'Threat Intelligence' },
    { value: 'AI', label: 'Artificial Intelligence' }
  ];

  const threatIntelligenceTypeOptions = ['VirusTotal', 'AbuseIPDB', 'GroupIB'].map((i) => ({
    value: i,
    label: i
  }));

  // const incidentTypeOptions = ['Zendesk', 'Jira'].map((i) => ({ value: i, label: i }));
  const incidentTypeOptions = ['Zendesk'].map((i) => ({ value: i, label: i }));
  const aiTypeOptions = ['OpenAI', 'AzureOpenAI'].map((i) => ({ value: i, label: i }));
  const authTypeOptions = ['Basic', 'Token'].map((i) => ({ value: i, label: i }));

  const form = Form.useFormInstance();

  return (
    <div className="flex flex-col min-h-0 h-full overflow-y-auto overflow-x-hidden">
      <Row gutter={32}>
        <Col span={24}>
          <Form.Item label="Integration Name" name="name" rules={[{ required: true }]}>
            <Input placeholder="Integration Name" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Description" name="description" rules={[{ required: true }]}>
            <Input.TextArea placeholder="Description" />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="Integration Type" name="type">
            <Select
              options={typeOptions}
              onChange={(value) => {
                form.setFieldValue('type', value);
                form.setFieldValue('context', {
                  incidentType: 'Zendesk',
                  auth_type: 'Basic',
                  threat_intelligence_type: 'VirusTotal',
                  attribute: 'Malicious Count',
                  ai_type: 'OpenAI'
                  // attribute: 'Abuse Confidence Score'
                });
              }}
            />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="Status" name="status" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Col>

        <Col span={24}>
          <Divider>
            {typeOptions.find((i) => i.value === form.getFieldValue('type')).label} Settings
          </Divider>
        </Col>
        {form.getFieldValue('type') === 'Email' ? (
          <Col span={24}>
            <Form.Item
              label="Recipients"
              name={['context', 'recipients']}
              rules={[{ required: true }]}>
              <RecipientsPicker placeholder="Please Select" />
            </Form.Item>
          </Col>
        ) : null}
        {form.getFieldValue('type') === 'ThreatIntelligence' ? (
          <>
            <Col span={12}>
              <Form.Item
                label="Threat Intelligence Type"
                name={['context', 'threat_intelligence_type']}
                rules={[{ required: true }]}>
                <Select
                  placeholder="Please Select"
                  options={threatIntelligenceTypeOptions}
                  onChange={(value) => {
                    form.setFieldValue('context', {
                      threat_intelligence_type: value,
                      attribute:
                        value === 'VirusTotal' ? 'Malicious Count' : 'Abuse Confidence Score'
                      // attribute: 'Abuse Confidence Score'
                    });
                  }}
                />
              </Form.Item>
            </Col>
            {form.getFieldValue(['context', 'threat_intelligence_type']) === 'GroupIB' ? (
              <>
                <Col span={12}>
                  <Form.Item label="URL" name={['context', 'url']} rules={[{ required: true }]}>
                    <Input placeholder="URL" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Username"
                    name={['context', 'username']}
                    rules={[{ required: true }]}>
                    <Input placeholder="Username" />
                  </Form.Item>
                </Col>
              </>
            ) : null}
            <Col span={12}>
              <Form.Item label="API Key" name={['context', 'api_key']} rules={[{ required: true }]}>
                <Input placeholder="API Key" />
              </Form.Item>
            </Col>
            {form.getFieldValue(['context', 'threat_intelligence_type']) !== 'GroupIB' ? (
              <>
                <Col span={8}>
                  <Form.Item label=" " name={['context', 'attribute']}>
                    <Input
                      placeholder="Attribute"
                      disabled
                      className="no-border"
                      value={`${form.getFieldValue(['context', 'attribute'])}`}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="Condition"
                    name={['context', 'condition']}
                    rules={[{ required: true }]}>
                    <Select placeholder="Condition" options={policyAttributeConditionOptions} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="Condition Value"
                    name={['context', 'value']}
                    rules={[{ required: true }]}>
                    <Input placeholder="Condition Value" />
                  </Form.Item>
                </Col>
              </>
            ) : null}
          </>
        ) : null}
        {form.getFieldValue('type') === 'Incident' ? (
          <>
            <Col span={12}>
              <Form.Item
                label="Incident Type"
                name={['context', 'incidentType']}
                rules={[{ required: true }]}>
                <Select
                  options={incidentTypeOptions}
                  onChange={(value) => {
                    form.setFieldValue(['context', 'incidentType'], value);
                    form.setFieldValue(['context', 'url'], undefined);
                    form.setFieldValue(['context', 'auth_type'], undefined);
                    form.setFieldValue(['context', 'username'], undefined);
                    form.setFieldValue(['context', 'password'], undefined);
                    form.setFieldValue(['context', 'token'], undefined);
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12} />
            <Col span={12}>
              <Form.Item
                label="URL"
                name={['context', 'url']}
                rules={[{ required: true }, { type: 'url' }]}>
                <Input placeholder="ex. https://zirozen.zendesk.com" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Authentication Type"
                name={['context', 'auth_type']}
                rules={[{ required: true }]}>
                <Select
                  options={authTypeOptions}
                  onChange={(value) => {
                    form.setFieldValue(['context', 'auth_type'], value);
                    form.setFieldValue(['context', 'username'], undefined);
                    form.setFieldValue(['context', 'password'], undefined);
                    form.setFieldValue(['context', 'token'], undefined);
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={24} />
            <Col span={12}>
              <Form.Item
                label="Username"
                name={['context', 'username']}
                rules={[{ required: true }]}>
                <Input placeholder="username" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={
                  form.getFieldValue(['context', 'auth_type']) === 'Basic' ? 'Password' : 'Token'
                }
                name={[
                  'context',
                  form.getFieldValue(['context', 'auth_type']) === 'Basic' ? 'password' : 'token'
                ]}
                rules={[{ required: true }]}>
                <Input
                  type="password"
                  placeholder={
                    form.getFieldValue(['context', 'auth_type']) === 'Basic' ? 'Password' : 'Token'
                  }
                />
              </Form.Item>
            </Col>
          </>
        ) : null}
        {form.getFieldValue('type') === 'AI' ? (
          <>
            <Col span={12}>
              <Form.Item
                label="Artificial Intelligence Type"
                name={['context', 'ai_type']}
                rules={[{ required: true }]}>
                <Select
                  placeholder="Please Select"
                  options={aiTypeOptions}
                  onChange={(value) => {
                    form.setFieldValue('context', {
                      ai_type: value,
                      endpoint: undefined
                      // attribute: 'Abuse Confidence Score'
                    });
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Endpoint"
                name={['context', 'endpoint']}
                rules={[{ required: form.getFieldValue(['context', 'ai_type']) !== 'OpenAI' }]}>
                <Input placeholder="Endpoint" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={
                  form.getFieldValue(['context', 'ai_type']) === 'OpenAI' ? 'Model' : 'Deployment'
                }
                name={['context', 'model']}
                rules={[{ required: true }]}>
                <Input
                  placeholder={
                    form.getFieldValue(['context', 'ai_type']) === 'OpenAI' ? 'Model' : 'Deployment'
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="API Key" name={['context', 'api_key']} rules={[{ required: true }]}>
                <Input placeholder="API Key" />
              </Form.Item>
            </Col>
          </>
        ) : null}
      </Row>
    </div>
  );
}
