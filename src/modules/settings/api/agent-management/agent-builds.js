import api from '@api';

const END_POINT = `/settings/agent-build`;

const transform = (item) => ({
  id: item.id,
  agent_arch: item.agent_arch,
  agent_platform: item.agent_platform,
  agent_version: item.agent_version,
  modified_time: item.modified_time
  // ...(item.agent_ref_name
  //   ? {
  //       zip: [
  //         {
  //           ref: item.agent_ref_name,
  //           name: item.agent_file_name,
  //           uid: item.agent_ref_name,
  //           url: `/api/download/${item.agent_ref_name}?mid=`,
  //           status: 'done'
  //         }
  //       ]
  //     }
  //   : {})
});

const transformForServer = async (item) => {
  return Promise.resolve({
    agent_platform: item.agent_platform,
    agent_arch: item.agent_arch,
    ...(item.zip && item.zip.length && (item.zip[0].ref || item.zip[0].response.ref)
      ? {
          agent_ref_name: item.zip[0].ref || item.zip[0].response.ref,
          agent_file_name: item.zip[0].name || item.zip[0].response.name
        }
      : {})
  });
};

const sortKeyMap = {};

const searchableColumns = ['agent_platform', 'agent_version', 'agent_arch'];

export function getAllAgentBuilds(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function updateAgentBuildApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then(() => getAllAgentBuilds(0, 1000, {}))
    .then(({ result }) => result.find((i) => i.id === item.id));
}

export function deleteAgentBuildApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
