import api from '@api';

const END_POINT = `/inventory/endpoint-approvals/endpoints`;

const transform = (item) => ({
  id: item.id,
  uuid: item.uuid,
  host_name: item.host_name,
  ip_addresses: JSON.parse(item.ip_addresses),
  status: item.status,
  reject_reason: item.reject_reason,
  updated_by: item.updated_by,
  createdAt: item.created_time
});

// const transformForServer = async (item) => {
//   return Promise.resolve({
//     name: item.name,
//     department: item.department,
//     organization: item.organization
//   });
// };

const sortKeyMap = {
  createdAt: 'created_time'
};

const searchableColumns = ['host_name'];

export function getAllApprovalApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: [
        ...(sortFilter.status
          ? [
              {
                operator: 'Equal',
                column: 'status',
                value: sortFilter.status
              }
            ]
          : []),
        ...(sortFilter.searchTerm
          ? searchableColumns.map((c) => ({
              operator: 'Contains',
              column: c,
              value: sortFilter.searchTerm
            }))
          : [])
      ]
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

// function getApprovalByIdApi(id) {
//   return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
// }

export function changeApprovalStatusApi({ id, status, reject_reason }) {
  return api.put(`/inventory/endpoint-approvals/${id}`, {
    status,
    ...(status === 0 ? { reject_reason } : {})
  });
}
