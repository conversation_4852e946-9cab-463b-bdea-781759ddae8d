import api from '@api';

const END_POINT = `/settings/agent-settings/1`;

const transform = (data) => ({
  refresh_cycle: data.refresh_cycle,
  system_action_refresh_cycle: data.system_action_refresh_cycle,
  patch_refresh_cycle: data.patch_refresh_cycle,
  sbom_refresh_cycle: data.sbom_refresh_cycle,
  process_refresh_cycle: data.process_refresh_cycle,
  network_refresh_cycle: data.network_refresh_cycle,
  certificate_refresh_cycle: data.certificate_refresh_cycle,
  startup_items_refresh_cycle: data.startup_items_refresh_cycle,
  users_refresh_cycle: data.users_refresh_cycle,
  resources_refresh_cycle: data.resources_refresh_cycle,
  file_events_refresh_cycle: data.file_events_refresh_cycle,
  service_refresh_cycle: data.service_refresh_cycle,
  quick_check_refresh_cycle: data.quick_check_refresh_cycle,
  software_meter_refresh_cycle: data.software_meter_refresh_cycle,
  max_file_download_speed: data.max_file_download_speed / 1024
});

const transformForServer = (data) => ({
  refresh_cycle: data.refresh_cycle,
  system_action_refresh_cycle: data.system_action_refresh_cycle,
  patch_refresh_cycle: data.patch_refresh_cycle,
  sbom_refresh_cycle: data.sbom_refresh_cycle,
  process_refresh_cycle: data.process_refresh_cycle,
  network_refresh_cycle: data.network_refresh_cycle,
  quick_check_refresh_cycle: data.quick_check_refresh_cycle,
  certificate_refresh_cycle: data.certificate_refresh_cycle,
  startup_items_refresh_cycle: data.startup_items_refresh_cycle,
  users_refresh_cycle: data.users_refresh_cycle,
  resources_refresh_cycle: data.resources_refresh_cycle,
  file_events_refresh_cycle: data.file_events_refresh_cycle,
  service_refresh_cycle: data.service_refresh_cycle,
  software_meter_refresh_cycle: data.software_meter_refresh_cycle,
  max_file_download_speed: data.max_file_download_speed * 1024
});

export function getAgentConfigApi() {
  return api.get(END_POINT).then(({ result }) => transform(result));
}

export function updateAgentConfigApi(data) {
  return api.put(END_POINT, transformForServer(data)).then(() => getAgentConfigApi());
}

export function getAgentApprovalSettingsApi() {
  return api.get(`/settings/agent-approval-settings/1`).then(({ result }) => ({
    approval_type: result.approval_type,
    approval_auto_type: result.approval_auto_type || 'all',
    approval_criteria: (result.approval_criteria || []).map((item) => ({
      attribute: item.attribute,
      condition: item.condition_operator,
      value: item.condition_value,
      operator: item.condition
    }))
  }));
}

export function updateAgentApprovalSettingsApi(data) {
  return api
    .put(`/settings/agent-approval-settings/1`, {
      approval_type: data.approval_type,
      approval_auto_type: data.approval_type === 'auto' ? data.approval_auto_type : null,
      approval_criteria:
        data.approval_auto_type === 'criteria'
          ? (data.approval_criteria || []).map((item) => ({
              attribute: item.attribute,
              condition: item.operator,
              condition_value: item.value,
              condition_operator: item.condition
            }))
          : null
    })
    .then(() => getAgentApprovalSettingsApi());
}
