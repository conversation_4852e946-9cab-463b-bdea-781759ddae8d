import api from '@api';
import Dayjs from 'dayjs';

const END_POINT = `/settings/vulnerability-preference/1`;
const FORMAT = 'HH:mm:ss';

const transform = (item) => ({
  vuln_sync_running: <PERSON><PERSON><PERSON>(item.vuln_sync_running),
  vuln_sync_status: <PERSON><PERSON><PERSON>(item.vuln_sync_status),
  vuln_scan_job_time: item.vuln_scan_job_time,
  vuln_database_sync_time: Dayjs(item.vuln_database_sync_time, FORMAT),
  last_vuln_database_sync_time: item.last_vuln_database_sync_time,
  cve_count: item.cve_count
});

const transformForServer = (item) => ({
  vuln_scan_job_time: +item['vuln_scan_job_time']
    ? +item['vuln_scan_job_time']
    : item['vuln_scan_job_time'],
  vuln_database_sync_time: item.vuln_database_sync_time.format(FORMAT)
});

export function getVulnerabilityPreferenceApi() {
  return api.get(END_POINT).then(({ result }) => transform(result));
}

export function updateVulnerabilityPreferenceApi(data) {
  return api.put(END_POINT, transformForServer(data)).then(() => getVulnerabilityPreferenceApi());
}

export function vulnerabilitySyncApi() {
  return api.get('/settings/vulnerability-preference/sync');
}
