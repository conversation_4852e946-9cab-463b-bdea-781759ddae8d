import api from '@api';

const END_POINT = `/settings/port`;

const transform = (item) => ({
  id: item.id,
  name: item.name,
  ports: item.ports || [],
  remediations: item.remediation_action || [],
  raise_alert: <PERSON><PERSON>an(item.raise_alert),
  status: <PERSON><PERSON>an(item.status),
  severity: item.severity,
  actions: item.actions || [],
  createdAt: item.created_time
});

const transformForServer = async (item) => {
  return Promise.resolve({
    id: item.id,
    name: item.name,
    ports: item.ports || [],
    raise_alert: item.raise_alert ? 1 : 0,
    ...(item.raise_alert
      ? { actions: item.actions || [], remediation_action: item.remediations || [] }
      : {}),
    severity: item.severity,
    status: item.status ? 1 : 0,
    actions: item.actions !== 'null' && item.actions ? item.actions : []
  });
};

const searchableColumns = ['name', 'ports'];

export function getAllPortsApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${sortFilter.sort.field}`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getPortApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updatePortApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getPortApi(data.result));
}

export function createPortApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getPortApi(data.result));
}

export function deletePortApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
