import api from '@api';

const END_POINT = `/settings/ldap-server`;

const transform = (item) => {
  return {
    id: item.id,
    name: item.ldap_server_name,
    host: item.ldap_server_host,
    port: item.ldap_server_port,
    fqdn: item.ldap_server_fqdn,
    username: item.username,
    baseDn: item.ldap_server_base_dn,
    groupBase: item.ldap_server_group_base,
    mapping: JSON.parse(item.mapping),
    autoSync: item.ldap_server_auto_sync === 'yes',
    syncInterval: item.ldap_server_auto_sync_interval,
    createdAt: item.created_time
  };
};

const transformForServer = async (item) => {
  return Promise.resolve({
    id: item.id,
    ldap_server_name: item.name,
    ldap_server_host: item.host,
    ldap_server_port: item.port,
    ldap_server_fqdn: item.fqdn,
    username: item.username,
    password: item.password,
    ldap_server_base_dn: item.baseDn,
    ldap_server_group_base: item.groupBase,
    ldap_server_auto_sync: item.autoSync ? 'yes' : 'no',
    ...(item.syncInterval ? { ldap_server_auto_sync_interval: item.syncInterval } : {}),
    mapping: item.mapping
  });
};

const sortKeyMap = {
  name: 'ldap_server_name',
  host: 'ldap_server_host',
  port: 'ldap_server_port',
  fqdn: 'ldap_server_fqdn',
  baseDn: 'ldap_server_base_dn',
  groupBase: 'ldap_server_group_base',
  createdAt: 'created_time'
};

const searchableColumns = [
  'ldap_server_name',
  'ldap_server_host',
  'ldap_server_fqdn',
  'ldap_server_port',
  'ldap_server_base_dn',
  'ldap_server_group_base'
];

export function getAllLdapServerApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getLdapServerApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateLdapServerApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getLdapServerApi(data.result));
}

export function createLdapServerApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getLdapServerApi(data.result));
}

export function deleteLdapServerApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}

export function syncLDAPServerConfigApi(data) {
  return api.post(`/settings/ldap-server/sync/${data.id}`);
}

export function testLDAPServerConfigApi(data) {
  return transformForServer(data).then((item) => {
    return api.post(`/settings/ldap-server/test`, item);
  });
}
