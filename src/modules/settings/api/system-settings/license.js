import api from '@api';

export function getLicenseApi() {
  return api.get(`/settings/license`).then(({ result }) => {
    return {
      po: result.po_number,
      invoiceNumber: result.invoice_number,
      licenseProduct: result.license_product,
      email: result.email_address,
      partner: result.partner_name,
      expiresAt: result.expiry_time,
      issuedAt: result.issue_time,
      licensedTo: result.license_to,
      licenseType: result.license_type,
      totalEndPoints: result.no_of_endpoints,
      remainingDays: result.remaining_days,
      remainingEndPoints: result.remaining_end_points,
      usedEndPoints: result.used_end_points
    };
  });
}

export function getActivationCodeApi() {
  return api.get(`/settings/license/activation/code`).then(({ result }) => ({
    code: result
  }));
}

export function updateLicenseCodeApi(data) {
  return api.put(`/settings/license`, { license: data.code });
}
