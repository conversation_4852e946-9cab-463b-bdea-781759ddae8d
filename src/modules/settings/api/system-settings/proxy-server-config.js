import api from '@api';

const END_POINT = `/settings/proxy-server/0`;

const transform = (data) => ({
  proxy_host: data.proxy_host,
  proxy_port: data.proxy_port,
  proxy_type: data.proxy_type,
  proxy_enable: data.proxy_enable === 'yes',
  proxy_enable_authentication: data.proxy_enable_authentication === 'yes',
  username: data.username,
  timeout: data.timeout
});

const transformForServer = (data) => ({
  proxy_host: data.proxy_host,
  proxy_port: data.proxy_port,
  proxy_type: data.proxy_type,
  proxy_enable: data.proxy_enable ? 'yes' : 'no',
  proxy_enable_authentication: data.proxy_enable_authentication ? 'yes' : 'no',
  ...(data.proxy_enable_authentication
    ? {
        username: data.username,
        password: data.password
      }
    : {}),
  timeout: data.timeout,
  ...(data.target ? { target: data.target } : {})
});

export function getProxyServerConfigApi() {
  return api.get(END_POINT).then(({ result }) => transform(result));
}

export function updateProxyServerConfigApi(data) {
  return api.put(END_POINT, transformForServer(data)).then(() => getProxyServerConfigApi());
}

export function testProxyServerConfigApi(data) {
  return api.post(`/settings/proxy-server/test`, transformForServer(data)).then((data) => ({
    status: data.status,
    error: data['response-message'],
    message: data.result
  }));
}
