import api from '@api';

const END_POINT = `/settings/edition`;

const transform = (item, forServer) => {
  return {
    ...(!forServer
      ? { id: item.id, created_time: item.created_time, modified_time: item.modified_time }
      : {}),
    metric: item.metric,
    license_subscription_type: item.license_subscription_type,
    license_edition: item.license_edition
  };
};

const sortKeyMap = {
  license_edition: 'license_edition',
  metric: 'metric',
  license_subscription_type: 'license_subscription_type'
};

const searchableColumns = ['license_edition', 'metric', 'license_subscription_type'];

export function getAllLicenseEditionApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map((i) => transform(i, false))
      };
    });
}

export function getLicenseEditionApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateLicenseEditionApi(item) {
  return api
    .put(`${END_POINT}/${item.id}`, transform(item, true))
    .then((data) => getLicenseEditionApi(data.result));
}

export function createLicenseEditionApi(item) {
  return api
    .post(`${END_POINT}`, transform(item, true))
    .then((data) => getLicenseEditionApi(data.result));
}

export function deleteLicenseEditionApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
