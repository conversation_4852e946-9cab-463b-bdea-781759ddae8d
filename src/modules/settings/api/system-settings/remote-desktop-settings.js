import api from '@api';

const END_POINT = `/settings/rdp/1`;

const transform = (data) => ({
  remote_session_indicator: data.remote_session_indicator === 'yes',
  user_consent: data.user_consent === 'yes',
  connection_type: data.connection_type,
  server_fqdn: data.server_fqdn,
  server_ip_address: data.server_ip_address,
  ssh_port: data.ssh_port,
  username: data.username,
  auth_type: data.auth_type,
  archived: data.archived,
  ssh_key: data.ssh_key,
  ssh_passphrase: data.ssh_passphrase
});

const transformForServer = (data) => ({
  remote_session_indicator: data.remote_session_indicator ? 'yes' : 'no',
  user_consent: data.user_consent ? 'yes' : 'no',
  connection_type: data.connection_type,

  ...(data.connection_type === 'remote'
    ? {
        username: data.username,
        auth_type: data.auth_type,
        server_fqdn: data.server_fqdn,
        server_ip_address: data.server_ip_address,
        ssh_port: data.ssh_port,
        ...(data.auth_type === 'password'
          ? { password: data.password }
          : {
              ssh_key: data.ssh_key,
              ssh_passphrase: data.ssh_passphrase
            })
      }
    : {})
});

export function getRemoteDesktopSettingsApi() {
  return api.get(END_POINT).then(({ result }) => transform(result));
}

export function updateRemoteDesktopSettingsApi(data) {
  return api.put(END_POINT, transformForServer(data)).then(() => getRemoteDesktopSettingsApi());
}

export function testRemoteDesktopSettingsApi(data) {
  return api.post(`/settings/rdp/test`, transformForServer(data)).then((data) => ({
    status: data.status,
    error: data['response-message'],
    message: data.result
  }));
}
