import api from '@api';

export function getRiskSettingsApi(id) {
  return api.get(`/settings/risk`).then(({ result }) => {
    return {
      id: result.id,
      useDefault: Boolean(result.is_default),
      scoreWeight: result.vulnerability_score_weights ? +result.vulnerability_score_weights : 0,
      severityWeight: result.vulnerability_severity_weights
        ? +result.vulnerability_severity_weights
        : 0,
      threatsWeight: result.threats_weights ? +result.threats_weights : 0,
      quickCheckWeight: result.quick_checks_weights ? +result.quick_checks_weights : 0
    };
  });
}

export function updateRiskSettingApi(data) {
  return api.put(`/settings/risk/${data.id}`, {
    ...(data.id === 0
      ? {
          is_default: data.useDefault ? 1 : 0
        }
      : {
          vulnerability_score_weights: String(data.scoreWeight),
          vulnerability_severity_weights: String(data.severityWeight),
          threats_weights: String(data.threatsWeight),
          quick_checks_weights: String(data.quickCheckWeight)
        })
  });
}
