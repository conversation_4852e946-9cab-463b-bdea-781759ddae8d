import api from '@api';

const END_POINT = `/settings/currency`;

const transform = (item, forServer) => {
  return {
    ...(!forServer
      ? { id: item.id, created_time: item.created_time, modified_time: item.modified_time }
      : {}),
    name: item.name,
    exchange_rate: item.exchange_rate
  };
};

const sortKeyMap = {
  name: 'name',
  metric: 'metric',
  exchange_rate: 'exchange_rate'
};

const searchableColumns = ['name', 'exchange_rate'];

export function getAllCurrencyApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map((i) => transform(i, false))
      };
    });
}

export function getCurrencyApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateCurrencyApi(item) {
  return api
    .put(`${END_POINT}/${item.id}`, transform(item, true))
    .then((data) => getCurrencyApi(data.result));
}

export function createCurrencyApi(item) {
  return api
    .post(`${END_POINT}`, transform(item, true))
    .then((data) => getCurrencyApi(data.result));
}

export function deleteCurrencyApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
