import api from '@api';

const END_POINT = `/settings/server/1`;

const transform = (item) => ({
  session_timeout_enable: item.session_timeout_enable === 'yes',
  session_timeout: item.session_timeout,
  session_idle_timeout: item.session_idle_timeout,
  endpoint_refresh_call_duration: item.endpoint_refresh_call_duration,
  eol_scan_job_time: item.eol_scan_job_time,
  log_level: item.log_level,
  archived: item.archived
});

const transformForServer = (item) => ({
  session_timeout_enable: item['session_timeout_enable'] ? 'yes' : 'no',
  session_timeout: +item['session_timeout'] ? +item['session_timeout'] : item['session_timeout'],
  session_idle_timeout: +item['session_idle_timeout']
    ? +item['session_idle_timeout']
    : item['session_idle_timeout'],
  endpoint_refresh_call_duration: +item['endpoint_refresh_call_duration']
    ? +item['endpoint_refresh_call_duration']
    : item['endpoint_refresh_call_duration'],
  eol_scan_job_time: +item['eol_scan_job_time']
    ? +item['eol_scan_job_time']
    : item['eol_scan_job_time'],
  log_level: +item['log_level'] ? +item['log_level'] : item['log_level']
});

export function getServerSettingsApi() {
  return api.get(END_POINT).then(({ result }) => transform(result));
}

export function updateServerSettingsApi(data) {
  return api.put(END_POINT, transformForServer(data)).then(() => getServerSettingsApi());
}
