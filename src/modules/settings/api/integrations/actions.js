import api from '@api';

const END_POINT = `/settings/action`;

const transformContext = (context) => ({
  url: context.url,
  username: context.username,
  incidentType: context.incident_type,
  auth_type: context.authentication_type,
  recipients: context.recipients,
  api_key: context['api-key'],
  ...(context.threat_intelligence_type
    ? {
        attribute:
          context.attribute || context.threat_intelligence_type === 'VirusTotal'
            ? 'Malicious Count'
            : 'Abuse Confidence Score',
        condition: context.condition,
        value: context.condition_value || null,
        url: context.url,
        username: context.username
      }
    : {}),
  endpoint: context.endpoint,
  model: context.model,
  ai_type: context['ai-type'],
  threat_intelligence_type: context.threat_intelligence_type,
  ...(context.password ? { password: context.password } : {}),
  ...(context.token ? { token: context.token } : {})
});

const transformContextForServer = (context, actionType) => ({
  ...(actionType === 'Email'
    ? {
        recipients: context.recipients
      }
    : {}),
  ...(actionType === 'AI'
    ? {
        endpoint: context.endpoint,
        model: context.model,
        'ai-type': context.ai_type,
        'api-key': context.api_key
      }
    : {}),
  ...(actionType === 'ThreatIntelligence'
    ? {
        threat_intelligence_type: context.threat_intelligence_type,
        'api-key': context.api_key,
        url: context.url,
        username: context.username,
        attribute: context.attribute,
        condition: context.condition,
        condition_value: context.value || null
      }
    : {}),
  ...(actionType === 'Incident'
    ? {
        incident_type: context.incidentType,
        url: context.url,
        username: context.username,
        authentication_type: context.auth_type,
        ...(context.password ? { password: context.password } : {}),
        ...(context.token ? { token: context.token } : {})
      }
    : {})
});

const transform = (item) => ({
  id: item.id,
  name: item.action_name,
  description: item.description,
  type: item.action_type,
  status: Boolean(item.status),
  context: transformContext(item.action_context),
  createdBy: item.created_by,
  createdAt: item.created_time
});

const transformForServer = async (item) => {
  return Promise.resolve({
    id: item.id,
    action_name: item.name,
    description: item.description,
    action_type: item.type,
    status: item.status ? 1 : 0,
    ...(item.context ? { action_context: transformContextForServer(item.context, item.type) } : {})
  });
};

const sortKeyMap = {
  createdAt: 'createdTime',
  type: 'action_type',
  name: 'action_name'
};

const searchableColumns = ['action_name', 'description', 'action_type', 'action_context'];

export function getAllActionsApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: (data.result || []).map(transform)
      };
    });
}

export function getActionApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => {
    return transform(result);
  });
}

export function updateActionApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getActionApi(data.result));
}

export function createActionApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getActionApi(data.result));
}

export function deleteActionApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
