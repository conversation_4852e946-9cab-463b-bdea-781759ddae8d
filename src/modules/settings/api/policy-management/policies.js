import {
  transformAssetScope,
  transformAssetScopeForServer
} from '@/src/components/pickers/AssetScopePicker';
import api from '@api';

const END_POINT = `/settings/policy`;

const transformContext = (context) =>
  context.map((item) => ({
    attribute: item.attribute,
    condition: item.condition,
    value: item.condition_value,
    join: item.condition_operator
  }));

const transformContextForServer = (context) =>
  context.map((item, index) => ({
    attribute: item.attribute,
    condition: item.condition,
    condition_value: item.value || null,
    ...(index < context.length - 1 ? { condition_operator: item.join } : {})
  }));

const transform = (item) => ({
  id: item.id,
  description: item.policy_description,
  name: item.policy_name,
  severity: item.severity,
  module: item.policy_module,
  actions: item.policy_action || [],
  remediations: item.policy_remediation_action || [],
  status: Boolean(item.status),
  context: transformContext(item.policy_context || {}),
  ...transformAssetScope(item),
  createdAt: item.created_time
});

const transformForServer = async (item) => {
  return Promise.resolve({
    policy_name: item.name,
    policy_description: item.description,
    policy_module: item.module,
    severity: item.severity,
    policy_action: item.actions || [],
    policy_remediation_action: item.remediations || [],
    status: item.status ? 1 : 0,
    ...transformAssetScopeForServer(item),
    policy_context: transformContextForServer(item.context)
  });
};

const sortKeyMap = {
  name: 'policy_name',
  description: 'policy_description'
};

const searchableColumns = ['policy_name', 'policy_description'];

export function getAllPoliciesApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getPolicyApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updatePolicyApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getPolicyApi(data.result));
}

export function createPolicyApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getPolicyApi(data.result));
}

export function deletePolicyApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}

export function getAssetPolicyAttributesApi() {
  return api.get(`${END_POINT}/attributes/endpoint`).then(({ result }) => ({
    result: result.map((item) => ({
      name: Object.keys(item)[0],
      id: Object.keys(item)[0],
      dataType: Object.values(item)[0]
    }))
  }));
}

export function getFimPolicyAttributesApi() {
  return api.get(`${END_POINT}/attributes/fim`).then(({ result }) => ({
    result: result.map((item) => ({
      name: Object.keys(item)[0],
      id: Object.keys(item)[0],
      dataType: Object.values(item)[0]
    }))
  }));
}

export function getVulnerabilityPolicyAttributesApi() {
  return api.get(`${END_POINT}/attributes/vulnerability`).then(({ result }) => ({
    result: result.map((item) => ({
      name: Object.keys(item)[0],
      id: Object.keys(item)[0],
      dataType: Object.values(item)[0]
    }))
  }));
}
