import {
  transformAssetScope,
  transformAssetScopeForServer
} from '@/src/components/pickers/AssetScopePicker';
import { getTaskContextApi } from '@/src/modules/compliance/api/compliance-rules';
import api from '@api';

const END_POINT = `/settings/quick-checks`;

const transform = (item) => ({
  id: item.id,
  description: item.description,
  status: <PERSON><PERSON><PERSON>(item.status),
  name: item.name,
  raise_alert: +item.raise_alert === 1 ? true : false,
  severity: item.severity,
  command: item.command,
  command_type: item.command_type,
  expectedOutput: (item.expected_output || []).map((i) => ({
    value: i.ConditionValue,
    operator: i.RuleCondition,
    condition: i.Condition
  })),
  actions: item.actions || [],
  remediations: item.remediation_action || [],
  ...transformAssetScope(item),
  createdAt: item.created_time
});

const transformForServer = async (item) => {
  return Promise.resolve({
    name: item.name,
    status: item.status ? 1 : 0,
    severity: item.severity,
    raise_alert: item.raise_alert ? 1 : 0,
    description: item.description,
    ...(item.raise_alert
      ? { actions: item.actions || [], remediation_action: item.remediations || [] }
      : {}),
    ...transformAssetScopeForServer(item),
    command: item.command,
    command_type: item.command_type,
    expected_output: (item.expectedOutput || []).map((i) => ({
      ConditionValue: i.value,
      RuleCondition: i.operator,
      Condition: i.condition
    }))
  });
};

const sortKeyMap = {
  expectedOutput: 'expected_output',
  createdAt: 'created_time'
};

const searchableColumns = ['name', 'description', 'query'];

export function getAllQuickCheckApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function testQuickCheckApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`/patch/asset/quick/checks/${item.test_asset}`, data);
    })
    .then((data) => getTaskContextApi(data.result.id));
}

export function getQuickCheckApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateQuickCheckApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getQuickCheckApi(data.result));
}

export function createQuickCheckApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getQuickCheckApi(data.result));
}

export function deleteQuickCheckApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
