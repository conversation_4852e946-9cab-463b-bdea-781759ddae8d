import api from '@api';

const END_POINT = `/settings/network-discovery`;

const transformContext = (context = {}, item) => ({
  host: context.host,
  // subnet: context.subnet,
  range: context.range,
  cidr: context.cidr
});

const transform = (item) => ({
  id: item.id,
  ip_type: item.ip_type,
  name: item.discovery_name,
  discovery_type: item.discovery_type,
  description: item.discovery_description,
  organization: item.organization,
  department: item.department,
  port: item.port,
  retryCount: item.retry_count,
  timeout: item.timeout,
  running: item.running,
  credential_profile: item.credential_profile,
  polling_interval: item.polling_interval,
  ...transformContext(item.ip_context, item),
  createdAt: item.created_time
});

const transformContextForServer = (item) => ({
  ip_context: {
    ...(['IP'].includes(item.ip_type)
      ? {
          host: item.host
          // subnet: item.subnet
        }
      : {}),
    ...(['IP-RANGE'].includes(item.ip_type)
      ? {
          range: item.range
        }
      : {}),
    ...(['CIDR'].includes(item.ip_type)
      ? {
          cidr: item.cidr
        }
      : {})
  }
});

const transformForServer = (item) => ({
  ...(item.id ? { id: item.id } : {}),
  discovery_name: item.name,
  discovery_description: item.description,
  discovery_type: item.discovery_type,
  port: item.port,
  ip_type: item.ip_type,
  credential_profile: item.credential_profile,
  polling_interval: item.polling_interval,
  organization: item.organization,
  department: item.department,
  retry_count: item.retryCount,
  timeout: item.timeout,
  ...transformContextForServer(item)
});

const sortKeyMap = {
  name: 'discovery_name',
  createdAt: 'created_time'
};

const searchableColumns = [
  'discovery_name',
  'discovery_description',
  'discovery_type',
  'ip_context'
];

export function getAllNetworkProfilesApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getNetworkProfileApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateNetworkProfileApi(item) {
  if (item.forDiscoveryAction) {
    return discoveryActionApi(item);
  }
  return api
    .put(`${END_POINT}/${item.id}`, transformForServer(item))
    .then(() => getNetworkProfileApi(item.id));
}

export function createNetworkProfileApi(item) {
  return api
    .post(`${END_POINT}`, transformForServer(item))
    .then((data) => getNetworkProfileApi(data.result));
}

export function deleteNetworkProfileApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}

export function discoveryActionApi(record) {
  return api
    .post(`/settings/network-discovery/${record.running ? 'abort' : 'run'}/${record.id}`)
    .then((data) => {
      return getNetworkProfileApi(record.id);
    });
}

const resultSearchableColumns = ['host', 'status', 'message', 'credential'];
const resultSortKeyMap = {
  createdAt: 'created_time'
};

export function getNetworkProfileResultApi(id, offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/result/${id}`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              resultSortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? resultSearchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map((item) => ({
          ...item,
          createdAt: item.created_time
        }))
      };
    });
}
