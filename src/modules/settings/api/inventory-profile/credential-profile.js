import api from '@api';

const END_POINT = `/settings/credential-profile`;

const transformContext = (context = {}, item) => ({
  snmp_version: context.version,
  ...(['v1', 'v2c'].includes(context.version)
    ? {
        snmp_community: context.community
      }
    : {}),
  ...(['v3'].includes(context.version)
    ? {
        snmp_security_username: context.snmp_security_username,
        snmp_security_level: context.snmp_security_level,
        snmp_authentication_protocol: context.snmp_authentication_protocol,
        snmp_privacy_protocol: context.snmp_privacy_protocol,
        snmp_private_password: context.snmp_private_password,
        snmp_authentication_password: context.snmp_authentication_password
      }
    : {})
});

const transform = (item) => ({
  id: item.id,
  name: item.credential_profile_name,
  credential_type: item.credential_type,
  description: item.credential_profile_description,
  ...transformContext(item.context, item),
  createdAt: item.created_time
});

const transformContextForServer = (item) => ({
  context: {
    version: item.snmp_version,
    ...(['v1', 'v2c'].includes(item.snmp_version)
      ? {
          community: item.snmp_community
        }
      : {}),
    ...(['v3'].includes(item.snmp_version)
      ? {
          snmp_security_username: item.snmp_security_username,
          snmp_security_level: item.snmp_security_level,
          ...(item.snmp_authentication_protocol
            ? { snmp_authentication_protocol: item.snmp_authentication_protocol }
            : {}),
          ...(item.snmp_privacy_protocol
            ? { snmp_privacy_protocol: item.snmp_privacy_protocol }
            : {}),
          ...(item.snmp_private_password
            ? { snmp_private_password: item.snmp_private_password }
            : {}),
          ...(item.snmp_authentication_password
            ? { snmp_authentication_password: item.snmp_authentication_password }
            : {})
        }
      : {})
  }
});

const transformForServer = (item) => ({
  ...(item.id ? { id: item.id } : {}),
  credential_profile_name: item.name,
  credential_profile_description: item.description,
  credential_type: item.credential_type,
  ...transformContextForServer(item)
});

const sortKeyMap = {
  name: 'credential_profile_name',
  createdAt: 'created_time'
};

const searchableColumns = [
  'credential_profile_name',
  'credential_profile_description',
  'credential_type',
  'context'
];

export function getAllCredentialProfilesApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getCredentialProfileApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateCredentialProfileApi(item) {
  return api
    .put(`${END_POINT}/${item.id}`, transformForServer(item))
    .then(() => getCredentialProfileApi(item.id));
}

export function createCredentialProfileApi(item) {
  return api
    .post(`${END_POINT}`, transformForServer(item))
    .then((data) => getCredentialProfileApi(data.result));
}

export function deleteCredentialProfileApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
