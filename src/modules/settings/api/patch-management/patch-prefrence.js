import api from '@api';
import Dayjs from 'dayjs';
import Moment from 'moment';

const END_POINT = `/patch/patch-preference`;

const FORMAT = `'HH:mm:ss'`;

export const PATCH_PREFRENCE_FORM_TYPE = {
  PATCH_APPROVAL_POLICY: 'patchApprovalPolicy',
  SYSTEM_HEALTH: 'systemHealth'
};

const transformForServer = (item) => {
  return {
    id: item.id,
    name: item.name,
    createdById: item.created_by_id,
    createdTime: item.created_time,
    updatedById: item.updated_by_id,
    updatedTime: item.updated_time,
    removed: item.removed,
    oob: item.oob,
    patchApprovalPolicy: item.patch_approval_policy,
    enablePatching: item.enable_patching,
    enableScheduling: item.enable_scheduling,
    ...(item.schedule_time ? { scheduleTime: item.schedule_time.unix() } : {}),
    ...(item.zero_touch_deployment_schedule_time
      ? { zeroTouchDeploymentScheduleTime: item.zero_touch_deployment_schedule_time.unix() }
      : {}),
    ...(item.patch_approval_schedule_time
      ? { patchApprovalScheduleTime: item.patch_approval_schedule_time.unix() }
      : {}),
    enabledCategory_list: item.enabled_category_list,
    enabledPatchOs: item.enabled_patch_os,
    lastSyncDate: item.last_sync_date,
    isScanRunning: item.is_scan_running,
    highlyVulnerableCriticalPatch: item.highly_vulnerable_critical_patch,
    highlyVulnerableImportantPatches: item.highly_vulnerable_important_patches,
    highlyVulnerableModeratePatch: item.highly_vulnerable_moderate_patch,
    highlyVulnerableLowPatch: item.highly_vulnerable_low_patch,
    vulnerableCriticalPatch: item.vulnerable_critical_patch,
    vulnerableImportantPatches: item.vulnerable_important_patches,
    vulnerableModeratePatch: item.vulnerable_moderate_patch,
    vulnerableLowPatch: item.vulnerable_low_patch,
    onlyApprovedPatch: item.only_approved_patch,
    enableThirdPartyPatching: item.enable_third_party_patching
  };
};

const transform = (item) => {
  return {
    id: item.id,
    name: item.name,
    created_by_id: item.createdById,
    created_time: item.createdTime,
    updated_by_id: item.updatedById,
    updated_time: item.updatedTime,
    removed: item.removed,
    oob: item.oob,
    patch_approval_policy: item.patchApprovalPolicy,
    enable_patching: item.enablePatching,
    enable_scheduling: item.enableScheduling,
    ...(item.scheduleTime
      ? { schedule_time: Dayjs(Moment.unix(item.scheduleTime).format(FORMAT), FORMAT) }
      : {}),
    ...(item.patchApprovalScheduleTime
      ? {
          patch_approval_schedule_time: Dayjs(
            Moment.unix(item.patchApprovalScheduleTime).format(FORMAT),
            FORMAT
          )
        }
      : {}),
    ...(item.zeroTouchDeploymentScheduleTime
      ? {
          zero_touch_deployment_schedule_time: Dayjs(
            Moment.unix(item.zeroTouchDeploymentScheduleTime).format(FORMAT),
            FORMAT
          )
        }
      : {}),
    enabled_category_list: item.enabledCategoryList,
    enabled_patch_os: item.enabledPatchOs,
    last_sync_date: item.lastSyncDate,
    is_scan_running: item.isScanRunning,
    highly_vulnerable_critical_patch: item.highlyVulnerableCriticalPatch,
    highly_vulnerable_important_patches: item.highlyVulnerableImportantPatches,
    highly_vulnerable_moderate_patch: item.highlyVulnerableModeratePatch,
    highly_vulnerable_low_patch: item.highlyVulnerableLowPatch,
    vulnerable_critical_patch: item.vulnerableCriticalPatch,
    vulnerable_important_patches: item.vulnerableImportantPatches,
    vulnerable_moderate_patch: item.vulnerableModeratePatch,
    vulnerable_low_patch: item.vulnerableLowPatch,
    only_approved_patch: item.onlyApprovedPatch,
    enable_third_party_patching: item.enableThirdPartyPatching,
    is_patch_sync_running: item.isPatchSyncRunning,
    last_patch_sync_time: item.lastPatchSyncTime
  };
};

export function getPatchApprovalPolicyApi() {
  return api.get(END_POINT).then(({ result }) => transform(result));
}

export function updatPatchApprovalPolicyApi(data) {
  return api.put(END_POINT, transformForServer(data, true)).then(() => getPatchApprovalPolicyApi());
}

export function patchSyncApi() {
  return api.post('/patch/execute-patch-sync');
}
