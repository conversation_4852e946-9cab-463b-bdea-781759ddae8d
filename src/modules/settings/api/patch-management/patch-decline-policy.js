import api from '@api';

const END_POINT = `patch/patch-decline-policy`;

const transform = (item, forServer) => {
  return {
    id: item.id,
    name: item.name,
    application_patch_setting: item.applicationPatchSetting.map((i) => i),
    assets: item.assets,
    computer_filter_type: item.computerFilterType,
    computer_group_ids: item.computerGroupIds,
    created_by_id: item.createdById,
    oob: item.oob,
    description: item.description,
    platform: item.platform,
    platform_versions: item.platform_versions,
    removed: item.removed,
    modified_time: item.modified_time,
    created_time: item.created_time,
    scope: item.scope.scope,
    updated_by_id: item.updatedById,
    updated_time: item.updatedTime
  };
};
const transformForServer = (item, forServer) => {
  return {
    name: item.name,
    applicationPatchSetting: item.application_patch_setting.map((i) => i),
    assets: item.assets,
    computerFilterType: item.computer_filter_type,
    computerGroupIds: item.computer_group_ids,
    description: item.description,
    platform: item.platform,
    platform_versions: item.platform_versions,
    removed: item.removed,
    scope: item.scope,
    updatedById: item.updated_by_id,
    updatedTime: item.updated_time
  };
};

const sortKeyMap = {
  name: 'name'
};

const searchableColumns = ['name'];

export function getAllPatchDeclinePolicyApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map((i) => transform(i))
      };
    });
}

export function getPatchDeclinePolicyApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updatePatchDeclinePolicyApi(item) {
  return api
    .put(`${END_POINT}/${item.id}`, transformForServer(item, true))
    .then((data) => getPatchDeclinePolicyApi(data.result));
}

export function createPatchDeclinePolicyApi(item) {
  return api
    .post(`${END_POINT}`, transformForServer(item, true))
    .then((data) => getPatchDeclinePolicyApi(data.result));
}

export function deletePatchDeclinePolicyApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
