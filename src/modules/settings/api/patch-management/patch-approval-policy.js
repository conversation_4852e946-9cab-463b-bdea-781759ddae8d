import api from '@api';

const END_POINT = `/patch/patch-preference`;

const transform = (item, forServer) => {
  return {
    ...(!forServer ? item : {}),
    patchApprovalPolicy: item.patchApprovalPolicy
  };
};

export function getPatchApprovalPolicyApi() {
  return api.get(END_POINT).then(({ result }) => transform(result));
}

export function updatPatchApprovalPolicyApi(data) {
  return api.put(END_POINT, transform(data, true)).then(() => getPatchApprovalPolicyApi());
}
