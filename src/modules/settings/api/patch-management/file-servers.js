import api from '@api';

const END_POINT = `/patch/file-server-config`;

const transform = (item) => ({
  id: item.id,
  name: item.name,
  url: item.url,
  locationId: item.locationId,
  description: item.description,
  createdAt: item.createdTime
});

const transformForServer = async (item) => {
  return Promise.resolve({
    id: item.id,
    name: item.name,
    url: item.url,
    locationId: item.locationId,
    description: item.description,
    createdAt: item.createdTime
  });
};

const sortKeyMap = {};

const searchableColumns = ['name', 'url'];

export function getAllFileServerConfigApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getFileServerApi(id) {
  return api.get(`${END_POINT}/${id}`).then((result) => transform(result));
}

export function updateFileServerApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getFileServerApi(data.result));
}

export function createFileServerApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getFileServerApi(data.result));
}

export function deleteFileServerApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
