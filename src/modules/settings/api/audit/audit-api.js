import api from '@api';
import { transformTimelineForServer } from '@modules/dashboard/widget-api';

const END_POINT = `/settings/audit`;

const transform = (item) => ({
  id: item.id,
  module: item.module,
  user: item.user,
  operation: item.operation,
  status: item.status,
  message: item.message,
  createdAt: item.created_time
});

const sortKeyMap = {
  createdAt: 'created_time'
};

const searchableColumns = ['module', 'operation', 'user', 'message', 'status'];

export function getAllAuditApi(offset, size, sortFilter = {}, filters) {
  return api
    .post(`${END_POINT}`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      ...((filters.timeline || {}).selected
        ? {
            timeline: transformTimelineForServer(filters.timeline)
          }
        : {}),
      ...((filters.selectedModules || []).length
        ? {
            module: filters.selectedModules.join(',')
          }
        : {}),
      ...((filters.selectedUsers || []).length
        ? {
            user: filters.selectedUsers.join(',')
          }
        : {}),
      ...((filters.selectedOperations || []).length
        ? {
            operation: filters.selectedOperations.join(',')
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getAuditFiltersApi() {
  return api.get(`${END_POINT}/filters`).then(({ result }) => {
    return {
      modules: result.modules,
      users: result.users
    };
  });
}
