import api from '@api';

const END_POINT = `/settings/location`;

const transform = (item) => ({
  id: item.id,
  name: item.location_name,
  description: item.description,
  // organization: +item.organization,
  createdAt: item.created_time
});

const transformForServer = (item) => ({
  ...(item.id ? { id: item.id } : {}),
  location_name: item.name,
  description: item.description
  // organization: item.organization
});

const sortKeyMap = {
  name: 'location_name',
  createdAt: 'created_time'
};

const searchableColumns = ['location_name', 'description'];

export function getAllLocationApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getLocationApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateLocation(item) {
  return api
    .put(`${END_POINT}/${item.id}`, transformForServer(item))
    .then(() => getLocationApi(item.id));
}

export function createLocation(item) {
  return api
    .post(`${END_POINT}`, transformForServer(item))
    .then((data) => getLocationApi(data.result));
}

export function deleteLocation(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
