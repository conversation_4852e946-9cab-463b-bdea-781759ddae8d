import api from '@api';

const END_POINT = `/settings/team`;

const transform = (item) => ({
  id: item.id,
  name: item.team_name,
  description: item.description,
  department: +item.department,
  createdAt: item.created_time
});

const transformForServer = (item) => ({
  ...(item.id ? { id: item.id } : {}),
  team_name: item.name,
  description: item.description,
  department: item.department
});

const sortKeyMap = {
  name: 'team_name',
  createdAt: 'created_time'
};

const searchableColumns = ['team_name', 'description'];

export function getAllTeamApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getTeamApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateTeam(item) {
  return api
    .put(`${END_POINT}/${item.id}`, transformForServer(item))
    .then(() => getTeamApi(item.id));
}

export function createTeam(item) {
  return api.post(`${END_POINT}`, transformForServer(item)).then((data) => getTeamApi(data.result));
}

export function deleteTeam(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
