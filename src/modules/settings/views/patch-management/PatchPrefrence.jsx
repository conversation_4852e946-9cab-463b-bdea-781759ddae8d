import { useEffect, useState } from 'react';
import { Form, Spin, Button, Row, Col, Switch, TimePicker, Radio, Input, Divider } from 'antd';

import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Permissions } from '@/src/components/Permissions';
import { useLayout } from '@/src/layouts/Layout';

import {
  getPatchApprovalPolicyApi,
  updatPatchApprovalPolicyApi,
  patchSyncApi
} from '../../api/patch-management/patch-prefrence';
import PlatformPicker from '@/src/components/pickers/PlatformPicker';

// import SettingHeading from '@modules/settings/components/SettingHeading';
import ApiPoller from '@components/common/ApiPoller';

import SettingFormLayout from '@modules/settings/components/SettingFormLayout';
import { useAuth } from '@/src/hooks/auth';

export default function PatchPrefrence({ onDone }) {
  const { message } = useLayout();

  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  const [config, setConfig] = useState({ patchApprovalPolicy: 'manual_approved' });
  const [form] = Form.useForm();
  const { hasPermission } = Permissions.usePermission();
  const { formatDateTime } = useAuth();

  function getPatchPrefrenceApi() {
    return getPatchApprovalPolicyApi().then((response) => {
      setConfig(response);
      form.setFieldsValue(response);
      setLoading(false);
    });
  }

  useEffect(() => {
    getPatchPrefrenceApi();

    // eslint-disable-next-line
  }, []);

  const submitForm = (data) => {
    setProcessing(true);
    updatPatchApprovalPolicyApi(data)
      .then(() => {
        if (onDone) {
          onDone();
        }
      })
      .finally(() => setProcessing(false));
  };

  function onValuesChange(changedValues, allValues) {
    setConfig(allValues);
  }
  function initiatPatchSync() {
    return patchSyncApi().then(() => {
      getPatchPrefrenceApi();

      return message.success(`Patch sync will be handled`);
    });
  }

  return loading ? (
    <div className="flex flex-col min-h-0 flex-1 items-center justify-center">
      <Spin spinning />
    </div>
  ) : (
    <>
      <ApiPoller apiFunction={getPatchApprovalPolicyApi} onUpdate={(data) => setConfig(data)} />
      <SettingFormLayout>
        <Form
          layout="vertical"
          form={form}
          className="h-full px-4"
          disabled={!hasPermission(constants.Update_Settings)}
          onFinish={submitForm}
          onValuesChange={onValuesChange}
          initialValues={config}>
          <Row gutter={32}>
            <Col span={12}>
              <Form.Item label="Enable Patching" name="enable_patching" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="patch_approval_policy"
                label="Patch Approval Policy"
                rules={[{ required: true }]}>
                <Radio.Group
                  options={[
                    { label: 'Pre Approved', value: 'pre_approved' },
                    { label: 'Manually Approve', value: 'manual_approved' },
                    { label: ' Test and Approve', value: 'test_and_approve' }
                  ]}
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label="Consider Only Approved Patch"
                name="only_approved_patch"
                valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label="Enable Third Party Patching"
                name="enable_third_party_patching"
                valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Patch Sync for OS"
                name="enabled_patch_os"
                rules={[{ required: true }]}>
                <PlatformPicker
                  mode="multiple"
                  onChange={(value) => {
                    form.setFieldValue('os', value);
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Schedule Time" name="schedule_time" rules={[{ required: true }]}>
                <TimePicker className="w-full" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label="Patch Approval Schedule Time"
                name="patch_approval_schedule_time"
                rules={[{ required: true }]}>
                <TimePicker className="w-full" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Zero Touch Deployment Schedule Time"
                name="zero_touch_deployment_schedule_time"
                rules={[{ required: true }]}>
                <TimePicker className="w-full" />
              </Form.Item>
            </Col>

            {/* <Divider className="my-4 grow-0" /> */}

            <Row gutter={0} className="w-full px-4">
              <Col span={24}>
                <div className=" text-base rounded-lg py-0 px-2">{`Risk Prefrences`}</div>
              </Col>
            </Row>

            <Divider className=" my-6 mt-2 grow-0" />

            <Col span={12}>
              <Form.Item
                label=" "
                name="highly_vulnerable_critical_patch"
                className="wide-addon-after no-lebel-input">
                <Input
                  placeholder=" "
                  type="number"
                  addonAfter="Missing Critical Severity Patches"
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label=" "
                name="highly_vulnerable_important_patches"
                className="wide-addon-after no-lebel-input">
                <Input
                  placeholder=" "
                  type="number"
                  addonAfter="Missing Important Severity Patches"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label=" "
                name="highly_vulnerable_moderate_patch"
                className="wide-addon-after no-lebel-input">
                <Input
                  placeholder=" "
                  type="number"
                  addonAfter=" Missing Moderate Severity Patches"
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label=" "
                name="highly_vulnerable_low_patch"
                className="wide-addon-after no-lebel-input">
                <Input placeholder=" " type="number" addonAfter=" Missing Low Severity Patches" />
              </Form.Item>
            </Col>
            {/* <Divider className="my-4 grow-0" /> */}

            {/* <Col
                span={24}
                className="text-primary text-base rounded-lg py-4"
                style={{
                  backgroundColor: 'var(--seperator-background-color)'
                }}>{`Criteria for tagging system as "Vulnerable"`}</Col> */}
            {/* <Divider className="my-4 grow-0" /> */}

            {/* <Col span={12}>
                <Form.Item label=" " name="vulnerable_critical_patch" className="wide-addon-after">
                  <Input
                    placeholder=" "
                    type="number"
                    addonAfter=" Missing Critical Severity Patches"
                  />
                </Form.Item>
              </Col> */}

            {/* <Col span={12}>
                <Form.Item
                  label=" "
                  name="vulnerable_important_patches"
                  className="wide-addon-after">
                  <Input
                    placeholder=" "
                    type="number"
                    addonAfter=" Missing Important Severity Patches"
                  />
                </Form.Item>
              </Col> */}

            {/* <Col span={12}>
                <Form.Item label=" " name="vulnerable_moderate_patch" className="wide-addon-after">
                  <Input
                    placeholder=" "
                    type="number"
                    addonAfter=" Missing Moderate Severity Patches"
                  />
                </Form.Item>
              </Col> */}

            {/* <Col span={12}>
                <Form.Item label=" " name="vulnerable_low_patch" className="wide-addon-after">
                  <Input placeholder=" " type="number" addonAfter="Missing Low Severity Patches" />
                </Form.Item>
              </Col> */}

            {/* <Col span={12}>
            <Form.Item label="Enabled Patch Os" name="enabled_patch_os" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col> */}

            <Col span={12} className={'mb-6'}>
              {config.last_patch_sync_time && !config.is_patch_sync_running
                ? `Last Synced At ${formatDateTime(config.last_patch_sync_time)}`
                : ''}
            </Col>

            <PermissionChecker permission={constants.Update_Settings}>
              <Col span={24} className="text-left">
                <Button
                  type="primary"
                  className="mr-2"
                  onClick={initiatPatchSync}
                  loading={config.is_patch_sync_running}>
                  {config.is_patch_sync_running ? 'Syncing ...' : 'Sync Now'}
                </Button>
                <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                  Save
                </Button>

                <Button type="primary" ghost onClick={getPatchPrefrenceApi}>
                  Reset
                </Button>
              </Col>
            </PermissionChecker>
          </Row>
        </Form>
      </SettingFormLayout>
    </>
  );
}
