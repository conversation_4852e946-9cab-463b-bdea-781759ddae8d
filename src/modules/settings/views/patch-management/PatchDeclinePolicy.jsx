import constants from '@/src/constants/index';

import { Button } from 'antd';
import Icon from '@/src/components/Icon';

import PermissionChecker from '@/src/components/PermissionChecker';

import { CrudProvider } from '@/src/hooks/crud';
import { ComputerGroups } from '@/src/components/pickers/ComputerGroupsPicker';

import {
  createPatchDeclinePolicyApi,
  deletePatchDeclinePolicyApi,
  getAllPatchDeclinePolicyApi,
  updatePatchDeclinePolicyApi
} from '../../api/patch-management/patch-decline-policy';

import PatchDeclinePolicyForm from '../../components/patch-management/PatchDeclinePolicyForm';

export default function PatchDeclinePolicy() {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },

    {
      title: 'Created On',
      dataIndex: 'created_time',
      key: 'created_time',
      type: 'datetime',
      hidden: true
    },
    {
      title: 'Modified Time',
      dataIndex: 'modified_time',
      key: 'modified_time',
      type: 'datetime',
      hidden: true
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings],
      render({ record, edit, delete: deleteFn }) {
        return (
          <div>
            <PermissionChecker permission={constants.Update_Settings}>
              <Button
                type="link"
                onClick={() => {
                  edit(record);
                }}
                className="mr-2">
                <Icon name="edit" style={{ fontSize: '1.1rem' }} />
              </Button>
            </PermissionChecker>
            <PermissionChecker permission={constants.Delete_Settings}>
              <Button danger type="text" onClick={() => deleteFn(record)}>
                <Icon name="delete" style={{ fontSize: '1.1rem' }} />
              </Button>
            </PermissionChecker>
          </div>
        );
      }
    }
  ];
  return (
    <ComputerGroups.Provider>
      <CrudProvider
        columns={columns}
        defaultFormItem={{
          computer_filter_type: 'all_cg'
        }}
        resourceTitle="Patch Decline Policy"
        hasSearch
        fetchFn={getAllPatchDeclinePolicyApi}
        deleteFn={deletePatchDeclinePolicyApi}
        createFn={createPatchDeclinePolicyApi}
        updateFn={updatePatchDeclinePolicyApi}
        createSlot={(createFn) => (
          <PermissionChecker permission={constants.Create_Settings}>
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          </PermissionChecker>
        )}
        formActions={({ formItem, resetForm, processingForm }) => (
          <>
            <Button type="primary" loading={processingForm} htmlType="submit" className="mr-2">
              {formItem && formItem.id ? 'Update' : 'Create'}
            </Button>

            <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
              Reset
            </Button>
          </>
        )}
        formFields={(item, _, { disabled }) => (
          <PatchDeclinePolicyForm item={item} disabled={disabled} />
        )}
      />
    </ComputerGroups.Provider>
  );
}
