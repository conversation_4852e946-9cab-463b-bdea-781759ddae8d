import { useEffect, useState } from 'react';
import { Form, Spin, Button, Row, Col, Switch, TimePicker } from 'antd';

import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Permissions } from '@/src/components/Permissions';
import { useLayout } from '@/src/layouts/Layout';

import {
  getPatchApprovalPolicyApi,
  updatPatchApprovalPolicyApi,
  patchSyncApi
} from '../../api/patch-management/patch-prefrence';
import PlatformPicker from '@/src/components/pickers/PlatformPicker';

export default function UpdatePatchDatabase({ onDone }) {
  const { message } = useLayout();

  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  const [config, setConfig] = useState({ patchApprovalPolicy: 'manual_approved' });
  const [form] = Form.useForm();
  const { hasPermission } = Permissions.usePermission();

  useEffect(() => {
    getPatchApprovalPolicyApi().then((response) => {
      setConfig(response);
      form.setFieldsValue(response);
      setLoading(false);
    });
    // eslint-disable-next-line
  }, []);

  const submitForm = (data) => {
    setProcessing(true);
    updatPatchApprovalPolicyApi(data)
      .then(() => {
        if (onDone) {
          onDone();
        }
      })
      .finally(() => setProcessing(false));
  };

  function onValuesChange(changedValues, allValues) {
    setConfig(allValues);
  }
  function initiatPatchSync() {
    return patchSyncApi().then(() => {
      return message.success(`Patch sync will be handled`);
    });
  }

  return loading ? (
    <div className="flex flex-col min-h-0 flex-1 items-center justify-center">
      <Spin spinning />
    </div>
  ) : (
    <div className="flex flex-col min-h-0 p-4">
      <Form
        layout="vertical"
        form={form}
        className="h-full"
        disabled={!hasPermission(constants.Update_Settings)}
        onFinish={submitForm}
        onValuesChange={onValuesChange}
        initialValues={config}>
        <Row gutter={32}>
          <Col span={12}>
            <Form.Item label="Enable Patching" name="enable_patching" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          <Col span={12}></Col>
          <Col span={12}>
            <Form.Item label="Schedule Time" name="schedule_time" rules={[{ required: true }]}>
              <TimePicker className="w-full" />
            </Form.Item>
          </Col>
          <Col span={12}></Col>

          <Col span={12}>
            <Form.Item
              label="Patch Sync for OS"
              name="enabled_patch_os"
              rules={[{ required: true }]}>
              <PlatformPicker
                mode="multiple"
                onChange={(value) => {
                  form.setFieldValue('os', value);
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}></Col>

          <Col span={12}>
            <Form.Item
              label="Enable Third Party Patching"
              name="enable_third_party_patching"
              valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          {/* <Col span={12}>
            <Form.Item label="Enabled Patch Os" name="enabled_patch_os" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col> */}

          <PermissionChecker permission={constants.Update_Settings}>
            <Col span={24} className="text-right">
              <Button type="primary" className="mr-2" onClick={initiatPatchSync}>
                Sync Now
              </Button>
              <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                Save
              </Button>

              <Button type="primary" ghost htmlType="reset">
                Reset
              </Button>
            </Col>
          </PermissionChecker>
        </Row>
      </Form>
    </div>
  );
}
