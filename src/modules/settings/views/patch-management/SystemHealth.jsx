import { useEffect, useState } from 'react';
import { Form, Spin, Button, Row, Col, Input, Divider, Switch } from 'antd';

import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Permissions } from '@/src/components/Permissions';

import {
  getPatchApprovalPolicyApi,
  updatPatchApprovalPolicyApi
} from '../../api/patch-management/patch-prefrence';

export default function SystemHealth({ onDone }) {
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  const [config, setConfig] = useState({});
  const [form] = Form.useForm();
  const { hasPermission } = Permissions.usePermission();

  useEffect(() => {
    getPatchApprovalPolicyApi().then((response) => {
      setConfig(response);
      form.setFieldsValue(response);
      setLoading(false);
    });
    // eslint-disable-next-line
  }, []);

  const submitForm = (data) => {
    setProcessing(true);
    updatPatchApprovalPolicyApi(data)
      .then(() => {
        if (onDone) {
          onDone();
        }
      })
      .finally(() => setProcessing(false));
  };

  function onValuesChange(changedValues, allValues) {
    setConfig(allValues);
  }

  return loading ? (
    <div className="flex flex-col min-h-0 flex-1 items-center justify-center">
      <Spin spinning />
    </div>
  ) : (
    <div className="flex flex-col min-h-0 p-4">
      <Form
        layout="vertical"
        form={form}
        className="h-full"
        disabled={!hasPermission(constants.Update_Settings)}
        onFinish={submitForm}
        onValuesChange={onValuesChange}
        initialValues={config}>
        <Row gutter={32}>
          <Col
            span={24}
            className="text-primary text-base ">{`Criteria for tagging system as "Highly Vulnerable"`}</Col>
          <Divider className="my-4 grow-0" />

          <Col span={12}>
            <Form.Item
              label=" "
              name="highly_vulnerable_critical_patch"
              className="wide-addon-after">
              <Input placeholder=" " type="number" addonAfter="Missing Critical Severity Patches" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              label=" "
              name="highly_vulnerable_important_patches"
              className="wide-addon-after">
              <Input
                placeholder=" "
                type="number"
                addonAfter="Missing Important Severity Patches
"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label=" "
              name="highly_vulnerable_moderate_patch"
              className="wide-addon-after">
              <Input
                placeholder=" "
                type="number"
                addonAfter=" Missing Moderate Severity Patches"
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label=" " name="highly_vulnerable_low_patch" className="wide-addon-after">
              <Input placeholder=" " type="number" addonAfter=" Missing Low Severity Patches" />
            </Form.Item>
          </Col>
          <Divider className="my-4 grow-0" />

          <Col
            span={24}
            className="text-primary text-base ">{`Criteria for tagging system as "Vulnerable"`}</Col>
          <Divider className="my-4 grow-0" />

          <Col span={12}>
            <Form.Item label=" " name="vulnerable_critical_patch" className="wide-addon-after">
              <Input
                placeholder=" "
                type="number"
                addonAfter=" Missing Critical Severity Patches"
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label=" " name="vulnerable_important_patches" className="wide-addon-after">
              <Input
                placeholder=" "
                type="number"
                addonAfter=" Missing Important Severity Patches"
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label=" " name="vulnerable_moderate_patch" className="wide-addon-after">
              <Input
                placeholder=" "
                type="number"
                addonAfter=" Missing Moderate Severity Patches"
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label=" " name="vulnerable_low_patch" className="wide-addon-after">
              <Input placeholder=" " type="number" addonAfter="Missing Low Severity Patches" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              label="Consider Only Approved Patch"
              name="only_approved_patch"
              valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          <PermissionChecker permission={constants.Update_Settings}>
            <Col span={24} className="text-right">
              <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                update
              </Button>

              <Button type="primary" ghost htmlType="reset">
                Reset
              </Button>
            </Col>
          </PermissionChecker>
        </Row>
      </Form>
    </div>
  );
}
