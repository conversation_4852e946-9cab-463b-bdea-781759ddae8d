// import { Fragment, useState } from 'react';

import { Form, Button, Col, Row, Input } from 'antd';

import { CrudProvider } from '@/src/hooks/crud';
// import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';

import {
  createComputerGroupsApi,
  deleteComputerGroupsApi,
  getAllComputerGroupsApi,
  updateComputerGroupsApi
} from '../../api/patch-management/computer-groups';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import Icon from '@/src/components/Icon';
import { Asset } from '@/src/components/pickers/AssetPicker';

export default function ComputerGroups() {
  const form = Form.useFormInstance();

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Created On',
      dataIndex: 'created_time',
      key: 'created_time',
      type: 'datetime',
      hidden: true
    },

    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings],
      render({ record, edit, delete: deleteFn }) {
        return (
          <div>
            <PermissionChecker permission={constants.Update_Settings}>
              <Button
                type="link"
                onClick={() => {
                  edit(record);
                }}
                className="mr-2">
                <Icon name="edit" style={{ fontSize: '1.1rem' }} />
              </Button>
            </PermissionChecker>
            <PermissionChecker permission={constants.Delete_Settings}>
              <Button danger type="text" onClick={() => deleteFn(record)}>
                <Icon name="delete" style={{ fontSize: '1.1rem' }} />
              </Button>
            </PermissionChecker>
          </div>
        );
      }
    }
  ];
  return (
    <Asset.Provider>
      <CrudProvider
        columns={columns}
        defaultFormItem={{}}
        resourceTitle="Computer Groups"
        hasSearch
        fetchFn={getAllComputerGroupsApi}
        deleteFn={deleteComputerGroupsApi}
        createFn={createComputerGroupsApi}
        updateFn={updateComputerGroupsApi}
        createSlot={(createFn) => (
          <PermissionChecker permission={constants.Create_Settings}>
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          </PermissionChecker>
        )}
        formActions={({ formItem, resetForm, processingForm }) => (
          <>
            <Button type="primary" loading={processingForm} htmlType="submit" className="mr-2">
              {formItem && formItem.id ? 'Update' : 'Create'}
            </Button>

            <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
              Reset
            </Button>
          </>
        )}
        formFields={(item, _, { disabled }) => (
          <>
            <Row gutter={32}>
              <Col span={12}>
                <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                  <Input placeholder="Name" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Description" name="description">
                  <Input.TextArea placeholder="Description" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item name="asset_ids" label="Endpoints">
                  <Asset.Picker
                    mode="multiple"
                    value={item.asset_ids}
                    onChange={(asset_ids) => form.setFieldValue(item, asset_ids)}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        )}
      />
    </Asset.Provider>
  );
}
