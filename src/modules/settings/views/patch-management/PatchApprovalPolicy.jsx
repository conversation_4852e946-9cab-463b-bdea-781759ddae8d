import { useEffect, useState } from 'react';
import { Form, Spin, Button, Row, Col, Radio } from 'antd';

import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Permissions } from '@/src/components/Permissions';

import {
  getPatchApprovalPolicyApi,
  updatPatchApprovalPolicyApi
} from '../../api/patch-management/patch-prefrence';

export default function PatchApprovalPolicy({ onDone }) {
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  const [config, setConfig] = useState({ patchApprovalPolicy: 'manual_approved' });
  const [form] = Form.useForm();
  const { hasPermission } = Permissions.usePermission();

  useEffect(() => {
    getPatchApprovalPolicyApi().then((response) => {
      setConfig(response);
      form.setFieldsValue(response);
      setLoading(false);
    });
    // eslint-disable-next-line
  }, []);

  const submitForm = (data) => {
    setProcessing(true);
    updatPatchApprovalPolicyApi(data)
      .then(() => {
        if (onDone) {
          onDone();
        }
      })
      .finally(() => setProcessing(false));
  };

  function onValuesChange(changedValues, allValues) {
    setConfig(allValues);
  }

  return loading ? (
    <div className="flex flex-col min-h-0 flex-1 items-center justify-center">
      <Spin spinning />
    </div>
  ) : (
    <div className="flex flex-col min-h-0 p-4">
      <Form
        layout="vertical"
        form={form}
        className="h-full"
        disabled={!hasPermission(constants.Update_Settings)}
        onFinish={submitForm}
        onValuesChange={onValuesChange}
        initialValues={config}>
        <Row gutter={32}>
          <Col span={12}>
            <Form.Item
              name="patch_approval_policy"
              label="Patch Approval Policy
"
              rules={[{ required: true }]}>
              <Radio.Group
                options={[
                  { label: 'Pre Approved', value: 'pre_approved' },
                  { label: 'Manually Approve', value: 'manual_approved' },
                  { label: ' Test and Approve', value: 'test_and_approve' }
                ]}
              />
            </Form.Item>
          </Col>

          <PermissionChecker permission={constants.Update_Settings}>
            <Col span={24} className="text-right">
              <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                Save
              </Button>

              <Button type="primary" ghost htmlType="reset">
                Reset
              </Button>
            </Col>
          </PermissionChecker>
        </Row>
      </Form>
    </div>
  );
}
