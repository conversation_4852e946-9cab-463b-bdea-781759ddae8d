import { Button, Row, Col, Form, Input } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createFileServerApi,
  deleteFileServerApi,
  getAllFileServerConfigApi,
  updateFileServerApi
} from '../../api/patch-management/file-servers';
import Permission<PERSON>he<PERSON> from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Location } from '@/src/components/pickers/LocationPicker';

export default function FileServers() {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      sortable: false
    },
    {
      title: 'Location',
      dataIndex: 'locationId',
      key: 'locationId',
      sortable: false,
      render({ record }) {
        return <Location.Picker value={record.locationId} disabled textOnly />;
      }
    },
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url'
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  return (
    <Location.Provider>
      <CrudProvider
        columns={columns}
        defaultFormItem={{}}
        resourceTitle="FileServer"
        hasSearch
        fetchFn={getAllFileServerConfigApi}
        deleteFn={deleteFileServerApi}
        createFn={createFileServerApi}
        updateFn={updateFileServerApi}
        createSlot={(createFn) => (
          <PermissionChecker permission={constants.Create_Settings}>
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          </PermissionChecker>
        )}
        formFields={(item) => (
          <>
            <Row gutter={32}>
              <Col span={12}>
                <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                  <Input placeholder="Name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="URL" name="url" rules={[{ required: true }]}>
                  <Input placeholder="URL" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Location" name="locationId" rules={[{ required: true }]}>
                  <Location.Picker />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Description" name="description" rules={[{ required: true }]}>
                  <Input.TextArea placeholder="Description" />
                </Form.Item>
              </Col>
            </Row>
          </>
        )}
      />
    </Location.Provider>
  );
}
