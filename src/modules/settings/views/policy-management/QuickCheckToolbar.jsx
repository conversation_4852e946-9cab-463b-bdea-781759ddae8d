import { Button, Switch } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createQuick<PERSON>heck<PERSON><PERSON>,
  deleteQuick<PERSON>heckA<PERSON>,
  getAllQuickCheckApi,
  testQuickCheck<PERSON>pi,
  updateQuick<PERSON>heck<PERSON><PERSON>
} from '../../api/policy-management/quick-check-toolbar';
import { assetFilterOptions } from '@/src/components/pickers/AssetScopePicker';
import { Asset } from '@/src/components/pickers/AssetPicker';
import { Severity } from '@/src/components/pickers/SeverityPicker';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import SeverityDot from '@/src/components/Severity';
import { User } from '@/src/components/pickers/UserPicker';
import { IntegrationAction } from '@/src/components/pickers/IntegrationActionPicker';
import { Configuration } from '@/src/components/pickers/ConfigurationPicker';
import QuickCheckForm from '../../components/policy-management/QuickCheckForm';
import { TestResourceCreationProvider } from '@/src/components/TestResourceCreationTask';

export default function QuickCheckToolbar() {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      searchable: false,
      render({ record }) {
        return <SeverityDot severity={record.severity} useTag />;
      }
    },
    {
      title: 'Scope',
      dataIndex: 'scope',
      key: 'scope',
      render({ record }) {
        const option = assetFilterOptions.find((d) => d.value === record.scope.assetFilter);
        return option ? option.label : null;
      },
      sortable: false
    },
    {
      title: 'Raise Alert',
      dataIndex: 'raise_alert',
      key: 'raise_alert',
      render({ record }) {
        return record.raise_alert ? 'Yes' : 'No';
      },
      sortable: false
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render({ record, update }) {
        return (
          <Switch
            checked={record.status}
            onChange={(e) => {
              update({ ...record, status: e });
            }}
          />
        );
      },
      sortable: false
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Quick_Checks],
      deletePermissions: [constants.Delete_Quick_Checks]
    }
  ];

  return (
    <User.Provider>
      <Severity.Provider>
        <Asset.Provider>
          <Department.Provider>
            <IntegrationAction.Provider>
              <Configuration.Provider>
                <CrudProvider
                  columns={columns}
                  resourceTitle="Endpoint Vital"
                  hasSearch
                  defaultFormItem={{
                    status: true,
                    command_type: 'cmd',
                    expectedOutput: [
                      {
                        condition: 'and',
                        operator: 'Equal'
                      }
                    ]
                  }}
                  fetchFn={getAllQuickCheckApi}
                  updateFn={updateQuickCheckApi}
                  deleteFn={deleteQuickCheckApi}
                  createFn={createQuickCheckApi}
                  createSlot={(createFn) => (
                    <PermissionChecker permission={constants.Create_Quick_Checks}>
                      <Button type="primary" onClick={createFn}>
                        Create
                      </Button>
                    </PermissionChecker>
                  )}
                  formFields={(item) => (
                    <TestResourceCreationProvider testApiFn={(data) => testQuickCheckApi(data)}>
                      <QuickCheckForm value={item} />
                    </TestResourceCreationProvider>
                  )}
                />
              </Configuration.Provider>
            </IntegrationAction.Provider>
          </Department.Provider>
        </Asset.Provider>
      </Severity.Provider>
    </User.Provider>
  );
}
