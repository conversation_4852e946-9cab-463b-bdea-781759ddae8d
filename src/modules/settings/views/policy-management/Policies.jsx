import { Button, Switch } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createPolicy<PERSON><PERSON>,
  getAllPoliciesApi,
  deletePolicyApi,
  updatePolicyApi
} from '../../api/policy-management/policies';
import { assetFilterOptions } from '@/src/components/pickers/AssetScopePicker';
import { Asset } from '@/src/components/pickers/AssetPicker';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import { Severity } from '@/src/components/pickers/SeverityPicker';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { AssetPolicyAttributes } from '@/src/components/pickers/AssetPolicyAttributePicker';
import { VulnerabilityPolicyAttributes } from '@/src/components/pickers/VulnerabilityAttributePicker';
import { User } from '@/src/components/pickers/UserPicker';
import SeverityDot from '@/src/components/Severity';
import { FimPolicyAttributes } from '@/src/components/pickers/FimPolicyAttributePicker';
import { IntegrationAction } from '@/src/components/pickers/IntegrationActionPicker';
import { Configuration } from '@/src/components/pickers/ConfigurationPicker';
import PolicyForm from '../../components/policy-management/PolicyForm';

export default function Policies() {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      searchable: false,
      render({ record }) {
        return <SeverityDot severity={record.severity} useTag />;
      }
    },
    {
      title: 'Scope',
      dataIndex: 'scope',
      key: 'scope',
      render({ record }) {
        const option = assetFilterOptions.find((d) => d.value === record.scope.assetFilter);
        return option ? option.label : null;
      },
      sortable: false
    },
    {
      title: 'Module',
      dataIndex: 'module',
      key: 'module',
      sortable: false
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render({ record, update }) {
        return (
          <Switch
            checked={record.status}
            onChange={(e) => {
              update({ ...record, status: e });
            }}
          />
        );
      },
      sortable: false
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Alert],
      deletePermissions: [constants.Delete_Alert]
    }
  ];

  return (
    <Severity.Provider>
      <User.Provider>
        <AssetPolicyAttributes.Provider>
          <VulnerabilityPolicyAttributes.Provider>
            <FimPolicyAttributes.Provider>
              <Asset.Provider>
                <Department.Provider>
                  <IntegrationAction.Provider>
                    <Configuration.Provider>
                      <CrudProvider
                        columns={columns}
                        resourceTitle="Alert Configuration"
                        hasSearch
                        defaultFormItem={{
                          module: 'Endpoint',
                          status: true,
                          context: [
                            {
                              join: 'and'
                            }
                          ]
                        }}
                        fetchFn={getAllPoliciesApi}
                        updateFn={updatePolicyApi}
                        deleteFn={deletePolicyApi}
                        createFn={createPolicyApi}
                        createSlot={(createFn) => (
                          <PermissionChecker permission={constants.Create_Alert}>
                            <Button type="primary" onClick={createFn}>
                              Create
                            </Button>
                          </PermissionChecker>
                        )}
                        formFields={(item, update) => {
                          return <PolicyForm item={item} update={update} />;
                        }}
                      />
                    </Configuration.Provider>
                  </IntegrationAction.Provider>
                </Department.Provider>
              </Asset.Provider>
            </FimPolicyAttributes.Provider>
          </VulnerabilityPolicyAttributes.Provider>
        </AssetPolicyAttributes.Provider>
      </User.Provider>
    </Severity.Provider>
  );
}
