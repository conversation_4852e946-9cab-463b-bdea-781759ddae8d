import { Button, Form, Input } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { createTeam, deleteTeam, getAllTeamApi, updateTeam } from '../../api/user-management/team';
import { Fragment } from 'react';
import { Department } from '@/src/components/pickers/DepartmentPicker.jsx';
import constants from '@/src/constants/index';
import PermissionChecker from '@/src/components/PermissionChecker';

export default function Teams() {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
      render({ record }) {
        return <Department.Picker value={record.department} disabled textOnly />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  return (
    <Department.Provider>
      <CrudProvider
        columns={columns}
        resourceTitle="Team"
        hasSearch
        fetchFn={getAllTeamApi}
        deleteFn={deleteTeam}
        createFn={createTeam}
        updateFn={updateTeam}
        createSlot={(createFn) => (
          <PermissionChecker permission={constants.Create_Settings}>
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          </PermissionChecker>
        )}
        formFields={() => (
          <Fragment>
            <Form.Item label="Name" name="name" rules={[{ required: true }]}>
              <Input placeholder="Name" />
            </Form.Item>
            <Form.Item label="Description" name="description" rules={[{ required: true }]}>
              <Input.TextArea placeholder="Description" />
            </Form.Item>
            <Form.Item label="Department" name="department" rules={[{ required: true }]}>
              <Department.Picker />
            </Form.Item>
          </Fragment>
        )}
      />
    </Department.Provider>
  );
}
