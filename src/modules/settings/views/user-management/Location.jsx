import { Button } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createLocation,
  deleteLocation,
  getAllLocationApi,
  updateLocation
} from '../../api/user-management/location';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import LocationForm from '../../components/user-management/LocationForm';
// import SettingHeading from '@modules/settings/components/SettingHeading';

export default function Organization() {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  return (
    <>
      <CrudProvider
        columns={columns}
        resourceTitle="Location"
        hasSearch
        fetchFn={getAllLocationApi}
        deleteFn={deleteLocation}
        createFn={createLocation}
        updateFn={updateLocation}
        createSlot={(createFn) => (
          <PermissionChecker permission={[constants.Create_Settings]}>
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          </PermissionChecker>
        )}
        formFields={() => {
          return <LocationForm />;
        }}
      />
    </>
  );
}
