import { Button } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createDepartment,
  deleteDepartment,
  getAllDepartmentApi,
  updateDepartment
} from '../../api/user-management/department';
import { Organization } from '@/src/components/pickers/OrganizationPicker.jsx';
import constants from '@/src/constants/index';
import Permission<PERSON>hecker from '@/src/components/PermissionChecker';
import DepartmentForm from '../../components/user-management/DepartmentForm';

export default function Department() {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Organization',
      dataIndex: 'organization',
      key: 'organization',
      render({ record }) {
        return <Organization.Picker value={record.organization} disabled textOnly />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  return (
    <Organization.Provider>
      <CrudProvider
        columns={columns}
        resourceTitle="Department"
        hasSearch
        fetchFn={getAllDepartmentApi}
        deleteFn={deleteDepartment}
        createFn={createDepartment}
        updateFn={updateDepartment}
        createSlot={(createFn) => (
          <PermissionChecker permission={constants.Create_Settings}>
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          </PermissionChecker>
        )}
        formFields={() => <DepartmentForm />}
      />
    </Organization.Provider>
  );
}
