import { Button, Form, Input } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createRole,
  deleteRole,
  getAllRoleApi,
  updateRole,
  AVAILABLE_CAPABILITIES
} from '../../api/user-management/roles';
import { Fragment } from 'react';
import TreeTransfer from '@/src/components/TreeTransfer';
import constants from '@/src/constants/index';
import PermissionChecker from '@/src/components/PermissionChecker';
import { License } from '@/src/components/LicenseProvider';

export default function Roles() {
  const { license } = License.useLicense();
  const availableCapabilities = AVAILABLE_CAPABILITIES(license.licenseProduct);
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  return (
    <CrudProvider
      columns={columns}
      resourceTitle="Role"
      hasSearch
      fetchFn={getAllRoleApi}
      deleteFn={deleteRole}
      createFn={createRole}
      updateFn={updateRole}
      createSlot={(createFn) => (
        <PermissionChecker permission={constants.Create_Settings}>
          <Button type="primary" onClick={createFn}>
            Create
          </Button>
        </PermissionChecker>
      )}
      formFields={(item, update, { disabled }) => (
        <Fragment>
          <Form.Item label="Name" name="name" rules={[{ required: true }]}>
            <Input placeholder="Name" />
          </Form.Item>
          <Form.Item label="Description" name="description" rules={[{ required: true }]}>
            <Input.TextArea placeholder="Description" />
          </Form.Item>
          <Form.Item
            label="Capabilities"
            name="capabilities"
            disabled={disabled}
            rules={[{ required: true }]}
            valuePropName="targetKeys">
            <TreeTransfer
              disabled={disabled}
              dataSource={availableCapabilities}
              render={(item) => item.label}
              listStyle={{
                width: '45%',
                height: 350
              }}
            />
          </Form.Item>
        </Fragment>
      )}
    />
  );
}
