import momentTz from 'moment-timezone';
import { Button, Avatar, Space, Drawer, Row, Col, Form } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { CrudProvider } from '@/src/hooks/crud';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import { Role } from '@/src/components/pickers/RolePicker';
import { useAuth } from '@/src/hooks/auth';
import {
  createUser,
  deleteUser,
  getAllUserApi,
  importUsersByCsvApi,
  updateUser
} from '../../api/user-management/users';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import UserForm from '../../components/user-management/UserForm';
import { Organization } from '@/src/components/pickers/OrganizationPicker';
import { useState } from 'react';
import Uploader from '@/src/components/Uploader';
import { useLayout } from '@/src/layouts/Layout';

export default function Users() {
  const { token } = useAuth();
  const [isImproting, startImport] = useState(false);
  const [processing, setProcessing] = useState(false);
  const { notification } = useLayout();

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link',
      render: ({ record, view }) =>
        record.name && (
          <Button type="link" onClick={() => view(record)}>
            <Space size={16}>
              <Avatar
                size={24}
                {...(record.profile
                  ? { src: `/api/download?id=${record.profile[0].ref}&mid=${token.access_token}` }
                  : { icon: <UserOutlined /> })}
              />
              {record.name}
            </Space>
          </Button>
        )
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone'
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  function handleImportUserSubmitted(values) {
    setProcessing(true);
    importUsersByCsvApi(values)
      .then(() => {
        notification.success({
          message: 'Success',
          description: 'File has been uploaded successfully, Users will be imported',
          duration: 4
        });
        startImport(false);
      })
      .finally(() => {
        setProcessing(false);
      });
  }

  return (
    <Organization.Provider>
      <Department.Provider>
        <Role.Provider>
          <CrudProvider
            columns={columns}
            defaultFormItem={{
              status: true,
              timezone: momentTz.tz.guess(),
              login_allow: true,
              asset_assignment_allow: false
            }}
            resourceTitle="User"
            hasSearch
            fetchFn={getAllUserApi}
            deleteFn={deleteUser}
            createFn={createUser}
            updateFn={updateUser}
            createSlot={(createFn) => (
              <PermissionChecker permission={constants.Create_Settings}>
                <Button type="primary" onClick={createFn}>
                  Create
                </Button>
                <Button type="primary" className="ml-2" onClick={() => startImport(true)}>
                  Import
                </Button>
              </PermissionChecker>
            )}
            formFields={(item) => <UserForm item={item} />}
          />
          <Drawer
            title="Import Users"
            placement={'right'}
            width="50%"
            onClose={() => startImport(false)}
            destroyOnClose
            open={isImproting}>
            {isImproting ? (
              <Form onFinish={handleImportUserSubmitted} layout="vertical" className="h-full">
                <Row gutter={32}>
                  <Col span={24} className="mb-4">
                    <div>
                      <h4>Download Sample CSV</h4>
                      <a href="/sample_user.csv">sample_user.csv</a>
                    </div>
                  </Col>
                  <Col span={24}>
                    <Form.Item
                      required={false}
                      label="Select CSV File"
                      name="csv_file"
                      className="flex-1"
                      rules={[{ required: true }]}>
                      <Uploader
                        message="Please select CSV file containing Malicious Hashes"
                        accept="text/csv"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24} className="text-right">
                    <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                      Import
                    </Button>
                    <Button type="primary" ghost htmlType="reset">
                      Reset
                    </Button>
                  </Col>
                </Row>
              </Form>
            ) : null}
          </Drawer>
        </Role.Provider>
      </Department.Provider>
    </Organization.Provider>
  );
}
