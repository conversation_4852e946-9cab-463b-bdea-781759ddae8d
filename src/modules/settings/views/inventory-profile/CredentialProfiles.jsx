import { Button, Tag } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createCredentialProfileApi,
  deleteCredentialProfileApi,
  getAllCredentialProfilesApi,
  updateCredentialProfileApi
} from '../../api/inventory-profile/credential-profile';
import constants from '@/src/constants/index';
import Per<PERSON><PERSON><PERSON><PERSON> from '@/src/components/PermissionChecker';
import CredentialProfileForm from '../../components/inventory-profile/CredentialProfileForm';

export default function CredentialProfiles() {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'SNMP Version',
      dataIndex: 'snmp_version',
      key: 'snmp_version',
      render({ record }) {
        return (
          <Tag color="processing" className="uppercase">
            {record.snmp_version}
          </Tag>
        );
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  return (
    <CrudProvider
      columns={columns}
      resourceTitle="Credential Profile"
      hasSearch
      defaultFormItem={{
        credential_type: 'SNMP',
        snmp_security_level: 'no_auth_no_privacy'
      }}
      fetchFn={getAllCredentialProfilesApi}
      deleteFn={deleteCredentialProfileApi}
      createFn={createCredentialProfileApi}
      updateFn={updateCredentialProfileApi}
      createSlot={(createFn) => (
        <PermissionChecker permission={constants.Create_Settings}>
          <Button type="primary" onClick={createFn}>
            Create
          </Button>
        </PermissionChecker>
      )}
      formFields={() => <CredentialProfileForm />}
    />
  );
}
