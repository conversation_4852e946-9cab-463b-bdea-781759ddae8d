import { <PERSON><PERSON>, Tag, Drawer } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createNetworkProfileApi,
  deleteNetworkProfileApi,
  getAllNetworkProfilesApi,
  updateNetworkProfileApi,
  discoveryAction<PERSON>pi
} from '../../api/inventory-profile/network-profile';
import { EyeOutlined } from '@ant-design/icons';
import constants from '@/src/constants/index';
import PermissionChecker from '@/src/components/PermissionChecker';
import NetworkProfileForm from '../../components/inventory-profile/NetworkProfileForm';
import { CredentialProfile } from '@/src/components/pickers/CredentialProfilePicker';
import { Organization } from '@/src/components/pickers/OrganizationPicker';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import Icon from '@/src/components/Icon';
import { useLayout } from '@/src/layouts/Layout';
import { useState } from 'react';
import NetworkProfileResult from '../../components/inventory-profile/NetworkProfileResult';

export default function NetworkProfiles() {
  const { message } = useLayout();
  const [viewResultFor, setViewResultFor] = useState(null);

  function perfomAction(record, update) {
    return discoveryActionApi(record).then((data) => {
      if (update) update(data);

      message.success(`The Discovery will ${record.running ? 'abort' : 'run'}`);
    });
  }
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Discovery Type',
      dataIndex: 'discovery_type',
      key: 'discovery_type',
      render({ record }) {
        return (
          <Tag color="processing" className="uppercase">
            {record.discovery_type}
          </Tag>
        );
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings],
      prependAction({ record }) {
        return (
          <>
            <Button
              shape="circle"
              type="link"
              onClick={() => setViewResultFor(record)}
              className="mx-2"
              title="View Result">
              <EyeOutlined className="mr-2 text-lg" />
            </Button>
            {record.running ? (
              <>
                <Button
                  shape="circle"
                  type="link"
                  // onClick={() => update({ ...record, forDiscoveryAction: true })}

                  className="mx-2"
                  title="Running">
                  <Icon
                    name="sync"
                    title="Running"
                    className="mr-2 text-lg rotate"
                    style={{ color: 'rgb(254,203,5)' }}
                  />
                </Button>
              </>
            ) : null}
            <Button
              danger={record.running}
              shape="circle"
              type="link"
              // onClick={() => update({ ...record, forDiscoveryAction: true })}
              onClick={() => perfomAction(record)}
              className="mx-2"
              title={record.running ? 'Abort' : 'Run'}>
              <Icon
                name={record.running ? 'kill-process' : 'play'}
                title={record.running ? 'Abort' : 'Run'}
                className="mr-2 text-lg"
              />
            </Button>
          </>
        );
      }
    }
  ];

  return (
    <CredentialProfile.Provider>
      <Organization.Provider>
        <Department.Provider>
          <CrudProvider
            columns={columns}
            resourceTitle="Network Profile"
            hasSearch
            defaultFormItem={{
              discovery_type: 'SNMP',
              ip_type: 'IP',
              port: 161,
              polling_interval: 3600
            }}
            fetchFn={getAllNetworkProfilesApi}
            deleteFn={deleteNetworkProfileApi}
            createFn={createNetworkProfileApi}
            updateFn={updateNetworkProfileApi}
            createSlot={(createFn) => (
              <PermissionChecker permission={constants.Create_Settings}>
                <Button type="primary" onClick={createFn}>
                  Create
                </Button>
              </PermissionChecker>
            )}
            formFields={() => <NetworkProfileForm />}
          />
          {viewResultFor ? (
            <Drawer
              placement={'right'}
              width={'70%'}
              title={`Result for ${viewResultFor.name}`}
              onClose={() => setViewResultFor(null)}
              destroyOnClose
              maskClosable={false}
              open={Boolean(viewResultFor)}>
              <NetworkProfileResult id={viewResultFor.id} />
            </Drawer>
          ) : null}
        </Department.Provider>
      </Organization.Provider>
    </CredentialProfile.Provider>
  );
}
