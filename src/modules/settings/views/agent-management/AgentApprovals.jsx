import { Button, Modal, Form, Input, Tag } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import constants from '@/src/constants/index';
import { CrudProvider } from '@/src/hooks/crud';
import {
  changeApprovalStatusApi,
  getAllApprovalApi
} from '../../api/agent-management/agent-approval';
import PermissionChecker from '@/src/components/PermissionChecker';
import { useState } from 'react';
import { User } from '@/src/components/pickers/UserPicker';

function AgentRejectReason({ onCreate, onCancel }) {
  const [form] = Form.useForm();
  return (
    <Modal
      open={true}
      title="Reject Agent Approval"
      okText="Reject"
      cancelText="Cancel"
      onCancel={onCancel}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            form.resetFields();
            onCreate({ ...values, status: 0 });
          })
          .catch((info) => {
            console.log('Validate Failed:', info);
          });
      }}>
      <Form form={form} layout="vertical" name="form_in_modal">
        <Form.Item name="reject_reason" label="Reason for Rejection" rules={[{ required: true }]}>
          <Input.TextArea rows={3} placeholder="Reason to reject" />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default function AgentApprovals() {
  const [rejectReasonItem, setRejectReasonItem] = useState(null);
  const [crudKey, setCrudKey] = useState(1);

  const columns = [
    {
      title: 'UUID',
      dataIndex: 'uuid',
      key: 'uuid',
      ellipsis: true
    },
    {
      title: 'Host Name',
      dataIndex: 'host_name',
      key: 'host_name',
      ellipsis: true
    },
    {
      title: 'IP Addresses',
      dataIndex: 'ip_addresses',
      key: 'ip_addresses',
      ellipsis: true
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: 'Performed By',
      dataIndex: 'updated_by',
      key: 'updated_by',
      render({ record }) {
        return <User.Picker textOnly value={record.updated_by} disabled />;
      }
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      buttons: [],
      prependAction({ record, update }) {
        return (
          <PermissionChecker permission={constants.Manage_Inventory}>
            <div className="flex items-center">
              {record.status === 0 ? (
                <Tag color="error" title={record.reject_reason}>
                  Rejected
                </Tag>
              ) : null}
              {record.status === 1 ? <Tag color="success">Approved</Tag> : null}
              {record.status === -1 ? (
                <>
                  <Button
                    title="Approve"
                    type="link"
                    onClick={() => handleChangeStatus({ id: record.id, status: 1 })}>
                    <CheckCircleOutlined className="text-success text-lg" />
                  </Button>
                  <Button
                    className="ml-1"
                    title="Reject"
                    type="link"
                    onClick={() => setRejectReasonItem(record)}>
                    <CloseCircleOutlined className="text-danger text-lg" />
                  </Button>
                </>
              ) : null}
            </div>
          </PermissionChecker>
        );
      }
    }
  ];

  function handleChangeStatus(data) {
    changeApprovalStatusApi({ id: data.id, ...data }).then((data) => {
      setCrudKey((key) => key + 1);
      setRejectReasonItem(null);
    });
  }

  return (
    <div className="flex flex-col flex-1 min-h-0">
      <User.Provider>
        <CrudProvider
          columns={columns}
          key={crudKey}
          defaultSort={{
            order: 'descend',
            field: 'modified_time',
            columnKey: 'createdAt'
          }}
          deleteMsgFn={(record) => `Are you sure, you want to reject agent ${record.host_name}`}
          disableColumnSelection
          resourceTitle="Agent Approvals"
          hasSearch
          updateFn={changeApprovalStatusApi}
          fetchFn={getAllApprovalApi}
        />
      </User.Provider>
      {rejectReasonItem ? (
        <AgentRejectReason
          onCancel={() => setRejectReasonItem(null)}
          onCreate={(data) => handleChangeStatus({ ...data, id: rejectReasonItem.id })}
        />
      ) : null}
    </div>
  );
}
