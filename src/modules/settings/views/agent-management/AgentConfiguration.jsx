import { <PERSON>, Spin, Button, Row, Col, InputNumber } from 'antd';
import {
  getAgentConfigApi,
  updateAgentConfigApi
} from '../../api/agent-management/agent-configuration';
import { useEffect, useState } from 'react';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Permissions } from '@/src/components/Permissions';
import SettingFormLayout from '../../components/SettingFormLayout';

export default function AgentRefreshTimes({ onDone }) {
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [config, setConfig] = useState({});
  const [form] = Form.useForm();
  const { hasPermission } = Permissions.usePermission();

  useEffect(() => {
    getAgentConfigApi().then((response) => {
      setConfig(response);
      form.setFieldsValue(response);
      setLoading(false);
    });
    // eslint-disable-next-line
  }, []);

  const submitForm = (data) => {
    setProcessing(true);
    updateAgentConfigApi(data)
      .then(() => {
        if (onDone) {
          onDone();
        }
      })
      .finally(() => setProcessing(false));
  };

  return loading ? (
    <div className="flex flex-col min-h-0 flex-1 items-center justify-center">
      <Spin spinning />
    </div>
  ) : (
    <SettingFormLayout>
      <Form
        layout="vertical"
        form={form}
        className="h-full"
        disabled={!hasPermission(constants.Update_Settings)}
        onFinish={submitForm}
        initialValues={config}>
        <Row gutter={32}>
          <Col span={12}>
            <Form.Item
              label="Allowed Bandwidth to download Files"
              name="max_file_download_speed"
              extra="Use 0 to disable Bandwidth limit"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Bandwidth in KB/sec"
                min={0}
                addonAfter="KB/sec"
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={32}>
          <Col span={12}>
            <Form.Item
              label="Agent Refresh Cycle"
              name="refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Agent Refresh Cycle"
                min={10}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="System Action Refresh Cycle"
              name="system_action_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="System Action Refresh Cycle"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Endpoint Vitals Refresh Cycle"
              name="quick_check_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Endpoint Vitals Refresh Cycle"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Patch Scanning Refresh Cycle"
              name="patch_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Patch Scanning Refresh Cycle"
                min={1000}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="SBOM Refresh Cycle"
              name="sbom_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="SBOM Cycle in Seconds"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Process Refresh Cycle"
              name="process_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Process Cycle in Seconds"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Network Refresh Cycle"
              name="network_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Network Cycle in Seconds"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Certificate Refresh Cycle"
              name="certificate_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Certificate Cycle in Seconds"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Start-up Items Refresh Cycle"
              name="startup_items_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Start-up Items Cycle in Seconds"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Users Refresh Cycle"
              name="users_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Users Cycle in Seconds"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="System Resources Refresh Cycle"
              name="resources_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Users Cycle in Seconds"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="System Services Refresh Cycle"
              name="service_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="System Services Cycle in Seconds"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="FIM Events Refresh Cycle"
              name="file_events_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="FIM Events Cycle in Seconds"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Software Meter Refresh Cycle"
              name="software_meter_refresh_cycle"
              rules={[{ required: true }]}>
              <InputNumber
                className="w-full"
                placeholder="Software Meter Cycle in Seconds"
                min={5}
                addonAfter="Seconds"
              />
            </Form.Item>
          </Col>
          <PermissionChecker permission={constants.Update_Settings}>
            <Col span={24} className="text-left">
              <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                Save
              </Button>
              <Button type="primary" ghost htmlType="reset">
                Reset
              </Button>
            </Col>
          </PermissionChecker>
        </Row>
      </Form>
    </SettingFormLayout>
  );
}
