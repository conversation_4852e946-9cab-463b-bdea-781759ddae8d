import { Tag, Form, Button } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { CrudProvider } from '@/src/hooks/crud';
import constants from '@/src/constants/index';
import { getAllAgentBuilds, updateAgentBuildApi } from '../../api/agent-management/agent-builds';
import Icon from '@/src/components/Icon';
import Uploader from '@/src/components/Uploader';
import PermissionChecker from '@/src/components/PermissionChecker';

export default function AgentBuilds() {
  const columns = [
    {
      title: 'Platform',
      dataIndex: 'agent_platform',
      key: 'agent_platform',
      render({ record }) {
        return (
          <div className="flex items-center">
            <Icon
              name={`platform_${record.agent_platform.toLowerCase()}`}
              title={record.agent_platform}
              className="text-lg mr-2"
            />
            {record.agent_platform}
          </div>
        );
      }
    },
    {
      title: 'Architecture',
      dataIndex: 'agent_arch',
      key: 'agent_arch',
      render({ record }) {
        return <Tag color="processing">{record.agent_arch}</Tag>;
      }
    },
    {
      title: 'Version',
      dataIndex: 'agent_version',
      key: 'agent_version'
    },
    {
      title: 'Last Updated At',
      dataIndex: 'modified_time',
      key: 'modified_time',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      align: 'right',
      deletePermissions: [constants.Manage_Inventory],
      buttons: [],
      appendAction({ record, edit }) {
        return (
          <PermissionChecker permission={[constants.Manage_Inventory]}>
            <Button title="Upload" type="link" onClick={() => edit(record)}>
              <UploadOutlined style={{ fontSize: '1.1rem' }} />
            </Button>
          </PermissionChecker>
        );
      }
    }
  ];

  return (
    <CrudProvider
      columns={columns}
      resourceTitle="Agent Builds"
      hasSearch
      fetchFn={getAllAgentBuilds}
      updateFn={updateAgentBuildApi}
      formFields={() => (
        <div>
          <Form.Item label="Agent Zip" rules={[{ required: true }]} name="zip" className="flex-1">
            <Uploader accept="application/zip" message="Please Upload Build for updated Agent" />
          </Form.Item>
        </div>
      )}
    />
  );
}
