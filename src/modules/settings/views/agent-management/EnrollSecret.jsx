import { But<PERSON> } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { Department } from '@/src/components/pickers/DepartmentPicker';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Organization } from '@/src/components/pickers/OrganizationPicker';
import {
  createEnrollSecretApi,
  deleteEnrollSecretApi,
  getAllEnrollSecretApi
} from '../../api/agent-management/enroll-secret';
import EnrollSecretForm from '../../components/agent-management/EnrollSecretForm';

export default function EnrollSecret() {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Secret',
      dataIndex: 'secret',
      key: 'secret',
      className: 'select-none',
      ellipsis: true
    },
    {
      title: 'Organization',
      dataIndex: 'organization',
      key: 'organization',
      render({ record }) {
        return <Organization.Picker disabled textOnly value={record.organization} />;
      }
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
      render({ record }) {
        return <Department.Picker disabled textOnly value={record.department} />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      deletePermissions: [constants.Manage_Inventory],
      buttons: ['delete']
    }
  ];

  return (
    <Department.Provider>
      <Organization.Provider>
        <CrudProvider
          columns={columns}
          resourceTitle="Enroll Secret"
          hasSearch
          fetchFn={getAllEnrollSecretApi}
          deleteFn={deleteEnrollSecretApi}
          createFn={createEnrollSecretApi}
          createSlot={(createFn) => (
            <PermissionChecker permission={constants.Manage_Inventory}>
              <Button type="primary" onClick={createFn}>
                Create
              </Button>
            </PermissionChecker>
          )}
          formFields={() => <EnrollSecretForm />}
        />
      </Organization.Provider>
    </Department.Provider>
  );
}
