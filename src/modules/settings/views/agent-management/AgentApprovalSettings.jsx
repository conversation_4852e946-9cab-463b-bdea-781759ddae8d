import { Form, Spin, Button, Row, Col, Radio, Select, Divider, Input } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import {
  getAgentApprovalSettingsApi,
  updateAgentApprovalSettingsApi
} from '../../api/agent-management/agent-configuration';
import { useEffect, useState } from 'react';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Permissions } from '@/src/components/Permissions';
import Repeater from '@/src/components/Repeater';
import Icon from '@/src/components/Icon';
import SettingFormLayout from '../../components/SettingFormLayout';

export default function AgentApprovalSettings({ onDone }) {
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [config, setConfig] = useState({});
  const [form] = Form.useForm();
  const { hasPermission } = Permissions.usePermission();

  const conditionOptions = [
    { label: 'AND', value: 'and' },
    { label: 'OR', value: 'or' }
  ];
  const attributeOptions = ['IP Address', 'Host Name'].map((i) => ({ value: i, label: i }));
  const operatorOptions = [
    'Equal',
    'Contains',
    'NotContains',
    'StartWith',
    'EndWith',
    'Between'
  ].map((i) => ({ value: i, label: i }));

  useEffect(() => {
    getAgentApprovalSettingsApi().then((response) => {
      setConfig(response);
      form.setFieldsValue(response);
      setLoading(false);
    });
    // eslint-disable-next-line
  }, []);

  const submitForm = (data) => {
    setProcessing(true);
    updateAgentApprovalSettingsApi(data)
      .then(() => {
        if (onDone) {
          onDone();
        }
      })
      .finally(() => setProcessing(false));
  };

  return loading ? (
    <div className="flex flex-col min-h-0 flex-1 items-center justify-center">
      <Spin spinning />
    </div>
  ) : (
    <SettingFormLayout span={24}>
      <Form
        layout="vertical"
        form={form}
        className="h-full"
        disabled={!hasPermission(constants.Update_Settings)}
        onFinish={submitForm}
        initialValues={config}>
        <Row gutter={32}>
          <Col span={12}>
            <Form.Item label="Approval Type" name="approval_type" rules={[{ required: true }]}>
              <Radio.Group
                optionType="button"
                buttonStyle="solid"
                size="default"
                onChange={(event) => {
                  form.setFieldValue('approval_type', event.target.value);
                  form.setFieldValue('approval_auto_type', 'all');
                }}>
                <Radio value="auto">Auto</Radio>
                <Radio value="manual">Manual</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Form.Item noStyle dependencies={['approval_type']}>
            {() =>
              form.getFieldValue('approval_type') === 'auto' ? (
                <Col span={24}>
                  <Form.Item
                    label="Auto Approval Based on"
                    name="approval_auto_type"
                    rules={[{ required: true }]}>
                    <Radio.Group
                      optionType="button"
                      buttonStyle="solid"
                      size="default"
                      onChange={(event) => {
                        form.setFieldValue('approval_auto_type', event.target.value);
                        if (event.target.value === 'criteria') {
                          if ((form.getFieldValue('approval_criteria') || []).length === 0) {
                            form.setFieldValue('approval_criteria', [
                              {
                                condition: 'and',
                                operator: 'Equal',
                                attribute: 'IP Address'
                              }
                            ]);
                          }
                        }
                      }}>
                      <Radio value="all">All</Radio>
                      <Radio value="criteria">Criteria</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              ) : null
            }
          </Form.Item>

          <Form.Item noStyle dependencies={['approval_auto_type', 'approval_type']}>
            {() =>
              form.getFieldValue('approval_auto_type') === 'criteria' &&
              form.getFieldValue('approval_type') === 'auto' ? (
                <Col span={24}>
                  <Repeater
                    name={['approval_criteria']}
                    defaultItem={{
                      attribute: 'IP Address',
                      condition: 'and',
                      operator: 'Equal'
                    }}
                    addBtnText={`Add New Criteria`}>
                    {({ key, name, index, ...restField }, actions) => (
                      <div className="flex flex-col" key={key}>
                        <div className="flex items-center">
                          <div
                            className={`flex-1 mr-2 px-2 py-1 rounded border-solid border-border bg-border`}>
                            <Row gutter={8}>
                              <Col span={6}>
                                <div className="flex flex-1">
                                  <Form.Item
                                    {...restField}
                                    label="Compare"
                                    name={[name, 'attribute']}
                                    className="flex-1"
                                    rules={[{ required: true }]}>
                                    <Select
                                      placeholder={'Select Attribute'}
                                      options={attributeOptions}
                                    />
                                  </Form.Item>
                                </div>
                              </Col>
                              <Col span={6}>
                                <div className="flex flex-1">
                                  <Form.Item
                                    {...restField}
                                    label="Operator"
                                    name={[name, 'operator']}
                                    className="flex-1"
                                    rules={[{ required: true }]}>
                                    <Select
                                      placeholder={'Select Operator'}
                                      options={operatorOptions}
                                    />
                                  </Form.Item>
                                </div>
                              </Col>
                              <Col span={12}>
                                <div className="flex flex-1">
                                  <Form.Item
                                    {...restField}
                                    label="Value"
                                    name={[name, 'value']}
                                    className="flex-1"
                                    rules={[{ required: true }]}>
                                    <Input placeholder={'Value'} />
                                  </Form.Item>
                                </div>
                              </Col>
                            </Row>
                          </div>
                          <div className="flex-shrink-0 flex">
                            <Button
                              shape="circle"
                              className="mr-1"
                              title="Move Up"
                              disabled={index === 0}
                              onClick={() => actions.move(index, index - 1)}>
                              <ArrowUpOutlined />
                            </Button>
                            <Button
                              shape="circle"
                              className="mr-1"
                              title="Move Down"
                              disabled={index === actions.length - 1}
                              onClick={() => actions.move(index, index + 1)}>
                              <ArrowDownOutlined />
                            </Button>
                            <Button
                              shape="circle"
                              type="danger"
                              style={{ visibility: name === 0 ? 'hidden' : 'visible' }}
                              onClick={() => actions.remove(name)}>
                              <Icon
                                name="close"
                                className="text-danger"
                                style={{ fontSize: '1.5rem' }}
                              />
                            </Button>
                          </div>
                        </div>
                        {index + 1 < actions.length ? (
                          <Divider className="my-1">
                            <Form.Item
                              label=" "
                              {...restField}
                              name={[name, 'condition']}
                              noStyle
                              rules={[{ required: true }]}>
                              <Select options={conditionOptions} placeholder="Condition Type" />
                            </Form.Item>
                          </Divider>
                        ) : null}
                      </div>
                    )}
                  </Repeater>
                </Col>
              ) : null
            }
          </Form.Item>

          <PermissionChecker permission={constants.Update_Settings}>
            <Col span={24} className="text-left mt-4">
              <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                Save
              </Button>
              <Button type="primary" ghost htmlType="reset">
                Reset
              </Button>
            </Col>
          </PermissionChecker>
        </Row>
      </Form>
    </SettingFormLayout>
  );
}
