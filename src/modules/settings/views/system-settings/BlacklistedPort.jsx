import { Button, Form, Input, Row, Col, Switch, Select, Tag } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import {
  createPort<PERSON>pi,
  deletePort<PERSON>pi,
  getAllPortsApi,
  updatePort<PERSON>pi
} from '../../api/system-settings/blacklisted-port';
import { IntegrationAction } from '@/src/components/pickers/IntegrationActionPicker';
import { Severity } from '@/src/components/pickers/SeverityPicker';
import { Configuration } from '@/src/components/pickers/ConfigurationPicker';

export default function BlackListedPorts() {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Ports',
      dataIndex: 'ports',
      key: 'ports',
      render({ record }) {
        return record.ports.map((i) => <Tag key={i}>{i}</Tag>);
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      align: 'center',
      render({ record, update }) {
        return (
          <Switch
            checked={record.status}
            onChange={(e) => {
              update({ ...record, status: e });
            }}
          />
        );
      },
      sortable: false
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime',
      sortable: false
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  return (
    <Severity.Provider>
      <IntegrationAction.Provider>
        <Configuration.Provider>
          <CrudProvider
            columns={columns}
            resourceTitle="Blacklisted Port"
            hasSearch
            defaultFormItem={{
              status: 1,
              raise_alert: 1
            }}
            fetchFn={getAllPortsApi}
            deleteFn={deletePortApi}
            createFn={createPortApi}
            updateFn={updatePortApi}
            createDrawerLabel="Add"
            createSlot={(createFn) => (
              <PermissionChecker permission={constants.Create_Settings}>
                <Button type="primary" onClick={createFn}>
                  Add Blacklisted Port
                </Button>
              </PermissionChecker>
            )}
            formFields={(item, _, { disabled }) => (
              <>
                <Row gutter={32}>
                  <Col span={24}>
                    <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                      <Input placeholder="Name" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Status" valuePropName="checked" name="status">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Raise Alert" valuePropName="checked" name="raise_alert">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label="Ports" name="ports" rules={[{ required: true }]}>
                      <Select placeholder="Ports ex. 5000, 8000-8010" mode="tags" />
                    </Form.Item>
                  </Col>
                  {item.raise_alert ? (
                    <>
                      <Col span={6}>
                        <Form.Item label="Severity" name="severity" rules={[{ required: true }]}>
                          <Severity.Picker placeholder="Severity" />
                        </Form.Item>
                      </Col>
                      <Col span={18}>
                        <Form.Item label="Add Actions" name="actions">
                          <IntegrationAction.Picker mode="multiple" />
                        </Form.Item>
                      </Col>
                      <Col span={24}>
                        <Form.Item
                          label="Add Remidiations"
                          name="remediations"
                          valuePropName={'targetKeys'}>
                          <Configuration.Picker mode="multiple" />
                        </Form.Item>
                      </Col>
                    </>
                  ) : null}
                </Row>
              </>
            )}
          />
        </Configuration.Provider>
      </IntegrationAction.Provider>
    </Severity.Provider>
  );
}
