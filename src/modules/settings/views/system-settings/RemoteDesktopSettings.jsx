import { useEffect, useState } from 'react';
import Merge from 'lodash/merge';

import { Form, Spin, Button, Row, Col, Input, Switch, Radio } from 'antd';

import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Permissions } from '@/src/components/Permissions';

import {
  getRemoteDesktopSettingsApi,
  updateRemoteDesktopSettingsApi,
  testRemoteDesktopSettingsApi
} from '../../api/system-settings/remote-desktop-settings';
import SettingFormLayout from '@modules/settings/components/SettingFormLayout';

export default function RemoteDesktopSettings({ onDone }) {
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [testStatus, setTestStatus] = useState({});

  const [config, setConfig] = useState({});
  const [form] = Form.useForm();
  const { hasPermission } = Permissions.usePermission();

  useEffect(() => {
    getRemoteDesktopSettingsApi().then((response) => {
      setConfig(response);
      form.setFieldsValue(response);
      setLoading(false);
    });
    // eslint-disable-next-line
  }, []);

  const submitForm = (data) => {
    setTestStatus({});

    setProcessing(true);
    updateRemoteDesktopSettingsApi(data)
      .then(() => {
        if (onDone) {
          onDone();
        }
      })
      .finally(() => setProcessing(false));
  };

  const handleTriggerTest = () => {
    setTestStatus({});
    form
      .validateFields()
      .then(() => {
        onInitiateTest();
      })
      .catch((info) => {
        console.log('Validate Failed:', info);
      });
  };

  const onInitiateTest = () => {
    setProcessing(true);
    testRemoteDesktopSettingsApi({
      ...form.getFieldsValue()
    })
      .then((data) => {
        setProcessing(false);
        setTestStatus({
          status: 'success',
          message: data.message
        });
      })
      .catch((e) => {
        setProcessing(false);
        setTestStatus({
          status: 'error',
          message: e.response.data['response-message']
        });
      });
  };

  function onValuesChange(changedValues, allValues) {
    setConfig(Merge({ ...config }, { ...changedValues }));
  }

  return loading ? (
    <div className="flex flex-col min-h-0 flex-1 items-center justify-center">
      <Spin spinning />
    </div>
  ) : (
    <SettingFormLayout>
      <Form
        layout="vertical"
        form={form}
        className="h-full"
        disabled={!hasPermission(constants.Update_Settings)}
        onFinish={submitForm}
        onValuesChange={onValuesChange}
        initialValues={config}>
        <Row gutter={32}>
          <Col span={24}>
            <Form.Item label="Connetion Type" name="connection_type" rules={[{ required: true }]}>
              <Radio.Group optionType="button">
                <Radio value="local">Local</Radio>
                <Radio value="remote">Remote</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Remote Session Indicator"
              name="remote_session_indicator"
              valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="User Consent" name="user_consent" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>

          {config.connection_type === 'remote' ? (
            <>
              <Col span={12}>
                <Form.Item label="Server FQDN" name="server_fqdn" rules={[{ required: true }]}>
                  <Input placeholder="Server fqdn" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Server IP Address"
                  name="server_ip_address"
                  rules={[{ required: true }]}>
                  <Input placeholder="Server Ip Address" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="SSH Port" name="ssh_port" rules={[{ required: true }]}>
                  <Input placeholder="SSH Port" />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item label="Auth Type" name="auth_type" rules={[{ required: true }]}>
                  <Radio.Group optionType="button">
                    <Radio value="password">Password</Radio>
                    <Radio value="ssh_key">SSH Key</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item label="Username" name="username" rules={[{ required: true }]}>
                  <Input placeholder="Username" />
                </Form.Item>
              </Col>
              <Col span={12}></Col>
            </>
          ) : null}

          {config.auth_type === 'password' && config.connection_type === 'remote' ? (
            <>
              <Col span={12}>
                <Form.Item label="Password" name="password" rules={[{ required: true }]}>
                  <Input placeholder="Password" type="password" />
                </Form.Item>
              </Col>
            </>
          ) : null}

          {config.auth_type === 'ssh_key' && config.connection_type === 'remote' ? (
            <>
              <Col span={12}>
                <Form.Item label="SSH key" name="ssh_key" rules={[{ required: true }]}>
                  <Input.TextArea placeholder="SSH key" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="SSH Passphrase"
                  name="ssh_passphrase"
                  rules={[{ required: true }]}>
                  <Input placeholder="SSH Passphrase" type="password" />
                </Form.Item>
              </Col>
            </>
          ) : null}

          {testStatus.status ? (
            <Col span={24}>
              <div className={testStatus.status === 'error' ? 'text-danger' : 'text-success'}>
                {testStatus.message}
              </div>
            </Col>
          ) : null}

          <PermissionChecker permission={constants.Update_Settings}>
            <Col span={24} className="text-left">
              <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                Save
              </Button>

              {config.connection_type === 'remote' ? (
                <Button
                  ghost
                  type="primary"
                  loading={processing}
                  htmlType="button"
                  className="mr-2"
                  onClick={handleTriggerTest}>
                  Test
                </Button>
              ) : null}

              <Button type="primary" ghost htmlType="reset">
                Reset
              </Button>
            </Col>
          </PermissionChecker>
        </Row>
      </Form>
    </SettingFormLayout>
  );
}
