import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Form, Checkbox } from 'antd';
import { useEffect, useState } from 'react';
import Merge from 'lodash/merge';
import { getRiskSettingsApi, updateRiskSettingApi } from '../../api/system-settings/risk-settings';
import SettingFormLayout from '@modules/settings/components/SettingFormLayout';

export default function RiskSetting() {
  const [riskSetting, setRiskSetting] = useState({});
  const [loading, setLoading] = useState(true);
  const [processingForm, setProcessing] = useState(false);

  useEffect(() => {
    getRiskSettingsApi().then((riskSetting) => {
      setRiskSetting({ ...riskSetting });
      setLoading(false);
    });
  }, []);

  function submitForm() {
    setProcessing(true);
    updateRiskSettingApi({
      ...(riskSetting.useDefault
        ? {
            ...riskSetting,
            id: 0
          }
        : {
            ...riskSetting,
            id: 1
          })
    })
      .then(() => {
        getRiskSettingsApi().then((riskSetting) => {
          setRiskSetting(riskSetting);
        });
      })
      .finally(() => setProcessing(false));
  }

  return loading ? (
    <div className="flex-1 flex items-center justify-center h-full">
      <Spin spinning />
    </div>
  ) : (
    <SettingFormLayout title="Risk Preferences">
      <Form
        layout="vertical"
        className="h-full"
        onFinish={submitForm}
        initialValues={riskSetting}
        onValuesChange={(values) => {
          setRiskSetting(Merge({ ...riskSetting }, { ...values }));
        }}>
        <div className="py-2 mb-4">
          <Form.Item name="useDefault" valuePropName="checked" className="mb-0">
            <Checkbox>
              <span className="font-semibold ml-2">
                Apply Default settings to calculate ZiroScore
              </span>
            </Checkbox>
          </Form.Item>
        </div>
        <Row gutter={32}>
          <Col span={12}>
            <div className="px-4 rounded-lg py-2 bg-seperator mb-8">
              <div>Vulnerability Score Weight</div>
              <div className="flex flex-col w-full">
                <div className="flex items-center w-full">
                  <div className="font-semibold flex-shrink-0 mb-[24px] text-base mr-2">0</div>
                  <Form.Item
                    className="flex-1"
                    label=""
                    name="scoreWeight"
                    rules={[{ required: true }]}>
                    <Slider
                      step={0.01}
                      min={0}
                      max={1}
                      className="w-full"
                      precision={2}
                      disabled={riskSetting.useDefault}
                    />
                  </Form.Item>
                  <div className="font-semibold flex-shrink-0 mb-[24px] text-base ml-4">1</div>
                  <Tag
                    size="large"
                    className="mb-[24px] bg-zirozen-red text-base ml-16 flex-shrink-0 w-14 text-center"
                    color="error">
                    {riskSetting.scoreWeight}
                  </Tag>
                </div>
              </div>
            </div>
          </Col>
          <Col span={12}>
            <div className="px-4 rounded-lg py-2 bg-seperator mb-8">
              <div>Vulnerability Severity Weight</div>
              <div className="flex flex-col w-full">
                <div className="flex items-center w-full">
                  <div className="font-semibold mb-[24px] text-base mr-2">0</div>
                  <Form.Item
                    className="flex-1"
                    label=""
                    name="severityWeight"
                    rules={[{ required: true }]}>
                    <Slider
                      disabled={riskSetting.useDefault}
                      step={0.01}
                      min={0}
                      max={1}
                      className="w-full"
                      precision={2}
                    />
                  </Form.Item>
                  <div className="font-semibold mb-[24px] text-base ml-4">1</div>
                  <Tag
                    size="large"
                    className="mb-[24px] bg-zirozen-red text-base ml-16 w-14 text-center"
                    color="error">
                    {riskSetting.severityWeight}
                  </Tag>
                </div>
              </div>
            </div>
          </Col>
          <Col span={12}>
            <div className="px-4 rounded-lg py-2 bg-seperator mb-8">
              <div>Threats Weight</div>
              <div className="flex flex-col w-full">
                <div className="flex items-center w-full">
                  <div className="font-semibold mb-[24px] text-base mr-2">0</div>
                  <Form.Item
                    className="flex-1"
                    label=""
                    name="threatsWeight"
                    rules={[{ required: true }]}>
                    <Slider
                      disabled={riskSetting.useDefault}
                      step={0.01}
                      min={0}
                      max={1}
                      className="w-full"
                      precision={2}
                    />
                  </Form.Item>
                  <div className="font-semibold mb-[24px] text-base ml-4">1</div>
                  <Tag
                    size="large"
                    className="mb-[24px] bg-zirozen-red text-base ml-16 w-14 text-center"
                    color="error">
                    {riskSetting.threatsWeight}
                  </Tag>
                </div>
              </div>
            </div>
          </Col>
          <Col span={12}>
            <div className="px-4 rounded-lg py-2 bg-seperator mb-8">
              <div>Endpoint Vitals Weight</div>
              <div className="flex flex-col w-full">
                <div className="flex items-center w-full">
                  <div className="font-semibold mb-[24px] text-base mr-2">0</div>
                  <Form.Item
                    className="flex-1"
                    label=""
                    name="quickCheckWeight"
                    rules={[{ required: true }]}>
                    <Slider
                      disabled={riskSetting.useDefault}
                      step={0.01}
                      min={0}
                      max={1}
                      size="large"
                      className="w-full"
                      precision={2}
                    />
                  </Form.Item>
                  <div className="font-semibold mb-[24px] text-base ml-4">1</div>
                  <Tag
                    size="large"
                    className="mb-[24px] bg-zirozen-red text-base ml-16 w-14 text-center"
                    color="error">
                    {riskSetting.quickCheckWeight}
                  </Tag>
                </div>
              </div>
            </div>
          </Col>
          <Col span={24} className="text-left">
            <Button
              size="large"
              type="primary"
              loading={processingForm}
              htmlType="submit"
              className="mr-2">
              Save
            </Button>
          </Col>
        </Row>
      </Form>
    </SettingFormLayout>
  );
}
