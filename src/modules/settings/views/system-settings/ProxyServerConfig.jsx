import { Form, InputN<PERSON>ber, Spin, Select, Button, Row, Col, Input, Switch } from 'antd';
// import Merge from 'lodash/merge';
import {
  getProxyServerConfigApi,
  testProxyServerConfigApi,
  updateProxyServerConfigApi
} from '../../api/system-settings/proxy-server-config';
import { useEffect, useState } from 'react';
import TestProxyServerConfig from '../../components/system-settings/TestProxyServerConfig';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Permissions } from '@/src/components/Permissions';
import SettingFormLayout from '@modules/settings/components/SettingFormLayout';

export default function ProxyServerConfig() {
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [isModalopen, setIsModalopen] = useState(false);
  const [config, setConfig] = useState({});
  const [defaultConfig, setDefaultConfig] = useState({});
  const [form] = Form.useForm();
  const [testStatus, setTestStatus] = useState({});
  const { hasPermission } = Permissions.usePermission();

  useEffect(() => {
    getProxyServerConfigApi().then((response) => {
      setConfig(response);
      setDefaultConfig({ ...response });
      form.setFieldsValue(response);
      setLoading(false);
    });
    // eslint-disable-next-line
  }, []);

  const submitForm = (data) => {
    setTestStatus({});
    setProcessing(true);
    updateProxyServerConfigApi(data).finally(() => setProcessing(false));
  };

  const handleTriggerTest = () => {
    setTestStatus({});
    form
      .validateFields()
      .then(() => {
        setIsModalopen(true);
      })
      .catch((info) => {
        console.log('Validate Failed:', info);
      });
  };

  const onInitiateTest = (data) => {
    setProcessing(true);
    setIsModalopen(false);
    testProxyServerConfigApi({
      ...form.getFieldsValue(),
      ...data
    })
      .then((data) => {
        setProcessing(false);
        setTestStatus({
          status: 'success',
          message: data.message
        });
      })
      .catch((e) => {
        setProcessing(false);
        setTestStatus({
          status: 'error',
          message: e.response.data['response-message']
        });
      });
  };

  const proxyTypeOptions = ['HTTP', 'SOCKS4', 'SOCKS5'].map((p) => ({ label: p, value: p }));

  function onValuesChange(changedValues, allValues) {
    setConfig(allValues);
  }

  return loading ? (
    <div className="flex flex-col min-h-0 flex-1 items-center justify-center">
      <Spin spinning />
    </div>
  ) : (
    <SettingFormLayout>
      <Form
        layout="vertical"
        form={form}
        className="h-full"
        disabled={!hasPermission(constants.Update_Settings)}
        onFinish={submitForm}
        onValuesChange={onValuesChange}
        initialValues={defaultConfig}>
        <Row gutter={32}>
          <Col span={24}>
            <Form.Item label="Enable Proxy Server" name="proxy_enable" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          {config.proxy_enable ? (
            <>
              <Col span={12}>
                <Form.Item label="Proxy Host" name="proxy_host" rules={[{ required: true }]}>
                  <Input placeholder="Proxy Host" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Proxy Port" name="proxy_port" rules={[{ required: true }]}>
                  <InputNumber
                    min={1}
                    max={65536}
                    precision={0}
                    placeholder="Proxy Port"
                    className="w-full"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Proxy Type" name="proxy_type" rules={[{ required: true }]}>
                  <Select placeholder="Proxy Type" options={proxyTypeOptions} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Timeout" name="timeout" rules={[{ required: true }]}>
                  <InputNumber precision={0} className="w-full" placeholder="Timeout" min={1} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Enable Authentication"
                  name="proxy_enable_authentication"
                  valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={24}></Col>
              {config.proxy_enable_authentication ? (
                <>
                  <Col span={12}>
                    <Form.Item label="Username" name="username" rules={[{ required: true }]}>
                      <Input placeholder="Username" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="Password" name="password" rules={[{ required: true }]}>
                      <Input placeholder="Password" type="password" />
                    </Form.Item>
                  </Col>
                </>
              ) : null}
            </>
          ) : null}

          {testStatus.status ? (
            <Col span={24}>
              <div className={testStatus.status === 'error' ? 'text-danger' : 'text-success'}>
                {testStatus.message}
              </div>
            </Col>
          ) : null}
          <PermissionChecker permission={constants.Update_Settings}>
            <Col span={24} className="text-left">
              <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                Save
              </Button>
              <Button
                ghost
                type="primary"
                loading={processing}
                htmlType="button"
                className="mr-2"
                onClick={handleTriggerTest}>
                Test
              </Button>
              <Button type="primary" ghost htmlType="reset">
                Reset
              </Button>
            </Col>
          </PermissionChecker>
        </Row>
      </Form>
      <TestProxyServerConfig
        open={isModalopen}
        onCreate={onInitiateTest}
        onCancel={() => {
          setIsModalopen(false);
        }}
      />
    </SettingFormLayout>
  );
}
