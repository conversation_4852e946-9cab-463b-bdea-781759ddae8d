import { Button, Avatar, Form, Input, Row, Col, Select } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import Uploader from '@/src/components/Uploader';
import {
  createLogo<PERSON>pi,
  deleteLogo<PERSON>pi,
  getAll<PERSON>ogo<PERSON>pi,
  update<PERSON>ogo<PERSON>pi
} from '../../api/system-settings/logo-management';
import Permission<PERSON>hecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { useAuth } from '@/src/hooks/auth';

export default function LogoManagement() {
  const { token } = useAuth();

  const typeOptions = [
    {
      label: 'Vendor',
      value: 'vendor'
    },
    {
      label: 'OS',
      value: 'os'
    },
    {
      label: 'Framework',
      value: 'framework'
    },
    {
      label: 'Integration',
      value: 'integration'
    }
  ];

  const columns = [
    {
      title: 'Logo',
      key: 'logo',
      dataIndex: 'logo',
      render({ record }) {
        return record.image && record.image.length ? (
          <Avatar
            // eslint-disable-next-line
            shape="square"
            src={`/api/download?id=${
              record.image[0].ref || (record.image[0].response || {}).ref
              // eslint-disable-next-line
            }&mid=${token.access_token}`}
            size={25}
          />
        ) : (
          <img src="/images/addlogo_icon.png" style={{ width: `25px` }} alt={record.name} />
        );
      }
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type'
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  return (
    <CrudProvider
      columns={columns}
      resourceTitle="Logo"
      hasSearch
      fetchFn={getAllLogoApi}
      deleteFn={deleteLogoApi}
      createFn={createLogoApi}
      updateFn={updateLogoApi}
      createDrawerLabel="Add"
      createSlot={(createFn) => (
        <PermissionChecker permission={constants.Create_Settings}>
          <Button type="primary" onClick={createFn}>
            Add Logo
          </Button>
        </PermissionChecker>
      )}
      formFields={(item, _, { disabled }) => (
        <>
          <Row gutter={32}>
            <Col span={12}>
              <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                <Input placeholder="Name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Type" name="type" rules={[{ required: true }]}>
                <Select placeholder="Type" options={typeOptions} />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24} className="relative">
              {item.image &&
                item.image.length > 0 &&
                (item.image[0].ref || (item.image[0].response || {}).ref) && (
                  <Avatar
                    // eslint-disable-next-line
                    src={`/api/download?id=${
                      item.image[0].ref || item.image[0].response.ref
                      // eslint-disable-next-line
                    }&mid=${token.access_token}`}
                    size={55}
                    shape="square"
                    className="absolute"
                    style={{ top: '40px', left: '15px' }}
                  />
                )}
              <Form.Item label=" " name="image" className="flex-1">
                <Uploader message="please use png image for best result" accept="image/*" />
              </Form.Item>
            </Col>
          </Row>
        </>
      )}
    />
  );
}
