// import { Fragment, useState } from 'react';

import { Button, Form, Col, Row, Input } from 'antd';

import { CrudProvider } from '@/src/hooks/crud';

import {
  createCurrencyApi,
  deleteCurrencyApi,
  getAllCurrencyApi,
  updateCurrency<PERSON>pi
} from '../../api/system-settings/currency';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import Icon from '@/src/components/Icon';

export default function Currency({ onDone }) {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Exchange Rate',
      dataIndex: 'exchange_rate',
      key: 'exchange_rate'
    },
    {
      title: 'Created On',
      dataIndex: 'created_time',
      key: 'created_time',
      type: 'datetime',
      hidden: true
    },
    {
      title: 'Modified Time',
      dataIndex: 'modified_time',
      key: 'modified_time',
      type: 'datetime',
      hidden: true
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings],
      render({ record, edit, delete: deleteFn }) {
        return (
          <div>
            <PermissionChecker permission={constants.Update_Settings}>
              <Button
                type="link"
                onClick={() => {
                  edit(record);
                }}
                className="mr-2">
                <Icon name="edit" style={{ fontSize: '1.1rem' }} />
              </Button>
            </PermissionChecker>
            <PermissionChecker permission={constants.Delete_Settings}>
              <Button danger type="text" onClick={() => deleteFn(record)}>
                <Icon name="delete" style={{ fontSize: '1.1rem' }} />
              </Button>
            </PermissionChecker>
          </div>
        );
      }
    }
  ];
  return (
    <CrudProvider
      columns={columns}
      defaultFormItem={{}}
      resourceTitle="Currency"
      hasSearch
      fetchFn={getAllCurrencyApi}
      deleteFn={deleteCurrencyApi}
      createFn={createCurrencyApi}
      updateFn={updateCurrencyApi}
      createSlot={(createFn) => (
        <PermissionChecker permission={constants.Create_Settings}>
          <Button type="primary" onClick={createFn}>
            Create
          </Button>
        </PermissionChecker>
      )}
      formActions={({ formItem, resetForm, processingForm }) => (
        <>
          <Button type="primary" loading={processingForm} htmlType="submit" className="mr-2">
            {formItem && formItem.id ? 'Update' : 'Create'}
          </Button>

          <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
            Reset
          </Button>
        </>
      )}
      formFields={(item, _, { disabled }) => (
        <>
          <Row gutter={32}>
            <Col span={12}>
              <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                <Input placeholder="Name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Exchange Rate" name="exchange_rate" rules={[{ required: true }]}>
                <Input placeholder="Exchange Rate" />
              </Form.Item>
            </Col>
          </Row>
        </>
      )}
    />
  );
}
