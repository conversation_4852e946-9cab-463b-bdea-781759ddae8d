import { Button, Tabs, Form, Input, Row, InputN<PERSON>ber, Col, Select, Switch } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { Fragment, useState } from 'react';
import {
  createLdapServerApi,
  deleteLdapServerApi,
  getAllLdapServerApi,
  syncLDAPServerConfigApi,
  testLDAPServerConfigApi,
  updateLdapServerApi
} from '../../api/system-settings/ldap-server-config';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import Icon from '@/src/components/Icon';

export default function LDAPServerConfig() {
  const [isTesting, setIsTesting] = useState(false);

  const [testStatus, setTestStatus] = useState({});

  const syncLDAP = (item) => {
    setIsTesting(true);
    syncLDAPServerConfigApi(item).finally(() => setIsTesting(false));
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Host',
      dataIndex: 'host',
      key: 'host'
    },
    {
      title: 'FQDN',
      dataIndex: 'fqdn',
      key: 'fqdn'
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings],
      render({ record, edit, delete: deleteFn }) {
        return (
          <div>
            <Button
              title="Sync Now"
              type="link"
              onClick={() => {
                syncLDAP(record);
              }}
              className="mr-2">
              <Icon name="refresh" style={{ fontSize: '1.1rem' }} />
            </Button>
            <PermissionChecker permission={constants.Update_Settings}>
              <Button
                type="link"
                onClick={() => {
                  edit(record);
                }}
                className="mr-2">
                <Icon name="edit" style={{ fontSize: '1.1rem' }} />
              </Button>
            </PermissionChecker>
            <PermissionChecker permission={constants.Delete_Settings}>
              <Button danger type="text" onClick={() => deleteFn(record)}>
                <Icon name="delete" style={{ fontSize: '1.1rem' }} />
              </Button>
            </PermissionChecker>
          </div>
        );
      }
    }
  ];

  const MappingOptions = [
    'First Name',
    'Username',
    'Email',
    'Phone Number',
    'Department',
    // 'Team',
    'Location',
    { key: 'mac_address', label: 'MAC Address' }
  ];

  const testLDAP = (item) => {
    setIsTesting(true);
    testLDAPServerConfigApi(item)
      .then((data) => {
        setIsTesting(false);
        setTestStatus({
          status: 'success',
          message: data['response-message']
        });
      })
      .catch((e) => {
        setIsTesting(false);
        setTestStatus({
          status: 'error',
          message: e.response.data['response-message']
        });
      })
      .finally(() => setIsTesting(false));
  };

  const SyncIntervalOptions = [8, 12, 24, 48].map((i) => ({ label: `${i} Hours`, value: i }));

  const [tabKey, setActiveTabKey] = useState('configuration');

  const tabs = [
    {
      key: 'configuration',
      label: 'Configuration',
      children: (item) => (
        <>
          <Row gutter={32}>
            <Col span={12}>
              <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                <Input placeholder="Name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Host" name="host" rules={[{ required: true }]}>
                <Input placeholder="Host" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={32}>
            <Col span={12}>
              <Form.Item label="Port" name="port" rules={[{ required: true }]}>
                <InputNumber placeholder="Port" precision={0} className="w-full" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="FQDN" name="fqdn" rules={[{ required: true }]}>
                <Input placeholder="FQDN" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={32}>
            <Col span={12}>
              <Form.Item label="Username" name="username" rules={[{ required: true }]}>
                <Input placeholder="Username" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Password" name="password" rules={[{ required: true }]}>
                <Input placeholder="Password" type="password" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Base DN" name="baseDn" rules={[{ required: true }]}>
                <Input placeholder="Base DN" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Group Base" name="groupBase">
                <Input placeholder="Group Base" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Enable Auto Sync" name="autoSync" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Auto Sync Interval" name="syncInterval">
                <Select
                  placeholder="Select Interval"
                  disabled={!item.autoSync}
                  options={SyncIntervalOptions}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      )
    },
    {
      key: 'mapping',
      label: 'Mapping',
      children() {
        return (
          <Row gutter={32}>
            {MappingOptions.map((option) => (
              <Col span={12} key={option.key || option}>
                <Form.Item label={option.label || option} name={['mapping', option.key || option]}>
                  <Input placeholder={option.label || option} />
                </Form.Item>
              </Col>
            ))}
          </Row>
        );
      }
    }
  ];

  return (
    <CrudProvider
      columns={columns}
      defaultFormItem={{
        mapping: {}
      }}
      resourceTitle="LDAP Server"
      hasSearch
      fetchFn={getAllLdapServerApi}
      deleteFn={deleteLdapServerApi}
      createFn={createLdapServerApi}
      updateFn={updateLdapServerApi}
      createSlot={(createFn) => (
        <PermissionChecker permission={constants.Create_Settings}>
          <Button type="primary" onClick={createFn}>
            Create
          </Button>
        </PermissionChecker>
      )}
      formActions={({ formItem, resetForm, processingForm }) => (
        <>
          {testStatus.status ? (
            <div className={testStatus.status === 'error' ? 'text-danger' : 'text-success'}>
              {testStatus.message}
            </div>
          ) : null}
          <Button
            type="primary"
            loading={isTesting || processingForm}
            htmlType="submit"
            className="mr-2">
            {formItem && formItem.id ? 'Update' : 'Create'}
          </Button>
          <Button
            ghost
            type="primary"
            onClick={() => testLDAP(formItem)}
            loading={isTesting || processingForm}
            htmlType="button"
            className="mr-2">
            Test
          </Button>
          {formItem && formItem.id ? (
            <Button
              type="primary"
              ghost
              loading={isTesting || processingForm}
              htmlType="button"
              onClick={() => syncLDAP(formItem)}
              className="mr-2">
              Sync Now
            </Button>
          ) : null}
          <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
            Reset
          </Button>
        </>
      )}
      formFields={(item, _, { disabled }) => (
        <>
          <Tabs activeKey={tabKey} onChange={(key) => setActiveTabKey(key)} items={tabs} />
          {tabs.find((tab) => tab.key === tabKey).children(item)}
        </>
      )}
    />
  );
}
