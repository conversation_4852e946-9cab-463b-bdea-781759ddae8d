// import { Fragment, useState } from 'react';

import { Button, Form, Col, Row, Select, Input } from 'antd';

import { CrudProvider } from '@/src/hooks/crud';

import {
  createLicenseEditionApi,
  deleteLicenseEditionApi,
  getAllLicenseEditionApi,
  updateLicenseEditionApi
} from '../../api/system-settings/license-edition';
import Per<PERSON><PERSON><PERSON>cker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import Icon from '@/src/components/Icon';

export default function LicenseEdition({ onDone }) {
  const metricOptions = [
    { label: 'Single User', value: 'Single User' },
    { label: 'Multiple User', value: 'Multiple User' },
    { label: 'Per Device', value: 'Per Device' },
    { label: 'Per Installation', value: 'Per Installation' },
    { label: 'Per Processor', value: 'Per Processor' },
    { label: 'Per Core', value: 'Per Core' }
  ];

  const licenseSubscriptionTypeOptions = [
    { label: 'Subscription', value: 'Subscription' },
    { label: 'Perpetual', value: 'Perpetual' },
    { label: 'Free License', value: 'Free License' },
    { label: 'Trial', value: 'Trial' }
  ];

  const columns = [
    {
      title: 'License Edition',
      dataIndex: 'license_edition',
      key: 'license_edition',
      type: 'view_link'
    },
    {
      title: 'Metric',
      dataIndex: 'metric',
      key: 'metric'
    },
    {
      title: 'License Subscription Type',
      dataIndex: 'license_subscription_type',
      key: 'license_subscription_type'
    },
    {
      title: 'Created On',
      dataIndex: 'created_time',
      key: 'created_time',
      type: 'datetime',
      hidden: true
    },
    {
      title: 'Modified Time',
      dataIndex: 'modified_time',
      key: 'modified_time',
      type: 'datetime',
      hidden: true
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings],
      render({ record, edit, delete: deleteFn }) {
        return (
          <div>
            <PermissionChecker permission={constants.Update_Settings}>
              <Button
                type="link"
                onClick={() => {
                  edit(record);
                }}
                className="mr-2">
                <Icon name="edit" style={{ fontSize: '1.1rem' }} />
              </Button>
            </PermissionChecker>
            <PermissionChecker permission={constants.Delete_Settings}>
              <Button danger type="text" onClick={() => deleteFn(record)}>
                <Icon name="delete" style={{ fontSize: '1.1rem' }} />
              </Button>
            </PermissionChecker>
          </div>
        );
      }
    }
  ];
  return (
    <CrudProvider
      columns={columns}
      defaultFormItem={{}}
      resourceTitle="License Edition"
      hasSearch
      fetchFn={getAllLicenseEditionApi}
      deleteFn={deleteLicenseEditionApi}
      createFn={createLicenseEditionApi}
      updateFn={updateLicenseEditionApi}
      createSlot={(createFn) => (
        <PermissionChecker permission={constants.Create_Settings}>
          <Button type="primary" onClick={createFn}>
            Create
          </Button>
        </PermissionChecker>
      )}
      formActions={({ formItem, resetForm, processingForm }) => (
        <>
          <Button type="primary" loading={processingForm} htmlType="submit" className="mr-2">
            {formItem && formItem.id ? 'Update' : 'Create'}
          </Button>

          <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
            Reset
          </Button>
        </>
      )}
      formFields={(item, _, { disabled }) => (
        <>
          <Row gutter={32}>
            <Col span={12}>
              <Form.Item
                label="License Edition"
                name="license_edition"
                rules={[{ required: true }]}>
                <Input placeholder="License Edition" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Metric" name="metric" rules={[{ required: true }]}>
                <Select placeholder="metric" disabled={disabled} options={metricOptions} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="License Subscription Type" name="license_subscription_type">
                <Select
                  placeholder="License Subscription Type"
                  disabled={disabled}
                  options={licenseSubscriptionTypeOptions}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      )}
    />
  );
}
