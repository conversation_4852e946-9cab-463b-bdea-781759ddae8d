import { Tabs } from 'antd';
import { useState } from 'react';
import MaliciousHashForm from '../../components/system-settings/MaliciousHashForm';
import BlackListedIPForm from '../../components/system-settings/BlackListedIPForm';
import SettingFormLayout from '@modules/settings/components/SettingFormLayout';

export default function ThreatDatabase() {
  const [currentTab, setCurrentTab] = useState('malicious-hash');

  return (
    <SettingFormLayout>
      <Tabs
        activeKey={currentTab}
        className="w-full"
        onChange={(key) => setCurrentTab(key)}
        items={[
          { label: 'Malicious Hash', key: 'malicious-hash' },
          { label: 'Blacklisted IP', key: 'blacklisted-ip' }
        ]}
      />
      {currentTab === 'malicious-hash' ? <MaliciousHashForm /> : <BlackListedIPForm />}
    </SettingFormLayout>
  );
}
