import { Spin, Row, Col, List, Tag, Divider, Input, Button, message, Form } from 'antd';
import { useEffect, useState } from 'react';
import Merge from 'lodash/merge';
import AnimatedNumber from 'animated-number-react';
import {
  getActivationCode<PERSON>pi,
  getLicenseApi,
  updateLicenseCodeApi
} from '../../api/system-settings/license';
import Status from '@/src/components/Status';
import { useAuth } from '@/src/hooks/auth';

export default function License() {
  const { formatDateTime } = useAuth();

  const dateTimeKeys = ['issuedAt', 'expiresAt'];

  const [license, setLicense] = useState({});
  const [activationCode, setActivationCode] = useState({});
  const [loading, setLoading] = useState(true);
  const [processingForm, setProcessing] = useState(false);
  const [licenseForm, setLicenseForm] = useState({ code: '' });

  useEffect(() => {
    getLicenseApi()
      .then((license) => {
        setLicense(license);
        setLoading(false);
        return getActivationCodeApi();
      })
      .then((data) => {
        setActivationCode(data);
      });
  }, []);

  function copyCodeToClipboard() {
    navigator.clipboard.writeText(activationCode.code);
    message.success('Activation code copied successfully');
  }

  function submitLicenseCode() {
    setProcessing(true);
    updateLicenseCodeApi(licenseForm)
      .then(() => {
        message.success('License has been applied successfully.');
        getLicenseApi()
          .then((license) => {
            setLicense(license);
            setLoading(false);
            return getActivationCodeApi();
          })
          .then((data) => {
            setActivationCode(data);
          });
      })
      .finally(() => setProcessing(false));
  }

  return loading ? (
    <div className="flex-1 flex items-center justify-center h-full">
      <Spin spinning />
    </div>
  ) : (
    <div className="flex flex-col p-6 w-full">
      <div className="flex justify-between">
        <h1 className="text-primary">License To: {license.licensedTo}</h1>
        <h1 className="text-primary">Product Code: {license.licenseProduct}</h1>
      </div>
      <Divider />
      <div className="flex items-center my-2">
        <span className="mr-4">License Type:</span>
        <div>
          <Status
            useTag={true}
            color={license.licenseType === 'Free' ? 'error' : 'success'}
            status={license.licenseType}
          />
        </div>
      </div>
      <Row gutter={32}>
        <Col span={8}>
          <List
            bordered
            dataSource={[
              {
                title: 'PO Number',
                key: 'po'
              },
              {
                title: 'Invoice Number',
                key: 'invoiceNumber'
              },
              {
                title: 'Email',
                key: 'email'
              },
              {
                title: 'Partner',
                key: 'partner'
              }
            ]}
            renderItem={(item) => (
              <List.Item>
                {item.title}:{' '}
                {item.type === 'tag' ? (
                  <Tag color="processing">{license[item.key]}</Tag>
                ) : (
                  license[item.key] || '---'
                )}
              </List.Item>
            )}
          />
        </Col>
        <Col span={8}>
          <List
            bordered
            dataSource={[
              {
                title: 'Issue Date',
                key: 'issuedAt'
              },
              {
                title: 'Expires On',
                key: 'expiresAt'
              },
              {
                title: 'No. Of Endpoints',
                key: 'totalEndPoints',
                type: 'tag'
              },
              {
                title: 'Used Endpoints',
                key: 'usedEndPoints',
                type: 'tag'
              }
            ]}
            renderItem={(item) => (
              <List.Item>
                {item.title}:{' '}
                {item.type === 'tag' ? (
                  <Tag color="processing">{license[item.key]}</Tag>
                ) : dateTimeKeys.includes(item.key) && license[item.key] ? (
                  formatDateTime(license[item.key])
                ) : (
                  license[item.key] || '---'
                )}
              </List.Item>
            )}
          />
        </Col>
        <Col span={8}>
          <div className="flex flex-col items-center justify-center">
            <h3 className="text-lg text-primary">Remaining Days</h3>
            <AnimatedNumber
              className="text-primary text-7xl"
              value={license.remainingDays}
              formatValue={(v) => parseInt(v)}
            />
          </div>
          <Divider />
          <div className="flex flex-col items-center justify-center my-4">
            <h3 className="text-lg text-primary">Remaining Endpoints</h3>
            <AnimatedNumber
              className="text-primary text-7xl"
              value={license.remainingEndPoints}
              formatValue={(v) => parseInt(v)}
            />
          </div>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <div className="flex items-center">
            <span className="mr-2">Activation Code: </span>
            <div className="px-1 py-2 border-border border-solid rounded">
              {activationCode.code}
            </div>
            <Button
              type="primary"
              size="medium"
              className="ml-2"
              primary
              onClick={copyCodeToClipboard}>
              Copy To Clipboard
            </Button>
          </div>
        </Col>
        <Col span={24} className="mt-4">
          <Form
            layout="vertical"
            className="h-full"
            onFinish={submitLicenseCode}
            onValuesChange={(values) => {
              setLicenseForm(Merge({ ...licenseForm }, { ...values }));
            }}>
            <Form.Item label="License Code" name="code" rules={[{ required: true }]}>
              <Input.TextArea placeholder="License Code" />
            </Form.Item>
            <Button type="primary" loading={processingForm} htmlType="submit" className="mr-2">
              Update License
            </Button>
          </Form>
        </Col>
      </Row>
    </div>
  );
}
