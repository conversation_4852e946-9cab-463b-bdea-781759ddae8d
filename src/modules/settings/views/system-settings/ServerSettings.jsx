import { useEffect, useState } from 'react';
import { Form, Spin, Button, Row, Col, Input, Switch, Select } from 'antd';

import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { Permissions } from '@/src/components/Permissions';

import {
  getServerSettingsApi,
  updateServerSettingsApi
} from '../../api/system-settings/server-settings';

import SettingFormLayout from '@modules/settings/components/SettingFormLayout';

export default function ServerSettings({ onDone }) {
  const LogLevelOptions = [
    { label: 'Trace', value: 0 },
    { label: 'Debug', value: 1 },
    { label: 'Warning', value: 2 },
    { label: 'Info', value: 3 }
  ];

  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  const [config, setConfig] = useState({
    log_level: 2
  });
  const [form] = Form.useForm();
  const { hasPermission } = Permissions.usePermission();

  useEffect(() => {
    getServerSettingsApi().then((response) => {
      setConfig(response);
      form.setFieldsValue(response);
      setLoading(false);
    });
    // eslint-disable-next-line
  }, []);

  const submitForm = (data) => {
    setProcessing(true);
    updateServerSettingsApi(data)
      .then(() => {
        if (onDone) {
          onDone();
        }
      })
      .finally(() => setProcessing(false));
  };

  function onValuesChange(changedValues, allValues) {
    setConfig(allValues);
  }

  return loading ? (
    <div className="flex flex-col min-h-0 flex-1 items-center justify-center">
      <Spin spinning />
    </div>
  ) : (
    <SettingFormLayout>
      <Form
        layout="vertical"
        form={form}
        className="h-full"
        disabled={!hasPermission(constants.Update_Settings)}
        onFinish={submitForm}
        onValuesChange={onValuesChange}
        initialValues={config}>
        <Row gutter={32}>
          <Col span={12}>
            <Form.Item
              label="Session Timeout"
              name="session_timeout_enable"
              valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          <Col span={12} />

          <Col span={12}>
            <Form.Item label="Session Timeout" name="session_timeout" rules={[{ required: true }]}>
              <Input placeholder="Session Timeout" type="number" addonAfter="Minute" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Session Idle Timeout"
              name="session_idle_timeout"
              rules={[{ required: true }]}>
              <Input placeholder="Session Idle Timeout" type="number" addonAfter="Minute" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              label="Endpoint Online Status Timeout"
              name="endpoint_refresh_call_duration"
              rules={[{ required: true }]}>
              <Input
                placeholder="Endpoint Refresh Call Duration"
                type="number"
                addonAfter="Minute"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="EOL Scan Job Time"
              name="eol_scan_job_time"
              rules={[{ required: true }]}>
              <Input placeholder="Eol Scan Job Time" type="number" addonAfter="Hour" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Log Level" name="log_level" rules={[{ required: true }]}>
              <Select placeholder="Select Log Level" options={LogLevelOptions} />
            </Form.Item>
          </Col>
          <PermissionChecker permission={constants.Update_Settings}>
            <Col span={24} className="text-left">
              <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                Save
              </Button>

              <Button type="primary" ghost htmlType="reset">
                Reset
              </Button>
            </Col>
          </PermissionChecker>
        </Row>
      </Form>
    </SettingFormLayout>
  );
}
