import { Button, Tag } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import {
  createDeploymentPolicyApi,
  deleteDeploymentPolicyApi,
  getAllDeploymentPoliciesApi,
  updateDeploymentPolicyApi
} from '../../api/software-deployment/deployment-policy';
import Permission<PERSON><PERSON><PERSON> from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { User } from '@/src/components/pickers/UserPicker';
import DeployemntPolicyForm from '../../components/software-deployment/DeployemntPolicyForm';

export default function DeploymentPolicies() {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Name',
      dataIndex: 'displayName',
      key: 'displayName'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Type',
      dataIndex: 'type',
      align: 'center',
      key: 'type',
      sortable: false,
      render({ record }) {
        return (
          <Tag
            color={'processing'}
            className="inline-flex items-center justify-center"
            style={{
              textAlign: 'center',
              textTransform: 'uppercase'
            }}>
            {record.type}
          </Tag>
        );
      }
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      sortable: false,
      render({ record }) {
        return <User.Picker textOnly value={record.createdBy} disabled />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  return (
    <User.Provider>
      <CrudProvider
        columns={columns}
        defaultFormItem={{
          type: 'instant',
          restartType: 'no_restart',
          refModel: 'all'
        }}
        resourceTitle="Deployment Policy"
        disableFormScrolling
        hasSearch
        fetchFn={getAllDeploymentPoliciesApi}
        deleteFn={deleteDeploymentPolicyApi}
        createFn={createDeploymentPolicyApi}
        updateFn={updateDeploymentPolicyApi}
        createSlot={(createFn) => (
          <PermissionChecker permission={constants.Create_Settings}>
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          </PermissionChecker>
        )}
        formFields={(item) => <DeployemntPolicyForm item={item} />}
      />
    </User.Provider>
  );
}
