import { useEffect, useState } from 'react';
import { Form, Spin, Button, Row, Col, Input, TimePicker } from 'antd';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import AnimatedNumber from 'animated-number-react';
import { Permissions } from '@/src/components/Permissions';
import {
  getVulnerabilityPreferenceApi,
  updateVulnerabilityPreferenceApi,
  vulnerabilitySyncApi
} from '../../api/vulnerability-preference/vulnerability-preference';
import SettingFormLayout from '@modules/settings/components/SettingFormLayout';
import { useAuth } from '@/src/hooks/auth';
import { useLayout } from '@/src/layouts/Layout';

export default function VulnerabilityPreference() {
  const { formatDateTime } = useAuth();
  const { message } = useLayout();
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [config, setConfig] = useState({});
  const [form] = Form.useForm();
  const { hasPermission } = Permissions.usePermission();

  useEffect(() => {
    getVulnerabilityPreferenceApi().then((response) => {
      setConfig(response);
      form.setFieldsValue(response);
      setLoading(false);
    });
    // eslint-disable-next-line
  }, []);

  const submitForm = (data) => {
    setProcessing(true);
    updateVulnerabilityPreferenceApi(data)
      .then(() => resetForm())
      .finally(() => setProcessing(false));
  };

  function onValuesChange(changedValues, allValues) {
    setConfig(allValues);
  }

  function resetForm() {
    getVulnerabilityPreferenceApi().then((response) => {
      setConfig(response);
      form.setFieldsValue(response);
      setLoading(false);
    });
  }

  function initiateVulnerabilitySync() {
    return vulnerabilitySyncApi().then(() => {
      getVulnerabilityPreferenceApi().then((response) => {
        setConfig(response);
        form.setFieldsValue(response);
        setLoading(false);
      });

      return message.success(`Vulnerability database sync will be handled`);
    });
  }

  return loading ? (
    <div className="flex flex-col min-h-0 flex-1 items-center justify-center">
      <Spin spinning />
    </div>
  ) : (
    <SettingFormLayout span={24}>
      <Form
        layout="vertical"
        form={form}
        className="h-full"
        disabled={!hasPermission(constants.Update_Settings)}
        onFinish={submitForm}
        onValuesChange={onValuesChange}
        initialValues={config}>
        <Row gutter={16}>
          <Col span={14}>
            <Row gutter={16}>
              {config.last_vuln_database_sync_time && !config.vuln_sync_running ? (
                <Col span={24} className="mb-4">
                  <h3 className={`${config.vuln_sync_status ? 'text-success' : 'text-danger'}`}>
                    Last Vulnerability Database sync was performed at{' '}
                    {formatDateTime(config.last_vuln_database_sync_time)}
                  </h3>
                </Col>
              ) : null}
              <Col span={12}>
                <Form.Item
                  label="Vulnerability Scan Job Time"
                  name="vuln_scan_job_time"
                  rules={[{ required: true }]}>
                  <Input
                    placeholder="Vulnerability Scan Job Time"
                    min={1}
                    type="number"
                    addonAfter="Hour"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Vulnerability Database Sync Time"
                  name="vuln_database_sync_time"
                  rules={[{ required: true }]}>
                  <TimePicker
                    allowClear={false}
                    className="w-full"
                    placeholder="Vulnerability Database Sync Time"
                  />
                </Form.Item>
              </Col>
              <PermissionChecker permission={constants.Update_Settings}>
                <Col span={24} className="text-left">
                  <Button
                    type="primary"
                    className="mr-2"
                    onClick={initiateVulnerabilitySync}
                    loading={config.vuln_sync_running}>
                    {config.vuln_sync_running ? 'Syncing ...' : 'Sync Now'}
                  </Button>
                  <Button type="primary" loading={processing} htmlType="submit" className="mr-2">
                    Save
                  </Button>

                  <Button type="primary" ghost onClick={resetForm}>
                    Reset
                  </Button>
                </Col>
              </PermissionChecker>
            </Row>
          </Col>
          <Col span={10}>
            <Row>
              <Col span={24} className="mb-4">
                <div className="flex flex-col items-center justify-center">
                  <h3 className="text-lg text-primary">Total CVE Available</h3>
                  <AnimatedNumber
                    className="text-primary text-7xl"
                    value={config.cve_count}
                    formatValue={(v) =>
                      parseInt(v).toLocaleString(undefined, {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                      })
                    }
                  />
                </div>
              </Col>
            </Row>
          </Col>
        </Row>
      </Form>
    </SettingFormLayout>
  );
}
