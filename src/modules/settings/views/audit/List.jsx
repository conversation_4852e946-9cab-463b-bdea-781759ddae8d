import { Select } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { getAllAuditApi, getAuditFiltersApi } from '../../api/audit/audit-api';
import { useEffect, useState } from 'react';
import TimelinePicker from '@/src/components/pickers/TimelinePicker';

export default function List() {
  const [availableFilters, setAvailableFilters] = useState({
    users: [],
    modules: []
  });
  const [timeline, setTimeline] = useState({});
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectedModules, setSelectedModules] = useState([]);
  const [selectedOperations, setSelectedOperations] = useState([]);

  const operationOptions = [
    {
      value: 'Create',
      label: 'Create'
    },
    {
      value: 'Update',
      label: 'Update'
    },
    {
      value: 'Delete',
      label: 'Delete'
    },
    {
      value: 'Login',
      label: 'Login'
    },
    {
      value: 'Logout',
      label: 'Logout'
    }
  ];

  const columns = [
    {
      title: 'Module',
      dataIndex: 'module',
      key: 'module'
    },
    {
      title: 'Operation',
      dataIndex: 'operation',
      key: 'operation'
    },
    {
      title: 'User',
      dataIndex: 'user',
      key: 'user'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status'
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    }
  ];

  useEffect(() => {
    getAuditFiltersApi().then((data) => {
      setAvailableFilters(data);
    });
  }, []);

  return (
    <CrudProvider
      columns={columns}
      resourceTitle="Audit"
      hasSearch
      key={`${selectedModules.join(',')}-${selectedUsers.join(',')}-${selectedOperations.join(
        ','
      )}-${JSON.stringify(timeline)}`}
      fetchFn={(...args) =>
        getAllAuditApi(...args, {
          timeline,
          selectedModules,
          selectedUsers,
          selectedOperations
        })
      }
      beforeCreateSlot={() => (
        <>
          <TimelinePicker value={timeline} onChange={setTimeline} />
          <Select
            options={availableFilters.modules.map((i) => ({ value: i, label: i }))}
            placeholder="Select Module"
            className="mx-2"
            value={selectedModules}
            mode="multiple"
            onChange={setSelectedModules}
            style={{ width: '150px' }}
          />
          <Select
            options={availableFilters.users.map((i) => ({ value: i, label: i }))}
            placeholder="Select User"
            className="mr-2"
            mode="multiple"
            value={selectedUsers}
            onChange={setSelectedUsers}
            style={{ width: '150px' }}
          />
          <Select
            options={operationOptions}
            placeholder="Select Operation"
            className="mr-2"
            mode="multiple"
            value={selectedOperations}
            onChange={setSelectedOperations}
            style={{ width: '150px' }}
          />
        </>
      )}
    />
  );
}
