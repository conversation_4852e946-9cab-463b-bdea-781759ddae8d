import { Button, Switch } from 'antd';
import { <PERSON>rud<PERSON>rovider } from '@/src/hooks/crud';
import {
  createAction<PERSON><PERSON>,
  deleteAction<PERSON>pi,
  getAllActionsApi,
  updateActionApi
} from '../../api/integrations/actions';
import Permission<PERSON>hecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import { User } from '@/src/components/pickers/UserPicker';
import ActionForm from '../../components/integrations/ActionForm';
import SystemLogo from '@/src/components/SystemLogo';

export default function Actions() {
  const columns = [
    {
      title: 'Integration Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link',
      render({ record }) {
        return (
          <div className="flex items-center">
            <SystemLogo
              className="mr-2"
              style={{ width: '30px' }}
              name={
                record.context?.incidentType ||
                record.context?.threat_intelligence_type ||
                record.context?.ai_type ||
                record.action_type
              }
              type="integration"
            />
            {record.name}
          </div>
        );
      }
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Integration Type',
      dataIndex: 'type',
      key: 'type'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render({ record, update }) {
        return (
          <Switch
            checked={record.status}
            onChange={(e) => {
              update({ id: record.id, status: e });
            }}
          />
        );
      },
      sortable: false
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      sortable: false,
      render({ record }) {
        return <User.Picker textOnly value={record.createdBy} disabled />;
      }
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_Settings],
      deletePermissions: [constants.Delete_Settings]
    }
  ];

  return (
    <User.Provider>
      <CrudProvider
        columns={columns}
        defaultFormItem={{
          type: 'Email',
          context: {
            incidentType: 'Zendesk',
            auth_type: 'Basic'
          },
          status: true
        }}
        resourceTitle="Integration"
        disableFormScrolling
        hasSearch
        fetchFn={getAllActionsApi}
        deleteFn={deleteActionApi}
        createFn={createActionApi}
        updateFn={updateActionApi}
        createSlot={(createFn) => (
          <PermissionChecker permission={constants.Create_Settings}>
            <Button type="primary" onClick={createFn}>
              Create
            </Button>
          </PermissionChecker>
        )}
        formFields={(item) => <ActionForm item={item} />}
      />
    </User.Provider>
  );
}
