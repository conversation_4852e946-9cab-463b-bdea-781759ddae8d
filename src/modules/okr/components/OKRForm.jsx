import { Row, Col, Form, Button, Input, InputNumber, Select, Divider, DatePicker } from 'antd';
import dayjs from 'dayjs';
import Icon from '@/src/components/Icon';
import AssetScopePicker from '@/src/components/pickers/AssetScopePicker';
import { User } from '@/src/components/pickers/UserPicker';
import Repeater from '@/src/components/Repeater';
import { IntegrationAction } from '@/src/components/pickers/IntegrationActionPicker';

function AttributeCondition({ name, disabled, ...props }) {
  const policyAttributeConditionOptions = [
    'Equal',
    'NotEqual',
    'GreaterThan',
    'GreaterThanEqual',
    'LessThan',
    'LessThanEqual',
    'Contains',
    'NotContains',
    'StartWith',
    'EndWith',
    'In'
  ].map((i) => ({ value: i, label: i }));

  let stringAttrs = ['Severity', 'Exploitable'].map((i) => `Vulnerability ${i}`);

  const form = Form.useFormInstance();

  let joinOptions = [
    {
      label: 'AND',
      value: 'and'
    },
    {
      label: 'OR',
      value: 'or'
    }
  ];
  const attributeOptions = [
    'EPSS',
    'Score',
    'CVSS3',
    'CVSS2',
    'CVSS4',
    'Severity',
    'Exploitable'
  ].map((i) => ({ label: i, value: `Vulnerability ${i}` }));

  return (
    <Repeater
      name={name}
      disabled={disabled}
      canAdd={true}
      defaultItem={{
        attribute_name: undefined,
        condition: undefined,
        condition_value: undefined,
        join: 'and'
      }}
      addBtnText={'Add Condition'}
      {...props}>
      {({ key, name: innerName, ...restField }, actions) => {
        return (
          <Row key={key}>
            <Col span={24}>
              <div className="flex flex-col">
                <div className="flex items-center">
                  <div
                    className={`flex-1 mr-2 px-2 py-1 rounded border-solid border-border
       ${disabled ? '' : 'bg-border'}`}>
                    <Row gutter={32}>
                      <Col span={8}>
                        <Form.Item
                          label="Attribute"
                          name={[innerName, 'attribute_name']}
                          rules={[{ required: true }]}>
                          <Select
                            options={attributeOptions}
                            placeholder="Select Attribute"
                            onChange={(attribute) => {
                              form.setFieldValue([innerName, 'attribute_name'], attribute);
                              form.setFieldValue([innerName, 'condition'], undefined);
                              form.setFieldValue([innerName, 'condition_value'], undefined);
                            }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          label="Condition"
                          name={[innerName, 'condition']}
                          rules={[{ required: true }]}>
                          <Select
                            placeholder="Condition"
                            options={policyAttributeConditionOptions}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          label="Value"
                          name={[innerName, 'condition_value']}
                          dependencies={[[name, innerName, 'attribute_name']]}
                          rules={[{ required: true }]}>
                          {stringAttrs.includes(
                            form.getFieldValue([name, innerName, 'attribute_name'])
                          ) ? (
                            <Input placeholder="Value" />
                          ) : (
                            <InputNumber placeholder="Value" precision={2} className="w-full" />
                          )}
                        </Form.Item>
                      </Col>
                    </Row>
                  </div>
                  <div className="flex-shrink-0 flex items-center">
                    {!disabled && (
                      <Button
                        shape="circle"
                        type="danger"
                        style={{ visibility: innerName === 0 ? 'hidden' : 'visible' }}
                        onClick={() => actions.remove(innerName)}>
                        <Icon name="close" className="text-danger" style={{ fontSize: '1.5rem' }} />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </Col>
            {(form.getFieldValue(name) || []).length > 1 &&
              innerName < (form.getFieldValue(name) || []).length - 1 && (
                <Col span={24}>
                  <Divider>
                    <Form.Item
                      name={[innerName, 'join']}
                      dependencies={['type']}
                      style={{ margin: 0 }}
                      rules={[{ required: true }]}>
                      <Select options={joinOptions} />
                    </Form.Item>
                  </Divider>
                </Col>
              )}
          </Row>
        );
      }}
    </Repeater>
  );
}

export default function OKRForm({ item = {} }) {
  const form = Form.useFormInstance();
  const okrModuleOptions = [
    {
      label: 'Vulnerability',
      value: 'vulnerability'
    }
  ];

  const remediateTypeOptions = ['By', 'Within'].map((i) => ({
    label: i,
    value: i
  }));

  return (
    <div className="flex flex-col h-full flex-1 min-h-0">
      <div className="flex-1 min-h-0 flex flex-col">
        <div className="h-full overflow-y-auto overflow-x-hidden">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Name" name="name" rules={[{ required: true }]}>
                <Input placeholder="OKR Name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="OKR Module" name="type" rules={[{ required: true }]}>
                <Select placeholder="OKR Module" options={okrModuleOptions} />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label="Description" name="description">
            <Input.TextArea placeholder="Description" />
          </Form.Item>
          <Form.Item label=" " noStyle name="scope">
            <AssetScopePicker
              label="Scope"
              gutter={16}
              name={['scope', 'assetFilter']}
              subname={['scope', 'assets']}
            />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Objective Goal in %"
                name="okr_target_percentage"
                rules={[{ required: true }]}>
                <InputNumber
                  placeholder="Target Percentage"
                  precision={2}
                  max={100}
                  min={0}
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Assigned To" name="assigned_to">
                <User.Picker />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="okr_context"
            label="Objective Measurement Conditions"
            rules={[{ required: true }]}>
            <AttributeCondition name={'okr_context'} />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Objective Remediation Timeline">
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={['okr_remediate_context', 'remediate_type']}
                      label="Remediate Type"
                      rules={[{ required: true }]}>
                      <Select options={remediateTypeOptions} placeholder="Please Select" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={['okr_remediate_context', 'remediate_type_value']}
                      label="Remediate Type"
                      dependencies={[['okr_remediate_context', 'remediate_type']]}
                      rules={[{ required: true }]}>
                      {form.getFieldValue(['okr_remediate_context', 'remediate_type']) ===
                        'Within' || item.okr_remediate_context?.remediate_type === 'Within' ? (
                        <InputNumber precision={0} placeholder="Days" className="w-full" />
                      ) : (
                        <DatePicker minDate={dayjs()} className="w-full" />
                      )}
                    </Form.Item>
                  </Col>
                </Row>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Notification">
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item name={'okr_action'} label="OKR Action">
                      <IntegrationAction.Picker mode="multiple" includedTypes={['Email']} />
                    </Form.Item>
                  </Col>
                </Row>
              </Form.Item>
            </Col>
          </Row>
        </div>
      </div>
    </div>
  );
}
