import api from '@api';
import dayjs from 'dayjs';
import {
  transformAssetScope,
  transformAssetScopeForServer
} from '@/src/components/pickers/AssetScopePicker';

const END_POINT = `/sla/okr`;

const transform = (item) => ({
  id: item.id,
  name: item.okr_name,
  type: item.okr_type,
  ...transformAssetScope(item),
  okr_target_percentage: item.okr_target_percentage,
  assigned_to: item.assigned_to,
  okr_action: item.okr_action,
  description: item.okr_description || '',
  status: item.status,
  createdAt: item.created_time,
  createdBy: item.created_by,
  okr_context: item.okr_context.map((i) => ({
    attribute_name: i.attribute_name,
    condition: i.condition,
    condition_value: i.condition_value,
    join: i.operator
  })),
  okr_remediate_context: {
    remediate_type: item.okr_remediate_context?.remediate_type,
    remediate_type_value:
      item.okr_remediate_context?.remediate_type === 'By'
        ? dayjs.unix(item.okr_remediate_context?.remediate_type_value)
        : item.okr_remediate_context?.remediate_type_value
  }
});

const transformForServer = async (item) => {
  return Promise.resolve({
    id: item.id,
    okr_name: item.name,
    okr_type: item.type,
    okr_action: item.okr_action,
    assigned_to: item.assigned_to,
    ...transformAssetScopeForServer(item),
    okr_target_percentage: item.okr_target_percentage,
    okr_description: item.description,
    status: item.status,
    createdAt: item.created_time,
    okr_context: item.okr_context.map((i) => ({
      attribute_name: i.attribute_name,
      condition: i.condition,
      condition_value: i.condition_value,
      operator: i.join
    })),
    okr_remediate_context: {
      remediate_type: item.okr_remediate_context?.remediate_type,
      remediate_type_value:
        item.okr_remediate_context?.remediate_type === 'By'
          ? item.okr_remediate_context?.remediate_type_value.unix()
          : item.okr_remediate_context?.remediate_type_value
    }
  });
};

const sortKeyMap = {
  name: 'okr_name',
  assetFilter: 'scope',
  type: 'okr_type',
  description: 'okr_description'
};

const searchableColumns = [
  'okr_name',
  'okr_description',
  'okr_type',
  'okr_target_percentage',
  'okr_remediate_context',
  'okr_context'
];

export function getAllOkrApi(offset, size, sortFilter) {
  return api
    .post(`${END_POINT}/search`, {
      offset,
      size,
      ...(sortFilter && sortFilter.sort && sortFilter.sort.field
        ? {
            sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
              sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
            }`
          }
        : {}),
      qualification: sortFilter.searchTerm
        ? searchableColumns.map((c) => ({
            operator: 'Contains',
            column: c,
            value: sortFilter.searchTerm
          }))
        : []
    })
    .then((data) => {
      return {
        totalCount: data.totalCount,
        result: data.result.map(transform)
      };
    });
}

export function getOkrApi(id) {
  return api.get(`${END_POINT}/${id}`).then(({ result }) => transform(result));
}

export function updateOkrApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.put(`${END_POINT}/${item.id}`, data);
    })
    .then((data) => getOkrApi(data.result));
}

export function createOkrApi(item) {
  return transformForServer(item)
    .then((data) => {
      return api.post(`${END_POINT}`, data);
    })
    .then((data) => getOkrApi(data.result));
}

export function deleteOkrApi(item) {
  return api.delete(`${END_POINT}/${item.id}`);
}
