import { Row, Col } from 'antd';
import { Outlet } from 'react-router-dom';
import PageHeading from '@/src/components/PageHeading';
import AnimatedRoutes from '@components/AnimatedRoutes';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';

export default function OKRLayout() {
  return (
    <div className="h-full flex flex-col">
      <PageHeading icon="okr" title="OKR" />
      <div className="flex-1 min-h-0 flex flex-col">
        <Row className="h-full">
          <PermissionChecker permission={constants.View_OKR} redirect>
            <AnimatedRoutes element={Col} span={24} className="h-full flex flex-col min-h-0 flex-1">
              <Outlet />
            </AnimatedRoutes>
          </PermissionChecker>
        </Row>
      </div>
    </div>
  );
}
