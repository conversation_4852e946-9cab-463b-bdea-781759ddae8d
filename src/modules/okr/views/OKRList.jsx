import { Button } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import { assetFilterOptions } from '@/src/components/pickers/AssetScopePicker';
import { createOkrApi, deleteOkrApi, getAllOkrApi, updateOkrApi } from '../api/okr';
import PermissionChecker from '@/src/components/PermissionChecker';
import constants from '@/src/constants/index';
import OKRForm from '../components/OKRForm';
import { IntegrationAction } from '@/src/components/pickers/IntegrationActionPicker';
import { User } from '@/src/components/pickers/UserPicker';

export default function ReportsList() {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Scope',
      dataIndex: 'scope',
      key: 'scope',
      render({ record }) {
        const option = assetFilterOptions.find((d) => d.value === record.scope.assetFilter);
        return option ? option.label : null;
      },
      sortable: false
    },
    {
      title: 'Target Percentage',
      dataIndex: 'okr_target_percentage',
      key: 'okr_target_percentage'
    },
    {
      title: 'Created On',
      dataIndex: 'createdAt',
      key: 'createdAt',
      type: 'datetime'
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      editPermissions: [constants.Update_OKR],
      deletePermissions: [constants.Delete_OKR]
    }
  ];

  return (
    <User.Provider>
      <IntegrationAction.Provider>
        <CrudProvider
          columns={columns}
          resourceTitle="OKR"
          hasSearch
          fetchFn={getAllOkrApi}
          deleteFn={deleteOkrApi}
          createFn={createOkrApi}
          updateFn={updateOkrApi}
          defaultFormItem={{
            type: 'vulnerability',
            okr_context: [
              {
                attribute_name: undefined,
                condition: undefined,
                condition_value: undefined,
                join: 'and'
              }
            ],
            okr_remediate_context: {}
          }}
          createSlot={(create) => (
            <PermissionChecker permission={constants.Create_OKR}>
              <Button type="primary" onClick={create}>
                Create
              </Button>
            </PermissionChecker>
          )}
          formFields={(item) => <OKRForm item={item} />}
        />
      </IntegrationAction.Provider>
    </User.Provider>
  );
}
