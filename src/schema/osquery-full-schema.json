{"account_policy_data": [{"label": "uid", "info": "User ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "creation_time", "info": "When the account was first created", "section": "Column", "boost": 99, "detail": "(double)"}, {"label": "failed_login_count", "info": "The number of failed login attempts using an incorrect password. Count resets after a correct password is entered.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "failed_login_timestamp", "info": "The time of the last failed login attempt. Resets after a correct password is entered", "section": "Column", "boost": 99, "detail": "(double)"}, {"label": "password_last_set_time", "info": "The time the password was last changed", "section": "Column", "boost": 99, "detail": "(double)"}], "acpi_tables": [{"label": "name", "info": "ACPI table name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Size of compiled table data", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "md5", "info": "MD5 hash of table content", "section": "Column", "boost": 99, "detail": "(text)"}], "ad_config": [{"label": "name", "info": "The macOS-specific configuration name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "domain", "info": "Active Directory trust domain", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "option", "info": "Canonical name of option", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Variable typed option value", "section": "Column", "boost": 99, "detail": "(text)"}], "alf": [{"label": "allow_signed_enabled", "info": "1 If allow signed mode is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "firewall_unload", "info": "1 If firewall unloading enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "global_state", "info": "1 If the firewall is enabled with exceptions, 2 if the firewall is configured to block all incoming connections, else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "logging_enabled", "info": "1 If logging mode is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "logging_option", "info": "Firewall logging option", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "stealth_enabled", "info": "1 If stealth mode is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "version", "info": "Application Layer Firewall version", "section": "Column", "boost": 99, "detail": "(text)"}], "alf_exceptions": [{"label": "path", "info": "Path to the executable that is excepted", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state", "info": "Firewall exception state", "section": "Column", "boost": 99, "detail": "(integer)"}], "alf_explicit_auths": [{"label": "process", "info": "Process name explicitly allowed", "section": "Column", "boost": 99, "detail": "(text)"}], "app_schemes": [{"label": "scheme", "info": "Name of the scheme/protocol", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "handler", "info": "Application label for the handler", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "enabled", "info": "1 if this handler is the OS default, else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "external", "info": "1 if this handler does NOT exist on macOS by default, else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "protected", "info": "1 if this handler is protected (reserved) by macOS, else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "apparmor_events": [{"label": "type", "info": "Event type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "message", "info": "Raw audit message", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uptime", "info": "Time of execution in system uptime", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "apparmor", "info": "Apparmor Status like ALLOWED, DENIED etc.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "operation", "info": "Permission requested by the process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "parent", "info": "Parent process PID", "section": "Column", "boost": 99, "detail": "(unsigned_bigint)"}, {"label": "profile", "info": "Apparmor profile name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Process name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Process ID", "section": "Column", "boost": 99, "detail": "(unsigned_bigint)"}, {"label": "comm", "info": "Command-line name of the command that was used to invoke the analyzed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "denied_mask", "info": "Denied permissions for the process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "capname", "info": "Capability requested by the process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "fsuid", "info": "Filesystem user ID", "section": "Column", "boost": 99, "detail": "(unsigned_bigint)"}, {"label": "ouid", "info": "Object owner's user ID", "section": "Column", "boost": 99, "detail": "(unsigned_bigint)"}, {"label": "capability", "info": "Capability number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "requested_mask", "info": "Requested access mask", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "info", "info": "Additional information", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "error", "info": "Error information", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "namespace", "info": "AppArmor namespace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "label", "info": "AppArmor label", "section": "Column", "boost": 99, "detail": "(text)"}], "apparmor_profiles": [{"label": "path", "info": "Unique, aa-status compatible, policy identifier.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Policy name.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "attach", "info": "Which executable(s) a profile will attach to.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mode", "info": "How the policy is applied.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sha1", "info": "A unique hash that identifies this policy.", "section": "Column", "boost": 99, "detail": "(text)"}], "appcompat_shims": [{"label": "executable", "info": "Name of the executable that is being shimmed. This is pulled from the registry.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "This is the path to the SDB database.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Description of the SDB.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "install_time", "info": "Install time of the SDB", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "Type of the SDB database.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sdb_id", "info": "Unique GUID of the SDB.", "section": "Column", "boost": 99, "detail": "(text)"}], "apps": [{"label": "name", "info": "Name of the Name.app folder", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Absolute and full Name.app path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bundle_executable", "info": "Info properties CFBundleExecutable label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bundle_identifier", "info": "Info properties CFBundleIdentifier label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bundle_name", "info": "Info properties CFBundleName label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bundle_short_version", "info": "Info properties CFBundleShortVersionString label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bundle_version", "info": "Info properties CFBundleVersion label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bundle_package_type", "info": "Info properties CFBundlePackageType label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "environment", "info": "Application-set environment variables", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "element", "info": "Does the app identify as a background agent", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "compiler", "info": "Info properties DTCompiler label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "development_region", "info": "Info properties CFBundleDevelopmentRegion label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "display_name", "info": "Info properties CFBundleDisplayName label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "info_string", "info": "Info properties CFBundleGetInfoString label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "minimum_system_version", "info": "Minimum version of macOS required for the app to run", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "category", "info": "The UTI that categorizes the app for the App Store", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "applescript_enabled", "info": "Info properties NSAppleScriptEnabled label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "copyright", "info": "Info properties NSHumanReadableCopyright label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_opened_time", "info": "The time that the app was last used", "section": "Column", "boost": 99, "detail": "(double)"}], "apt_sources": [{"label": "name", "info": "Repository name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Source file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "base_uri", "info": "Repository base URI", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "release", "info": "Release name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Repository source version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "maintainer", "info": "Repository maintainer", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "components", "info": "Repository components", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "architectures", "info": "Repository architectures", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "arp_cache": [{"label": "address", "info": "IPv4 address target", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mac", "info": "MAC address of broadcasted address", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "interface", "info": "Interface of the network for the MAC", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "permanent", "info": "1 for true, 0 for false", "section": "Column", "boost": 99, "detail": "(text)"}], "asl": [{"label": "time", "info": "Unix timestamp.  Set automatically", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "time_nano_sec", "info": "Nanosecond time.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "host", "info": "Sender's address (set by the server).", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sender", "info": "Sender's identification string.  Default is process name.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "facility", "info": "Sender's facility.  <PERSON><PERSON><PERSON> is 'user'.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Sending process ID encoded as a string.  Set automatically.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "gid", "info": "GID that sent the log message (set by the server).", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uid", "info": "UID that sent the log message (set by the server).", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "level", "info": "Log level number.  See levels in asl.h.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "message", "info": "Message text.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ref_pid", "info": "Reference PID for messages proxied by launchd", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ref_proc", "info": "Reference process for messages proxied by launchd", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "extra", "info": "Extra columns, in JSON format. Queries against this column are performed entirely in SQLite, so do not benefit from efficient querying via asl.h.", "section": "Column", "boost": 99, "detail": "(text)"}], "augeas": [{"label": "node", "info": "The node path of the configuration item", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "The value of the configuration item", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "label", "info": "The label of the configuration item", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "The path to the configuration file", "section": "Column", "boost": 99, "detail": "(text)"}], "authenticode": [{"label": "path", "info": "Must provide a path or directoryRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "original_program_name", "info": "The original program name that the publisher has signed", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial_number", "info": "The certificate serial number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "issuer_name", "info": "The certificate issuer name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subject_name", "info": "The certificate subject name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "result", "info": "The signature check result", "section": "Column", "boost": 99, "detail": "(text)"}], "authorization_mechanisms": [{"label": "label", "info": "Label of the authorization right", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "plugin", "info": "Authorization plugin name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mechanism", "info": "Name of the mechanism that will be called", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "privileged", "info": "If privileged it will run as root, else as an anonymous user", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "entry", "info": "The whole string entry", "section": "Column", "boost": 99, "detail": "(text)"}], "authorizations": [{"label": "label", "info": "Item name, usually in reverse domain format", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "modified", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "allow_root", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "timeout", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "tries", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "authenticate_user", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "shared", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "comment", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "created", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "class", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "session_owner", "info": "Label top-level key", "section": "Column", "boost": 99, "detail": "(text)"}], "authorized_keys": [{"label": "uid", "info": "The local owner of authorized_keys file", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "algorithm", "info": "Key type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Key encoded as base64", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "options", "info": "Optional list of login options", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "comment", "info": "Optional comment", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key_file", "info": "Path to the authorized_keys file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "autoexec": [{"label": "path", "info": "Path to the executable", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Name of the program", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Source table of the autoexec item", "section": "Column", "boost": 99, "detail": "(text)"}], "azure_instance_metadata": [{"label": "location", "info": "Azure Region the VM is running in", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Name of the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "offer", "info": "Offer information for the VM image (Azure image gallery VMs only)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "publisher", "info": "Publisher of the VM image", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sku", "info": "SKU for the VM image", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Version of the VM image", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "os_type", "info": "Linux or Windows", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "platform_update_domain", "info": "Update domain the VM is running in", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "platform_fault_domain", "info": "Fault domain the VM is running in", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vm_id", "info": "Unique identifier for the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vm_size", "info": "VM size", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subscription_id", "info": "Azure subscription for the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "resource_group_name", "info": "Resource group for the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "placement_group_id", "info": "Placement group for the VM scale set", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vm_scale_set_name", "info": "VM scale set name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "zone", "info": "Availability zone of the VM", "section": "Column", "boost": 99, "detail": "(text)"}], "azure_instance_tags": [{"label": "vm_id", "info": "Unique identifier for the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "The tag key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "The tag value", "section": "Column", "boost": 99, "detail": "(text)"}], "background_activities_moderator": [{"label": "path", "info": "Application file path.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_execution_time", "info": "Most recent time application was executed.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "sid", "info": "User SID.", "section": "Column", "boost": 99, "detail": "(text)"}], "battery": [{"label": "manufacturer", "info": "The battery manufacturer's name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "manufacture_date", "info": "The date the battery was manufactured UNIX Epoch", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "model", "info": "The battery's model number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial_number", "info": "The battery's unique serial number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cycle_count", "info": "The number of charge/discharge cycles", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "health", "info": "One of the following: \"Good\" describes a well-performing battery, \"Fair\" describes a functional battery with limited capacity, or \"Poor\" describes a battery that's not capable of providing power", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "condition", "info": "One of the following: \"Normal\" indicates the condition of the battery is within normal tolerances, \"Service Needed\" indicates that the battery should be checked out by a licensed Mac repair service, \"Permanent Failure\" indicates the battery needs replacement", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state", "info": "One of the following: \"AC Power\" indicates the battery is connected to an external power source, \"Battery Power\" indicates that the battery is drawing internal power, \"Off Line\" indicates the battery is off-line or no longer connected", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "charging", "info": "1 if the battery is currently being charged by a power source. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "charged", "info": "1 if the battery is currently completely charged. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "designed_capacity", "info": "The battery's designed capacity in mAh", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "max_capacity", "info": "The battery's actual capacity when it is fully charged in mAh", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "current_capacity", "info": "The battery's current charged capacity in mAh", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "percent_remaining", "info": "The percentage of battery remaining before it is drained", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "amperage", "info": "The battery's current amperage in mA", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "voltage", "info": "The battery's current voltage in mV", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "minutes_until_empty", "info": "The number of minutes until the battery is fully depleted. This value is -1 if this time is still being calculated", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "minutes_to_full_charge", "info": "The number of minutes until the battery is fully charged. This value is -1 if this time is still being calculated", "section": "Column", "boost": 99, "detail": "(integer)"}], "bitlocker_info": [{"label": "device_id", "info": "ID of the encrypted drive.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "drive_letter", "info": "Drive letter of the encrypted drive.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "persistent_volume_id", "info": "Persistent ID of the drive.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "conversion_status", "info": "The bitlocker conversion status of the drive.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "protection_status", "info": "The bitlocker protection status of the drive.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "encryption_method", "info": "The encryption type of the device.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "The FVE metadata version of the drive.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "percentage_encrypted", "info": "The percentage of the drive that is encrypted.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "lock_status", "info": "The accessibility status of the drive from Windows.", "section": "Column", "boost": 99, "detail": "(integer)"}], "block_devices": [{"label": "name", "info": "Block device name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "parent", "info": "Block device parent name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vendor", "info": "Block device vendor string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "model", "info": "Block device model string identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Block device size in blocks", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "block_size", "info": "Block size in bytes", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "uuid", "info": "Block device Universally Unique Identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Block device type string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "label", "info": "Block device label string", "section": "Column", "boost": 99, "detail": "(text)"}], "bpf_process_events": [{"label": "tid", "info": "Thread ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pid", "info": "Process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "parent", "info": "Parent process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uid", "info": "User ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "cid", "info": "Cgroup ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "exit_code", "info": "Exit code of the system call", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "probe_error", "info": "Set to 1 if one or more buffers could not be captured", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "syscall", "info": "System call name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Binary path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cwd", "info": "Current working directory", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cmdline", "info": "Command line arguments", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "duration", "info": "How much time was spent inside the syscall (nsecs)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "json_cmdline", "info": "Command line arguments, in JSON format", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ntime", "info": "The nsecs uptime timestamp as obtained from BPF", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(integer)"}], "bpf_socket_events": [{"label": "tid", "info": "Thread ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pid", "info": "Process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "parent", "info": "Parent process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uid", "info": "User ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "cid", "info": "Cgroup ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "exit_code", "info": "Exit code of the system call", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "probe_error", "info": "Set to 1 if one or more buffers could not be captured", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "syscall", "info": "System call name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path of executed file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "fd", "info": "The file description for the process socket", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "family", "info": "The Internet protocol family ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "The socket type", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "protocol", "info": "The network protocol ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "local_address", "info": "Local address associated with socket", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "remote_address", "info": "Remote address associated with socket", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "local_port", "info": "Local network protocol port number", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "remote_port", "info": "Remote network protocol port number", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "duration", "info": "How much time was spent inside the syscall (nsecs)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ntime", "info": "The nsecs uptime timestamp as obtained from BPF", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(integer)"}], "browser_plugins": [{"label": "uid", "info": "The local user that owns the plugin", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "name", "info": "Plugin display name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "identifier", "info": "Plugin identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Plugin short version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sdk", "info": "Build SDK used to compile plugin", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Plugin description text", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "development_region", "info": "Plugin language-localization", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "native", "info": "Plugin requires native execution", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "path", "info": "Path to plugin bundle", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "disabled", "info": "Is the plugin disabled. 1 = Disabled", "section": "Column", "boost": 99, "detail": "(integer)"}], "carbon_black_info": [{"label": "sensor_id", "info": "Sensor ID of the Carbon Black sensor", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "config_name", "info": "Sensor group", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "collect_store_files", "info": "If the sensor is configured to send back binaries to the Carbon Black server", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_module_loads", "info": "If the sensor is configured to capture module loads", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_module_info", "info": "If the sensor is configured to collect metadata of binaries", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_file_mods", "info": "If the sensor is configured to collect file modification events", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_reg_mods", "info": "If the sensor is configured to collect registry modification events", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_net_conns", "info": "If the sensor is configured to collect network connections", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_processes", "info": "If the sensor is configured to process events", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_cross_processes", "info": "If the sensor is configured to cross process events", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_emet_events", "info": "If the sensor is configured to EMET events", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_data_file_writes", "info": "If the sensor is configured to collect non binary file writes", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_process_user_context", "info": "If the sensor is configured to collect the user running a process", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "collect_sensor_operations", "info": "Unknown", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "log_file_disk_quota_mb", "info": "Event file disk quota in MB", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "log_file_disk_quota_percentage", "info": "Event file disk quota in a percentage", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "protection_disabled", "info": "If the sensor is configured to report tamper events", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "sensor_ip_addr", "info": "IP address of the sensor", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sensor_backend_server", "info": "Carbon Black server", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "event_queue", "info": "Size in bytes of Carbon Black event files on disk", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "binary_queue", "info": "Size in bytes of binaries waiting to be sent to Carbon Black server", "section": "Column", "boost": 99, "detail": "(integer)"}], "carves": [{"label": "time", "info": "Time at which the carve was kicked off", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "sha256", "info": "A SHA256 sum of the carved archive", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Size of the carved archive", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "path", "info": "The path of the requested carve", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "Status of the carve, can be STARTING, PENDING, SUCCESS, or FAILED", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "carve_guid", "info": "Identifying value of the carve session", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "request_id", "info": "Identifying value of the carve request (e.g., scheduled query name, distributed request, etc)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "carve", "info": "Set this value to '1' to start a file carve", "section": "Column", "boost": 99, "detail": "(integer)"}], "certificates": [{"label": "common_name", "info": "Certificate CommonName", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subject", "info": "Certificate distinguished name (deprecated, use subject2)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "issuer", "info": "Certificate issuer distinguished name (deprecated, use issuer2)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ca", "info": "1 if CA: true (certificate is an authority) else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "self_signed", "info": "1 if self-signed, else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "not_valid_before", "info": "Lower bound of valid date", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "not_valid_after", "info": "Certificate expiration data", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "signing_algorithm", "info": "Signing algorithm used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key_algorithm", "info": "Key algorithm used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key_strength", "info": "Key size used for RSA/DSA, or curve name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key_usage", "info": "Certificate key usage and extended key usage", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subject_key_id", "info": "SKID an optionally included SHA1", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "authority_key_id", "info": "AKID an optionally included SHA1", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sha1", "info": "SHA1 hash of the raw certificate contents", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path to Keychain or PEM bundle", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial", "info": "Certificate serial number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sid", "info": "SID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "store_location", "info": "Certificate system store location", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "store", "info": "Certificate system store", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "username", "info": "Username", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "store_id", "info": "Exists for service/user stores. Contains raw store id provided by WinAPI.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "issuer2", "info": "Certificate issuer distinguished name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subject2", "info": "Certificate distinguished name", "section": "Column", "boost": 99, "detail": "(text)"}], "chassis_info": [{"label": "audible_alarm", "info": "If TRUE, the frame is equipped with an audible alarm.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "breach_description", "info": "If provided, gives a more detailed description of a detected security breach.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "chassis_types", "info": "A comma-separated list of chassis types, such as Desktop or Laptop.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "An extended description of the chassis if available.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "lock", "info": "If TRUE, the frame is equipped with a lock.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "manufacturer", "info": "The manufacturer of the chassis.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "model", "info": "The model of the chassis.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "security_breach", "info": "The physical status of the chassis such as B<PERSON><PERSON> Successful, Breach Attempted, etc.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial", "info": "The serial number of the chassis.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "smbios_tag", "info": "The assigned asset tag number of the chassis.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sku", "info": "The Stock Keeping Unit number if available.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "If available, gives various operational or nonoperational statuses such as OK, Degraded, and Pred Fail.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "visible_alarm", "info": "If TRUE, the frame is equipped with a visual alarm.", "section": "Column", "boost": 99, "detail": "(text)"}], "chocolatey_packages": [{"label": "name", "info": "Package display name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Package-supplied version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "summary", "info": "Package-supplied summary", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "author", "info": "Optional package author", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "license", "info": "License under which package is launched", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path at which this package resides", "section": "Column", "boost": 99, "detail": "(text)"}], "chrome_extension_content_scripts": [{"label": "browser_type", "info": "The browser type (Valid values: chrome, chromium, opera, yandex, brave)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "The local user that owns the extension", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "identifier", "info": "Extension identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Extension-supplied version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "script", "info": "The content script used by the extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "match", "info": "The pattern that the script is matched against", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "profile_path", "info": "The profile path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path to extension folder", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "referenced", "info": "1 if this extension is referenced by the Preferences file of the profile", "section": "Column", "boost": 99, "detail": "(bigint)"}], "chrome_extensions": [{"label": "browser_type", "info": "The browser type (Valid values: chrome, chromium, opera, yandex, brave, edge, edge_beta)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "The local user that owns the extension", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "name", "info": "Extension display name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "profile", "info": "The name of the Chrome profile that contains this extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "profile_path", "info": "The profile path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "referenced_identifier", "info": "Extension identifier, as specified by the preferences file. Empty if the extension is not in the profile.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "identifier", "info": "Extension identifier, computed from its manifest. Empty in case of error.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Extension-supplied version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Extension-optional description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "default_locale", "info": "Default locale supported by extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "current_locale", "info": "Current locale supported by extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "update_url", "info": "Extension-supplied update URI", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "author", "info": "Optional extension author", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "persistent", "info": "1 If extension is persistent across all tabs else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "path", "info": "Path to extension folder", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "permissions", "info": "The permissions required by the extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "permissions_json", "info": "The JSON-encoded permissions required by the extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "optional_permissions", "info": "The permissions optionally required by the extensions", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "optional_permissions_json", "info": "The JSON-encoded permissions optionally required by the extensions", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "manifest_hash", "info": "The SHA256 hash of the manifest.json file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "referenced", "info": "1 if this extension is referenced by the Preferences file of the profile", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "from_webstore", "info": "True if this extension was installed from the web store", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state", "info": "1 if this extension is enabled", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "install_time", "info": "Extension install time, in its original Webkit format", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "install_timestamp", "info": "Extension install time, converted to unix time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "manifest_json", "info": "The manifest file of the extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "The extension key, from the manifest file", "section": "Column", "boost": 99, "detail": "(text)"}], "connected_displays": [{"label": "name", "info": "The name of the display.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "product_id", "info": "The product ID of the display.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial_number", "info": "The serial number of the display. (may not be unique)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vendor_id", "info": "The vendor ID of the display.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "manufactured_week", "info": "The manufacture week of the display. This field is 0 if not supported", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "manufactured_year", "info": "The manufacture year of the display. This field is 0 if not supported", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "display_id", "info": "The display ID.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pixels", "info": "The number of pixels of the display.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "resolution", "info": "The resolution of the display.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ambient_brightness_enabled", "info": "The ambient brightness setting associated with the display. This will be 1 if enabled and is 0 if disabled or not supported.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "connection_type", "info": "The connection type associated with the display.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "display_type", "info": "The type of display.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "main", "info": "If the display is the main display.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mirror", "info": "If the display is mirrored or not. This field is 1 if mirrored and 0 if not mirrored.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "online", "info": "The online status of the display. This field is 1 if the display is online and 0 if it is offline.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "rotation", "info": "The orientation of the display.", "section": "Column", "boost": 99, "detail": "(text)"}], "connectivity": [{"label": "disconnected", "info": "True if the all interfaces are not connected to any network", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv4_no_traffic", "info": "True if any interface is connected via IPv4, but has seen no traffic", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv6_no_traffic", "info": "True if any interface is connected via IPv6, but has seen no traffic", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv4_subnet", "info": "True if any interface is connected to the local subnet via IPv4", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv4_local_network", "info": "True if any interface is connected to a routed network via IPv4", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv4_internet", "info": "True if any interface is connected to the Internet via IPv4", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv6_subnet", "info": "True if any interface is connected to the local subnet via IPv6", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv6_local_network", "info": "True if any interface is connected to a routed network via IPv6", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv6_internet", "info": "True if any interface is connected to the Internet via IPv6", "section": "Column", "boost": 99, "detail": "(integer)"}], "cpu_info": [{"label": "device_id", "info": "The DeviceID of the CPU.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "model", "info": "The model of the CPU.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "manufacturer", "info": "The manufacturer of the CPU.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "processor_type", "info": "The processor type, such as Central, Math, or Video.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cpu_status", "info": "The current operating status of the CPU.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "number_of_cores", "info": "The number of cores of the CPU.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "logical_processors", "info": "The number of logical processors of the CPU.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "address_width", "info": "The width of the CPU address bus.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "current_clock_speed", "info": "The current frequency of the CPU.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "max_clock_speed", "info": "The maximum possible frequency of the CPU.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "socket_designation", "info": "The assigned socket on the board for the given CPU.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "availability", "info": "The availability and status of the CPU.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "number_of_efficiency_cores", "info": "The number of efficiency cores of the CPU. Only available on Apple Silicon", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "number_of_performance_cores", "info": "The number of performance cores of the CPU. Only available on Apple Silicon", "section": "Column", "boost": 99, "detail": "(integer)"}], "cpu_time": [{"label": "core", "info": "Name of the cpu (core)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "user", "info": "Time spent in user mode", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "nice", "info": "Time spent in user mode with low priority (nice)", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "system", "info": "Time spent in system mode", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "idle", "info": "Time spent in the idle task", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "iowait", "info": "Time spent waiting for <PERSON><PERSON><PERSON> to complete", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "irq", "info": "Time spent servicing interrupts", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "softirq", "info": "Time spent servicing softirqs", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "steal", "info": "Time spent in other operating systems when running in a virtualized environment", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "guest", "info": "Time spent running a virtual CPU for a guest OS under the control of the Linux kernel", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "guest_nice", "info": "Time spent running a niced guest ", "section": "Column", "boost": 99, "detail": "(bigint)"}], "cpuid": [{"label": "feature", "info": "Present feature flags", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Bit value or string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "output_register", "info": "Register used to for feature value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "output_bit", "info": "Bit in register value for feature value", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "input_eax", "info": "Value of EAX used", "section": "Column", "boost": 99, "detail": "(text)"}], "crashes": [{"label": "type", "info": "Type of crash log", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Process (or thread) ID of the crashed process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "path", "info": "Path to the crashed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "crash_path", "info": "Location of log file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "identifier", "info": "Identifier of the crashed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Version info of the crashed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "parent", "info": "Parent PID of the crashed process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "responsible", "info": "Process responsible for the crashed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "User ID of the crashed process", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "datetime", "info": "Date/Time at which the crash occurred", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "crashed_thread", "info": "Thread ID which crashed", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "stack_trace", "info": "Most recent frame from the stack trace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "exception_type", "info": "Exception type of the crash", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "exception_codes", "info": "Exception codes from the crash", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "exception_notes", "info": "Exception notes from the crash", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "registers", "info": "The value of the system registers", "section": "Column", "boost": 99, "detail": "(text)"}], "crontab": [{"label": "event", "info": "The job @event name (rare)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "minute", "info": "The exact minute for the job", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hour", "info": "The hour of the day for the job", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "day_of_month", "info": "The day of the month for the job", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "month", "info": "The month of the year for the job", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "day_of_week", "info": "The day of the week for the job", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "command", "info": "Raw command string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "File parsed", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "cups_destinations": [{"label": "name", "info": "Name of the printer", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "option_name", "info": "Option name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "option_value", "info": "Option value", "section": "Column", "boost": 99, "detail": "(text)"}], "cups_jobs": [{"label": "title", "info": "Title of the printed job", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "destination", "info": "The printer the job was sent to", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user", "info": "The user who printed the job", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "format", "info": "The format of the print job", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "The size of the print job", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "completed_time", "info": "When the job completed printing", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "processing_time", "info": "How long the job took to process", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "creation_time", "info": "When the print request was initiated", "section": "Column", "boost": 99, "detail": "(integer)"}], "curl": [{"label": "url", "info": "The url for the requestRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "method", "info": "The HTTP method for the request", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user_agent", "info": "The user-agent string to use for the request", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "response_code", "info": "The HTTP status code for the response", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "round_trip_time", "info": "Time taken to complete the request", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "bytes", "info": "Number of bytes in the response", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "result", "info": "The HTTP response body", "section": "Column", "boost": 99, "detail": "(text)"}], "curl_certificate": [{"label": "hostname", "info": "Hostname to CURL (domain[:port], e.g. osquery.io)Required in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "common_name", "info": "Common name of company issued to", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "organization", "info": "Organization issued to", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "organization_unit", "info": "Organization unit issued to", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial_number", "info": "Certificate serial number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "issuer_common_name", "info": "Issuer common name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "issuer_organization", "info": "Issuer organization", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "issuer_organization_unit", "info": "Issuer organization unit", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "valid_from", "info": "Period of validity start date", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "valid_to", "info": "Period of validity end date", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sha256_fingerprint", "info": "SHA-256 fingerprint", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sha1_fingerprint", "info": "SHA1 fingerprint", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Version Number", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "signature_algorithm", "info": "Signature Algorithm", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "signature", "info": "Signature", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subject_key_identifier", "info": "Subject Key Identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "authority_key_identifier", "info": "Authority Key Identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key_usage", "info": "Usage of key in certificate", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "extended_key_usage", "info": "Extended usage of key in certificate", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "policies", "info": "Certificate Policies", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subject_alternative_names", "info": "Subject Alternative Name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "issuer_alternative_names", "info": "Issuer Alternative Name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "info_access", "info": "Authority Information Access", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subject_info_access", "info": "Subject Information Access", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "policy_mappings", "info": "Policy Mappings", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "has_expired", "info": "1 if the certificate has expired, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "basic_constraint", "info": "Basic Constraints", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name_constraints", "info": "Name Constraints", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "policy_constraints", "info": "Policy Constraints", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dump_certificate", "info": "Set this value to '1' to dump certificate", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "timeout", "info": "Set this value to the timeout in seconds to complete the TLS handshake (default 4s, use 0 for no timeout)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "pem", "info": "Certificate PEM format", "section": "Column", "boost": 99, "detail": "(text)"}], "deb_packages": [{"label": "name", "info": "Package name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Package version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Package source", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Package size in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "arch", "info": "Package architecture", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "revision", "info": "Package revision", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "Package status", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "maintainer", "info": "Package maintainer", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "section", "info": "Package section", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "priority", "info": "Package priority", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "<PERSON><PERSON><PERSON>", "info": "libdpkg admindir. Defaults to /var/lib/dpkg", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mount_namespace_id", "info": "Mount namespace id", "section": "Column", "boost": 99, "detail": "(text)"}], "default_environment": [{"label": "variable", "info": "Name of the environment variable", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Value of the environment variable", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "expand", "info": "1 if the variable needs expanding, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}], "device_file": [{"label": "device", "info": "Absolute file path to device nodeRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "partition", "info": "A partition numberRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "A logical path within the device node", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "filename", "info": "Name portion of file path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "inode", "info": "Filesystem inode number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uid", "info": "Owning user ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Owning group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "mode", "info": "Permission bits", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Size of file in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "block_size", "info": "Block size of filesystem", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "atime", "info": "Last access time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "mtime", "info": "Last modification time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ctime", "info": "Creation time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "hard_links", "info": "Number of hard links", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "File status", "section": "Column", "boost": 99, "detail": "(text)"}], "device_firmware": [{"label": "type", "info": "Type of device", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "device", "info": "The device name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Firmware version", "section": "Column", "boost": 99, "detail": "(text)"}], "device_hash": [{"label": "device", "info": "Absolute file path to device nodeRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "partition", "info": "A partition numberRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "inode", "info": "Filesystem inode numberRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "md5", "info": "MD5 hash of provided inode data", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sha1", "info": "SHA1 hash of provided inode data", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sha256", "info": "SHA256 hash of provided inode data", "section": "Column", "boost": 99, "detail": "(text)"}], "device_partitions": [{"label": "device", "info": "Absolute file path to device nodeRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "partition", "info": "A partition number or description", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "label", "info": "", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "offset", "info": "", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "blocks_size", "info": "Byte size of each block", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "blocks", "info": "Number of blocks", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "inodes", "info": "Number of meta nodes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "flags", "info": "", "section": "Column", "boost": 99, "detail": "(integer)"}], "disk_encryption": [{"label": "name", "info": "Disk name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uuid", "info": "Disk Universally Unique Identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "encrypted", "info": "1 If encrypted: true (disk is encrypted), else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "Description of cipher type and mode if available", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "encryption_status", "info": "Disk encryption status with one of following values: encrypted | not encrypted | undefined", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "Currently authenticated user if available", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user_uuid", "info": "UUID of authenticated user if available", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "filevault_status", "info": "FileVault status with one of following values: on | off | unknown", "section": "Column", "boost": 99, "detail": "(text)"}], "disk_events": [{"label": "action", "info": "Appear or disappear", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path of the DMG file accessed", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Disk event name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "device", "info": "Disk event BSD name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uuid", "info": "UUID of the volume inside DMG if available", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Size of partition in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ejectable", "info": "1 if ejectable, 0 if not", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mountable", "info": "1 if mountable, 0 if not", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "writable", "info": "1 if writable, 0 if not", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "content", "info": "Disk event content", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "media_name", "info": "Disk event media name string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vendor", "info": "Disk event vendor string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "filesystem", "info": "Filesystem if available", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "checksum", "info": "UDIF Master checksum if available (CRC32)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time of appearance/disappearance in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "disk_info": [{"label": "partitions", "info": "Number of detected partitions on disk.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "disk_index", "info": "Physical drive number of the disk.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "The interface type of the disk.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "id", "info": "The unique identifier of the drive on the system.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pnp_device_id", "info": "The unique identifier of the drive on the system.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "disk_size", "info": "Size of the disk.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "manufacturer", "info": "The manufacturer of the disk.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hardware_model", "info": "Hard drive model.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "The label of the disk object.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial", "info": "The serial number of the disk.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "The OS's description of the disk.", "section": "Column", "boost": 99, "detail": "(text)"}], "dns_cache": [{"label": "name", "info": "DNS record name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "DNS record type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "flags", "info": "DNS record flags", "section": "Column", "boost": 99, "detail": "(integer)"}], "dns_resolvers": [{"label": "id", "info": "Address type index or order", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "Address type: sortlist, nameserver, search", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "address", "info": "Resolver IP/IPv6 address", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "netmask", "info": "Address (sortlist) netmask length", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "options", "info": "Resolver options", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "docker_container_envs": [{"label": "id", "info": "Container ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Environment variable name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Environment variable value", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_container_fs_changes": [{"label": "id", "info": "Container IDRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "FIle or directory path relative to rootfs", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "change_type", "info": "Type of change: C:Modified, A:Added, D:Deleted", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_container_labels": [{"label": "id", "info": "Container ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Label key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Optional label value", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_container_mounts": [{"label": "id", "info": "Container ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Type of mount (bind, volume)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Optional mount name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Source path on host", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "destination", "info": "Destination path inside container", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "driver", "info": "Driver providing the mount", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mode", "info": "Mount options (rw, ro)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "rw", "info": "1 if read/write. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "propagation", "info": "Mount propagation", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_container_networks": [{"label": "id", "info": "Container ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Network name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "network_id", "info": "Network ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "endpoint_id", "info": "Endpoint ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "gateway", "info": "Gateway", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ip_address", "info": "IP address", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ip_prefix_len", "info": "IP subnet prefix length", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv6_gateway", "info": "IPv6 gateway", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ipv6_address", "info": "IPv6 address", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ipv6_prefix_len", "info": "IPv6 subnet prefix length", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mac_address", "info": "MAC address", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_container_ports": [{"label": "id", "info": "Container ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Protocol (tcp, udp)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "port", "info": "Port inside the container", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "host_ip", "info": "Host IP address on which public port is listening", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "host_port", "info": "Host port", "section": "Column", "boost": 99, "detail": "(integer)"}], "docker_container_processes": [{"label": "id", "info": "Container IDRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "name", "info": "The process path or shorthand argv[0]", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cmdline", "info": "Complete argv", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state", "info": "Process state", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "User ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "euid", "info": "Effective user ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "egid", "info": "Effective group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "suid", "info": "Saved user ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "sgid", "info": "Saved group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "wired_size", "info": "Bytes of unpageable memory used by process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "resident_size", "info": "Bytes of private memory used by process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "total_size", "info": "Total virtual memory size", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "start_time", "info": "Process start in seconds since boot (non-sleeping)", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "parent", "info": "Process parent's PID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pgroup", "info": "Process group", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "threads", "info": "Number of threads used by process", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "nice", "info": "Process nice level (-20 to 20, default 0)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "user", "info": "User name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Cumulative CPU time. [DD-]HH:MM:SS format", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cpu", "info": "CPU utilization as percentage", "section": "Column", "boost": 99, "detail": "(double)"}, {"label": "mem", "info": "Memory utilization as percentage", "section": "Column", "boost": 99, "detail": "(double)"}], "docker_container_stats": [{"label": "id", "info": "Container IDRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Container name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pids", "info": "Number of processes", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "read", "info": "UNIX time when stats were read", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "preread", "info": "UNIX time when stats were last read", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "interval", "info": "Difference between read and preread in nano-seconds", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "disk_read", "info": "Total disk read bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "disk_write", "info": "Total disk write bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "num_procs", "info": "Number of processors", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_total_usage", "info": "Total CPU usage", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "cpu_kernelmode_usage", "info": "CPU kernel mode usage", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "cpu_usermode_usage", "info": "CPU user mode usage", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "system_cpu_usage", "info": "CPU system usage", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "online_cpus", "info": "Online CPUs", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "pre_cpu_total_usage", "info": "Last read total CPU usage", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pre_cpu_kernelmode_usage", "info": "Last read CPU kernel mode usage", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pre_cpu_usermode_usage", "info": "Last read CPU user mode usage", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pre_system_cpu_usage", "info": "Last read CPU system usage", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pre_online_cpus", "info": "Last read online CPUs", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "memory_usage", "info": "Memory usage", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "memory_cached", "info": "Memory cached", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "memory_max_usage", "info": "Memory maximum usage", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "memory_limit", "info": "Memory limit", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "network_rx_bytes", "info": "Total network bytes read", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "network_tx_bytes", "info": "Total network bytes transmitted", "section": "Column", "boost": 99, "detail": "(bigint)"}], "docker_containers": [{"label": "id", "info": "Container ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Container name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "image", "info": "Docker image (name) used to launch this container", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "image_id", "info": "Docker image ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "command", "info": "Command with arguments", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "created", "info": "Time of creation as UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "state", "info": "Container state (created, restarting, running, removing, paused, exited, dead)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "Container status information", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Identifier of the initial process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "path", "info": "Container path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "config_entrypoint", "info": "Container entrypoint(s)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "started_at", "info": "Container start time as string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "finished_at", "info": "Container finish time as string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "privileged", "info": "Is the container privileged", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "security_options", "info": "List of container security options", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "env_variables", "info": "Container environmental variables", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "readonly_rootfs", "info": "Is the root filesystem mounted as read only", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cgroup_namespace", "info": "cgroup namespace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ipc_namespace", "info": "IPC namespace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mnt_namespace", "info": "Mount namespace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "net_namespace", "info": "Network namespace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_namespace", "info": "PID namespace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user_namespace", "info": "User namespace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uts_namespace", "info": "UTS namespace", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_image_history": [{"label": "id", "info": "Image ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "created", "info": "Time of creation as UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "size", "info": "Size of instruction in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "created_by", "info": "Created by instruction", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "tags", "info": "Comma-separated list of tags", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "comment", "info": "Instruction comment", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_image_labels": [{"label": "id", "info": "Image ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Label key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Optional label value", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_image_layers": [{"label": "id", "info": "Image ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "layer_id", "info": "Layer ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "layer_order", "info": "Layer Order (1 = base layer)", "section": "Column", "boost": 99, "detail": "(integer)"}], "docker_images": [{"label": "id", "info": "Image ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "created", "info": "Time of creation as UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "size_bytes", "info": "Size of image in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "tags", "info": "Comma-separated list of repository tags", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_info": [{"label": "id", "info": "Docker system ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "containers", "info": "Total number of containers", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "containers_running", "info": "Number of containers currently running", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "containers_paused", "info": "Number of containers in paused state", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "containers_stopped", "info": "Number of containers in stopped state", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "images", "info": "Number of images", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "storage_driver", "info": "Storage driver", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "memory_limit", "info": "1 if memory limit support is enabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "swap_limit", "info": "1 if swap limit support is enabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "kernel_memory", "info": "1 if kernel memory limit support is enabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_cfs_period", "info": "1 if CPU Completely Fair Scheduler (CFS) period support is enabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_cfs_quota", "info": "1 if CPU Completely Fair Scheduler (CFS) quota support is enabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_shares", "info": "1 if CPU share weighting support is enabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_set", "info": "1 if CPU set selection support is enabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv4_forwarding", "info": "1 if IPv4 forwarding is enabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "bridge_nf_iptables", "info": "1 if bridge netfilter iptables is enabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "bridge_nf_ip6tables", "info": "1 if bridge netfilter ip6tables is enabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "oom_kill_disable", "info": "1 if Out-of-memory kill is disabled. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "logging_driver", "info": "Logging driver", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cgroup_driver", "info": "Control groups driver", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "kernel_version", "info": "Kernel version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "os", "info": "Operating system", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "os_type", "info": "Operating system type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "architecture", "info": "Hardware architecture", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cpus", "info": "Number of CPUs", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "memory", "info": "Total memory", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "http_proxy", "info": "HTTP proxy", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "https_proxy", "info": "HTTPS proxy", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "no_proxy", "info": "Comma-separated list of domain extensions proxy should not be used for", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Name of the docker host", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "server_version", "info": "Server version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "root_dir", "info": "Docker root directory", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_network_labels": [{"label": "id", "info": "Network ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Label key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Optional label value", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_networks": [{"label": "id", "info": "Network ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Network name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "driver", "info": "Network driver", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "created", "info": "Time of creation as UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "enable_ipv6", "info": "1 if IPv6 is enabled on this network. 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "subnet", "info": "Network subnet", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "gateway", "info": "Network gateway", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_version": [{"label": "version", "info": "Docker version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "api_version", "info": "API version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "min_api_version", "info": "Minimum API version supported", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "git_commit", "info": "Docker build git commit", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "go_version", "info": "Go version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "os", "info": "Operating system", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "arch", "info": "Hardware architecture", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "kernel_version", "info": "Kernel version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "build_time", "info": "Build time", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_volume_labels": [{"label": "name", "info": "Volume name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Label key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Optional label value", "section": "Column", "boost": 99, "detail": "(text)"}], "docker_volumes": [{"label": "name", "info": "Volume name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "driver", "info": "Volume driver", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mount_point", "info": "Mount point", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Volume type", "section": "Column", "boost": 99, "detail": "(text)"}], "drivers": [{"label": "device_id", "info": "Device ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "device_name", "info": "Device name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "image", "info": "Path to driver image file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Driver description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "service", "info": "Driver service name, if one exists", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "service_key", "info": "Driver service registry key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Driver version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "inf", "info": "Associated inf file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "class", "info": "Device/driver class name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "provider", "info": "Driver provider", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "manufacturer", "info": "Device manufacturer", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "driver_key", "info": "Driver key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "date", "info": "Driver date", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "signed", "info": "Whether the driver is signed or not", "section": "Column", "boost": 99, "detail": "(integer)"}], "ec2_instance_metadata": [{"label": "instance_id", "info": "EC2 instance ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "instance_type", "info": "EC2 instance type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "architecture", "info": "Hardware architecture of this EC2 instance", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "region", "info": "AWS region in which this instance launched", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "availability_zone", "info": "Availability zone in which this instance launched", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "local_hostname", "info": "Private IPv4 DNS hostname of the first interface of this instance", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "local_ipv4", "info": "Private IPv4 address of the first interface of this instance", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mac", "info": "MAC address for the first network interface of this EC2 instance", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "security_groups", "info": "Comma separated list of security group names", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "iam_arn", "info": "If there is an IAM role associated with the instance, contains instance profile ARN", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ami_id", "info": "AMI ID used to launch this EC2 instance", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "reservation_id", "info": "ID of the reservation", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "account_id", "info": "AWS account ID which owns this EC2 instance", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ssh_public_key", "info": "SSH public key. Only available if supplied at instance launch time", "section": "Column", "boost": 99, "detail": "(text)"}], "ec2_instance_tags": [{"label": "instance_id", "info": "EC2 instance ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Tag key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Tag value", "section": "Column", "boost": 99, "detail": "(text)"}], "es_process_events": [{"label": "version", "info": "Version of EndpointSecurity event", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "seq_num", "info": "Per event sequence number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "global_seq_num", "info": "Global sequence number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "path", "info": "Path of executed file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "parent", "info": "Parent process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "original_parent", "info": "Original parent process ID in case of reparenting", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "cmdline", "info": "Command line arguments (argv)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cmdline_count", "info": "Number of command line arguments", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "env", "info": "Environment variables delimited by spaces", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "env_count", "info": "Number of environment variables", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "cwd", "info": "The process current working directory", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "User ID of the process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "euid", "info": "Effective User ID of the process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Group ID of the process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "egid", "info": "Effective Group ID of the process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "username", "info": "Username", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "signing_id", "info": "Signature identifier of the process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "team_id", "info": "Team identifier of the process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "c<PERSON>sh", "info": "Codesigning hash of the process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "platform_binary", "info": "Indicates if the binary is Apple signed binary (1) or not (0)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "exit_code", "info": "Exit code of a process in case of an exit event", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "child_pid", "info": "Process ID of a child process in case of a fork event", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "event_type", "info": "Type of EndpointSecurity event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "codesigning_flags", "info": "Codesigning flags matching one of these options, in a comma separated list: NOT_VALID, ADHOC, NOT_RUNTIME, INSTALLER. See kern/cs_blobs.h in XNU for descriptions.", "section": "Column", "boost": 99, "detail": "(text)"}], "es_process_file_events": [{"label": "version", "info": "Version of EndpointSecurity event", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "seq_num", "info": "Per event sequence number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "global_seq_num", "info": "Global sequence number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "parent", "info": "Parent process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "path", "info": "Path of executed file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "filename", "info": "The source or target filename for the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dest_filename", "info": "Destination filename for the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "event_type", "info": "Type of EndpointSecurity event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "etc_hosts": [{"label": "address", "info": "IP address mapping", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hostnames", "info": "Raw hosts mapping", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "etc_protocols": [{"label": "name", "info": "Protocol name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "number", "info": "Protocol number", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "alias", "info": "Protocol alias", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "comment", "info": "Comment with protocol description", "section": "Column", "boost": 99, "detail": "(text)"}], "etc_services": [{"label": "name", "info": "Service name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "port", "info": "Service port number", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "protocol", "info": "Transport protocol (TCP/UDP)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "aliases", "info": "Optional space separated list of other names for a service", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "comment", "info": "Optional comment for a service.", "section": "Column", "boost": 99, "detail": "(text)"}], "event_taps": [{"label": "enabled", "info": "Is the Event Tap enabled", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "event_tap_id", "info": "Unique ID for the Tap", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "event_tapped", "info": "The mask that identifies the set of events to be observed.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "process_being_tapped", "info": "The process ID of the target application", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "tapping_process", "info": "The process ID of the application that created the event tap.", "section": "Column", "boost": 99, "detail": "(integer)"}], "extended_attributes": [{"label": "path", "info": "Absolute file pathRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "directory", "info": "Directory of file(s)Required in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Name of the value generated from the extended attribute", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "The parsed information from the attribute", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "base64", "info": "1 if the value is base64 encoded else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "fan_speed_sensors": [{"label": "fan", "info": "Fan number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Fan name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "actual", "info": "Actual speed", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "min", "info": "Minimum speed", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "max", "info": "Maximum speed", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "target", "info": "Target speed", "section": "Column", "boost": 99, "detail": "(integer)"}], "file": [{"label": "path", "info": "Absolute file pathRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "directory", "info": "Directory of file(s)Required in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "filename", "info": "Name portion of file path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "inode", "info": "Filesystem inode number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uid", "info": "Owning user ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Owning group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "mode", "info": "Permission bits", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "device", "info": "Device ID (optional)", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "size", "info": "Size of file in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "block_size", "info": "Block size of filesystem", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "atime", "info": "Last access time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "mtime", "info": "Last modification time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ctime", "info": "Last status change time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "btime", "info": "(B)irth or (cr)eate time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "hard_links", "info": "Number of hard links", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "symlink", "info": "1 if the path is a symlink, otherwise 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "File status", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "attributes", "info": "File attrib string. See: https://ss64.com/nt/attrib.html", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "volume_serial", "info": "Volume serial number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "file_id", "info": "file ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "file_version", "info": "File version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "product_version", "info": "File product version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "original_filename", "info": "(Executable files only) Original filename", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "shortcut_target_path", "info": "Full path to the file the shortcut points to", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "shortcut_target_type", "info": "Display name for the target type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "shortcut_target_location", "info": "Folder name where the shortcut target resides", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "shortcut_start_in", "info": "Full path to the working directory to use when executing the shortcut target", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "shortcut_run", "info": "Window mode the target of the shortcut should be run in", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "shortcut_comment", "info": "Comment on the shortcut", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bsd_flags", "info": "The BSD file flags (chflags). Possible values: NODUMP, UF_IMMUTABLE, UF_APPEND, OPAQUE, HIDDEN, ARCHIVED, SF_IMMUTABLE, SF_APPEND", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mount_namespace_id", "info": "Mount namespace id", "section": "Column", "boost": 99, "detail": "(text)"}], "file_events": [{"label": "target_path", "info": "The path associated with the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "category", "info": "The category of the file defined in the config", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "action", "info": "Change action (UPDATE, REMOVE, etc)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "transaction_id", "info": "ID used during bulk update", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "inode", "info": "Filesystem inode number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uid", "info": "Owning user ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Owning group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "mode", "info": "Permission bits", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Size of file in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "atime", "info": "Last access time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "mtime", "info": "Last modification time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ctime", "info": "Last status change time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "md5", "info": "The MD5 of the file after change", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sha1", "info": "The SHA1 of the file after change", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sha256", "info": "The SHA256 of the file after change", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hashed", "info": "1 if the file was hashed, 0 if not, -1 if hashing failed", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "time", "info": "Time of file event", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "firefox_addons": [{"label": "uid", "info": "The local user that owns the addon", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "name", "info": "Addon display name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "identifier", "info": "Addon identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "creator", "info": "Addon-supported creator string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Extension, addon, webapp", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Addon-supplied version string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Addon-supplied description string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source_url", "info": "URL that installed the addon", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "visible", "info": "1 If the addon is shown in browser else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "active", "info": "1 If the addon is active else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "disabled", "info": "1 If the addon is application-disabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "autoupdate", "info": "1 If the addon applies background updates else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "location", "info": "Global, profile location", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path to plugin bundle", "section": "Column", "boost": 99, "detail": "(text)"}], "gatekeeper": [{"label": "assessments_enabled", "info": "1 If a Gatekeeper is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "dev_id_enabled", "info": "1 If a Gatekeeper allows execution from identified developers else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "version", "info": "Version of Gatekeeper's gke.bundle", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "opaque_version", "info": "Version of Gatekeeper's gkopaque.bundle", "section": "Column", "boost": 99, "detail": "(text)"}], "gatekeeper_approved_apps": [{"label": "path", "info": "Path of executable allowed to run", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "requirement", "info": "Code signing requirement language", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ctime", "info": "Last change time", "section": "Column", "boost": 99, "detail": "(double)"}, {"label": "mtime", "info": "Last modification time", "section": "Column", "boost": 99, "detail": "(double)"}], "groups": [{"label": "gid", "info": "Unsigned int64 group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid_signed", "info": "A signed int64 version of gid", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "groupname", "info": "Canonical local group name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "group_sid", "info": "Unique group ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "comment", "info": "Remarks or comments associated with the group", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "is_hidden", "info": "IsHidden attribute set in OpenDirectory", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "hardware_events": [{"label": "action", "info": "Remove, insert, change properties, etc", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Local device path assigned (optional)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Type of hardware and hardware event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "driver", "info": "Driver claiming the device", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vendor", "info": "Hardware device vendor", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vendor_id", "info": "Hex encoded Hardware vendor identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "model", "info": "Hardware device model", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "model_id", "info": "Hex encoded Hardware model identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial", "info": "Device serial (optional)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "revision", "info": "Device revision (optional)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time of hardware event", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "hash": [{"label": "path", "info": "Must provide a path or directoryRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "directory", "info": "Must provide a path or directoryRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "md5", "info": "MD5 hash of provided filesystem data", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sha1", "info": "SHA1 hash of provided filesystem data", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sha256", "info": "SHA256 hash of provided filesystem data", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mount_namespace_id", "info": "Mount namespace id", "section": "Column", "boost": 99, "detail": "(text)"}], "homebrew_packages": [{"label": "name", "info": "Package name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Package install path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Current 'linked' version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "prefix", "info": "Homebrew install prefix", "section": "Column", "boost": 99, "detail": "(text)"}], "hvci_status": [{"label": "version", "info": "The version number of the Device Guard build.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "instance_identifier", "info": "The instance ID of Device Guard.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vbs_status", "info": "The status of the virtualization based security settings. Returns UNKNOWN if an error is encountered.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "code_integrity_policy_enforcement_status", "info": "The status of the code integrity policy enforcement settings. Returns UNKNOWN if an error is encountered.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "umci_policy_status", "info": "The status of the User Mode Code Integrity security settings. Returns UNKNOWN if an error is encountered.", "section": "Column", "boost": 99, "detail": "(text)"}], "ibridge_info": [{"label": "boot_uuid", "info": "Boot UUID of the iBridge controller", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "coprocessor_version", "info": "The manufacturer and chip version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "firmware_version", "info": "The build version of the firmware", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "unique_chip_id", "info": "Unique id of the iBridge controller", "section": "Column", "boost": 99, "detail": "(text)"}], "ie_extensions": [{"label": "name", "info": "Extension display name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "registry_path", "info": "Extension identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Version of the executable", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path to executable", "section": "Column", "boost": 99, "detail": "(text)"}], "intel_me_info": [{"label": "version", "info": "Intel ME version", "section": "Column", "boost": 99, "detail": "(text)"}], "interface_addresses": [{"label": "interface", "info": "Interface name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "address", "info": "Specific address for interface", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mask", "info": "Interface netmask", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "broadcast", "info": "Broadcast address for the interface", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "point_to_point", "info": "PtP address for the interface", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Type of address. One of dhcp, manual, auto, other, unknown", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "friendly_name", "info": "The friendly display name of the interface.", "section": "Column", "boost": 99, "detail": "(text)"}], "interface_details": [{"label": "interface", "info": "Interface name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mac", "info": "MAC of interface (optional)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Interface type (includes virtual)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mtu", "info": "Network MTU", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "metric", "info": "Metric based on the speed of the interface", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "flags", "info": "Flags (netdevice) for the device", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipackets", "info": "Input packets", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "opackets", "info": "Output packets", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ibytes", "info": "Input bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "obytes", "info": "Output bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ierrors", "info": "Input errors", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "oerrors", "info": "Output errors", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "idrops", "info": "Input drops", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "odrops", "info": "Output drops", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "collisions", "info": "Packet Collisions detected", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "last_change", "info": "Time of last device modification (optional)", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "link_speed", "info": "Interface speed in Mb/s", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pci_slot", "info": "PCI slot number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "friendly_name", "info": "The friendly display name of the interface.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Short description of the object a one-line string.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "manufacturer", "info": "Name of the network adapter's manufacturer.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "connection_id", "info": "Name of the network connection as it appears in the Network Connections Control Panel program.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "connection_status", "info": "State of the network adapter connection to the network.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "enabled", "info": "Indicates whether the adapter is enabled or not.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "physical_adapter", "info": "Indicates whether the adapter is a physical or a logical adapter.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "speed", "info": "Estimate of the current bandwidth in bits per second.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "service", "info": "The name of the service the network adapter uses.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dhcp_enabled", "info": "If TRUE, the dynamic host configuration protocol (DHCP) server automatically assigns an IP address to the computer system when establishing a network connection.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "dhcp_lease_expires", "info": "Expiration date and time for a leased IP address that was assigned to the computer by the dynamic host configuration protocol (DHCP) server.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dhcp_lease_obtained", "info": "Date and time the lease was obtained for the IP address assigned to the computer by the dynamic host configuration protocol (DHCP) server.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dhcp_server", "info": "IP address of the dynamic host configuration protocol (DHCP) server.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dns_domain", "info": "Organization name followed by a period and an extension that indicates the type of organization, such as 'microsoft.com'.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dns_domain_suffix_search_order", "info": "Array of DNS domain suffixes to be appended to the end of host names during name resolution.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dns_host_name", "info": "Host name used to identify the local computer for authentication by some utilities.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dns_server_search_order", "info": "Array of server IP addresses to be used in querying for DNS servers.", "section": "Column", "boost": 99, "detail": "(text)"}], "interface_ipv6": [{"label": "interface", "info": "Interface name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hop_limit", "info": "Current Hop Limit", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "forwarding_enabled", "info": "Enable IP forwarding", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "redirect_accept", "info": "Accept ICMP redirect messages", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "rtadv_accept", "info": "Accept ICMP Router Advertisement", "section": "Column", "boost": 99, "detail": "(integer)"}], "iokit_devicetree": [{"label": "name", "info": "Device node name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "class", "info": "Best matching device class (most-specific category)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "id", "info": "IOKit internal registry ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "parent", "info": "Parent device registry ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "device_path", "info": "Device tree path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "service", "info": "1 if the device conforms to IOService else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "busy_state", "info": "1 if the device is in a busy state else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "retain_count", "info": "The device reference count", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "depth", "info": "Device nested depth", "section": "Column", "boost": 99, "detail": "(integer)"}], "iokit_registry": [{"label": "name", "info": "Default name of the node", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "class", "info": "Best matching device class (most-specific category)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "id", "info": "IOKit internal registry ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "parent", "info": "Parent registry ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "busy_state", "info": "1 if the node is in a busy state else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "retain_count", "info": "The node reference count", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "depth", "info": "Node nested depth", "section": "Column", "boost": 99, "detail": "(integer)"}], "iptables": [{"label": "filter_name", "info": "Packet matching filter table name.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "chain", "info": "Size of module content.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "policy", "info": "Policy that applies for this rule.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "target", "info": "Target that applies for this rule.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "protocol", "info": "Protocol number identification.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "src_port", "info": "Protocol source port(s).", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dst_port", "info": "Protocol destination port(s).", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "src_ip", "info": "Source IP address.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "src_mask", "info": "Source IP address mask.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "iniface", "info": "Input interface for the rule.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "iniface_mask", "info": "Input interface mask for the rule.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dst_ip", "info": "Destination IP address.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dst_mask", "info": "Destination IP address mask.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "outiface", "info": "Output interface for the rule.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "outiface_mask", "info": "Output interface mask for the rule.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "match", "info": "Matching rule that applies.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "packets", "info": "Number of matching packets for this rule.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "bytes", "info": "Number of matching bytes for this rule.", "section": "Column", "boost": 99, "detail": "(integer)"}], "kernel_extensions": [{"label": "idx", "info": "Extension load tag or index", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "refs", "info": "Reference count", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "size", "info": "Bytes of wired memory used by extension", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "name", "info": "Extension label", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Extension version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "linked_against", "info": "Indexes of extensions this extension is linked against", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Optional path to extension bundle", "section": "Column", "boost": 99, "detail": "(text)"}], "kernel_info": [{"label": "version", "info": "Kernel version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "arguments", "info": "Kernel arguments", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Kernel path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "device", "info": "Kernel device identifier", "section": "Column", "boost": 99, "detail": "(text)"}], "kernel_keys": [{"label": "serial_number", "info": "The serial key of the key.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "flags", "info": "A set of flags describing the state of the key.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "usage", "info": "the number of threads and open file references thatrefer to this key.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "timeout", "info": "The amount of time until the key will expire,expressed in human-readable form. The string perm heremeans that the key is permanent (no timeout).  Thestring expd means that the key has already expired.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "permissions", "info": "The key permissions, expressed as four hexadecimalbytes containing, from left to right, thepossessor, user, group, and other permissions.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "The user ID of the key owner.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "The group ID of the key.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "type", "info": "The key type.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "The key description.", "section": "Column", "boost": 99, "detail": "(text)"}], "kernel_modules": [{"label": "name", "info": "Module name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Size of module content", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "used_by", "info": "Module reverse dependencies", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "Kernel module status", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "address", "info": "Kernel module address", "section": "Column", "boost": 99, "detail": "(text)"}], "kernel_panics": [{"label": "path", "info": "Location of log file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Formatted time of the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "registers", "info": "A space delimited line of register:value pairs", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "frame_backtrace", "info": "Backtrace of the crashed module", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "module_backtrace", "info": "<PERSON><PERSON><PERSON> appearing in the crashed module's backtrace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dependencies", "info": "Module dependencies existing in crashed module's backtrace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Process name corresponding to crashed thread", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "os_version", "info": "Version of the operating system", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "kernel_version", "info": "Version of the system kernel", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "system_model", "info": "Physical system model, for example 'MacBookPro12,1 (Mac-E43C1C25D4880AD6)'", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uptime", "info": "System uptime at kernel panic in nanoseconds", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "last_loaded", "info": "Last loaded module before panic", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_unloaded", "info": "Last unloaded module before panic", "section": "Column", "boost": 99, "detail": "(text)"}], "keychain_acls": [{"label": "keychain_path", "info": "The path of the keychain", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "authorizations", "info": "A space delimited set of authorization attributes", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "The path of the authorized application", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "The description included with the ACL entry", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "label", "info": "An optional label tag that may be included with the keychain entry", "section": "Column", "boost": 99, "detail": "(text)"}], "keychain_items": [{"label": "label", "info": "Generic item name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Optional item description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "comment", "info": "Optional keychain comment", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "account", "info": "Optional item account", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "created", "info": "Date item was created", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "modified", "info": "Date of last modification", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Keychain item type (class)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pk_hash", "info": "Hash of associated public key (SHA1 of subjectPublicKey, see RFC 8520 *******)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path to keychain containing item", "section": "Column", "boost": 99, "detail": "(text)"}], "known_hosts": [{"label": "uid", "info": "The local user that owns the known_hosts file", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "key", "info": "parsed authorized keys line", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key_file", "info": "Path to known_hosts file", "section": "Column", "boost": 99, "detail": "(text)"}], "kva_speculative_info": [{"label": "kva_shadow_enabled", "info": "Kernel Virtual Address shadowing is enabled.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "kva_shadow_user_global", "info": "User pages are marked as global.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "kva_shadow_pcid", "info": "Kernel VA PCID flushing optimization is enabled.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "kva_shadow_inv_pcid", "info": "Kernel VA INVPCID is enabled.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "bp_mitigations", "info": "Branch Prediction mitigations are enabled.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "bp_system_pol_disabled", "info": "Branch Predictions are disabled via system policy.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "bp_microcode_disabled", "info": "Branch Predictions are disabled due to lack of microcode update.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_spec_ctrl_supported", "info": "SPEC_CTRL MSR supported by CPU Microcode.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ibrs_support_enabled", "info": "Windows uses IBRS.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "stibp_support_enabled", "info": "Windows uses STIBP.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_pred_cmd_supported", "info": "PRED_CMD MSR supported by CPU Microcode.", "section": "Column", "boost": 99, "detail": "(integer)"}], "last": [{"label": "username", "info": "Entry username", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "tty", "info": "Entry terminal", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "Entry type, according to ut_type types (utmp.h)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type_name", "info": "Entry type name, according to ut_type types (utmp.h)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Entry timestamp", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "host", "info": "Entry hostname", "section": "Column", "boost": 99, "detail": "(text)"}], "launchd": [{"label": "path", "info": "Path to daemon or agent plist", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "File name of plist (used by launchd)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "label", "info": "Daemon or agent service name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "program", "info": "Path to target program", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "run_at_load", "info": "Should the program run on launch load", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "keep_alive", "info": "Should the process be restarted if killed", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "on_demand", "info": "Deprecated key, replaced by keep_alive", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "disabled", "info": "Skip loading this daemon or agent on boot", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "username", "info": "Run this daemon or agent as this username", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "groupname", "info": "Run this daemon or agent as this group", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "stdout_path", "info": "Pipe stdout to a target path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "stderr_path", "info": "Pipe stderr to a target path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "start_interval", "info": "Frequency to run in seconds", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "program_arguments", "info": "Command line arguments passed to program", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "watch_paths", "info": "Key that launches daemon or agent if path is modified", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "queue_directories", "info": "Similar to watch_paths but only with non-empty directories", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "inetd_compatibility", "info": "Run this daemon or agent as it was launched from inetd", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "start_on_mount", "info": "Run daemon or agent every time a filesystem is mounted", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "root_directory", "info": "Key used to specify a directory to chroot to before launch", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "working_directory", "info": "Key used to specify a directory to chdir to before launch", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "process_type", "info": "Key describes the intended purpose of the job", "section": "Column", "boost": 99, "detail": "(text)"}], "launchd_overrides": [{"label": "label", "info": "Daemon or agent service name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Name of the override key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Overridden value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "User ID applied to the override, 0 applies to all", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "path", "info": "Path to daemon or agent plist", "section": "Column", "boost": 99, "detail": "(text)"}], "listening_ports": [{"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "port", "info": "Transport layer port", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "protocol", "info": "Transport protocol (TCP/UDP)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "family", "info": "Network protocol (IPv4, IPv6)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "address", "info": "Specific address for bind", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "fd", "info": "Socket file descriptor number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "socket", "info": "Socket handle or inode number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "path", "info": "Path for UNIX domain sockets", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "net_namespace", "info": "The inode number of the network namespace", "section": "Column", "boost": 99, "detail": "(text)"}], "load_average": [{"label": "period", "info": "Period over which the average is calculated.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "average", "info": "Load average over the specified period.", "section": "Column", "boost": 99, "detail": "(text)"}], "location_services": [{"label": "enabled", "info": "1 if Location Services are enabled, else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "logged_in_users": [{"label": "type", "info": "Login type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user", "info": "User login name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "tty", "info": "Device name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "host", "info": "Remote hostname", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time entry was made", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "sid", "info": "The user's unique security identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "registry_hive", "info": "HKEY_USERS registry hive", "section": "Column", "boost": 99, "detail": "(text)"}], "logical_drives": [{"label": "device_id", "info": "The drive id, usually the drive name, e.g., 'C:'.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Deprecated (always 'Unknown').", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "The canonical description of the drive, e.g. 'Logical Fixed Disk', 'CD-ROM Disk'.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "free_space", "info": "The amount of free space, in bytes, of the drive (-1 on failure).", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "size", "info": "The total amount of space, in bytes, of the drive (-1 on failure).", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "file_system", "info": "The file system of the drive.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "boot_partition", "info": "True if Windows booted from this drive.", "section": "Column", "boost": 99, "detail": "(integer)"}], "logon_sessions": [{"label": "logon_id", "info": "A locally unique identifier (LUID) that identifies a logon session.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "user", "info": "The account name of the security principal that owns the logon session.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "logon_domain", "info": "The name of the domain used to authenticate the owner of the logon session.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "authentication_package", "info": "The authentication package used to authenticate the owner of the logon session.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "logon_type", "info": "The logon method.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "session_id", "info": "The Terminal Services session identifier.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "logon_sid", "info": "The user's security identifier (SID).", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "logon_time", "info": "The time the session owner logged on.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "logon_server", "info": "The name of the server used to authenticate the owner of the logon session.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dns_domain_name", "info": "The DNS name for the owner of the logon session.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "upn", "info": "The user principal name (UPN) for the owner of the logon session.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "logon_script", "info": "The script used for logging on.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "profile_path", "info": "The home directory for the logon session.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "home_directory", "info": "The home directory for the logon session.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "home_directory_drive", "info": "The drive location of the home directory of the logon session.", "section": "Column", "boost": 99, "detail": "(text)"}], "lxd_certificates": [{"label": "name", "info": "Name of the certificate", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Type of the certificate", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "fingerprint", "info": "SHA256 hash of the certificate", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "certificate", "info": "Certificate content", "section": "Column", "boost": 99, "detail": "(text)"}], "lxd_cluster": [{"label": "server_name", "info": "Name of the LXD server node", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "enabled", "info": "Whether clustering enabled (1) or not (0) on this node", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "member_config_entity", "info": "Type of configuration parameter for this node", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "member_config_name", "info": "Name of configuration parameter", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "member_config_key", "info": "Config key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "member_config_value", "info": "Config value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "member_config_description", "info": "Config description", "section": "Column", "boost": 99, "detail": "(text)"}], "lxd_cluster_members": [{"label": "server_name", "info": "Name of the LXD server node", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "url", "info": "URL of the node", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "database", "info": "Whether the server is a database node (1) or not (0)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "status", "info": "Status of the node (Online/Offline)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "message", "info": "Message from the node (Online/Offline)", "section": "Column", "boost": 99, "detail": "(text)"}], "lxd_images": [{"label": "id", "info": "Image ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "architecture", "info": "Target architecture for the image", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "os", "info": "OS on which image is based", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "release", "info": "OS release version on which the image is based", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Image description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "aliases", "info": "Comma-separated list of image aliases", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "filename", "info": "Filename of the image file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Size of image in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "auto_update", "info": "Whether the image auto-updates (1) or not (0)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cached", "info": "Whether image is cached (1) or not (0)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "public", "info": "Whether image is public (1) or not (0)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "created_at", "info": "ISO time of image creation", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "expires_at", "info": "ISO time of image expiration", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uploaded_at", "info": "ISO time of image upload", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_used_at", "info": "ISO time for the most recent use of this image in terms of container spawn", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "update_source_server", "info": "Server for image update", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "update_source_protocol", "info": "Protocol used for image information update and image import from source server", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "update_source_certificate", "info": "Certificate for update source server", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "update_source_alias", "info": "Alias of image at update source server", "section": "Column", "boost": 99, "detail": "(text)"}], "lxd_instance_config": [{"label": "name", "info": "Instance nameRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Configuration parameter name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Configuration parameter value", "section": "Column", "boost": 99, "detail": "(text)"}], "lxd_instance_devices": [{"label": "name", "info": "Instance nameRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "device", "info": "Name of the device", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "device_type", "info": "Device type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Device info param name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Device info param value", "section": "Column", "boost": 99, "detail": "(text)"}], "lxd_instances": [{"label": "name", "info": "Instance name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "Instance state (running, stopped, etc.)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "stateful", "info": "Whether the instance is stateful(1) or not(0)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ephemeral", "info": "Whether the instance is ephemeral(1) or not(0)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "created_at", "info": "ISO time of creation", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "base_image", "info": "ID of image used to launch this instance", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "architecture", "info": "Instance architecture", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "os", "info": "The OS of this instance", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Instance description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Instance's process ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "processes", "info": "Number of processes running inside this instance", "section": "Column", "boost": 99, "detail": "(integer)"}], "lxd_networks": [{"label": "name", "info": "Name of the network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Type of network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "managed", "info": "1 if network created by LXD, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "ipv4_address", "info": "IPv4 address", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ipv6_address", "info": "IPv6 address", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "used_by", "info": "URLs for containers using this network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bytes_received", "info": "Number of bytes received on this network", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "bytes_sent", "info": "Number of bytes sent on this network", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "packets_received", "info": "Number of packets received on this network", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "packets_sent", "info": "Number of packets sent on this network", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "hwaddr", "info": "Hardware address for this network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state", "info": "Network status", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mtu", "info": "MTU size", "section": "Column", "boost": 99, "detail": "(integer)"}], "lxd_storage_pools": [{"label": "name", "info": "Name of the storage pool", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "driver", "info": "Storage driver", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Storage pool source", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Size of the storage pool", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "space_used", "info": "Storage space used in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "space_total", "info": "Total available storage space in bytes for this storage pool", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "inodes_used", "info": "Number of inodes used", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "inodes_total", "info": "Total number of inodes available in this storage pool", "section": "Column", "boost": 99, "detail": "(bigint)"}], "magic": [{"label": "path", "info": "Absolute path to target fileRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "magic_db_files", "info": "Colon(:) separated list of files where the magic db file can be found. By default one of the following is used: /usr/share/file/magic/magic, /usr/share/misc/magic or /usr/share/misc/magic.mgc", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "data", "info": "Magic number data from libmagic", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mime_type", "info": "MIME type data from libmagic", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mime_encoding", "info": "MIME encoding data from libmagic", "section": "Column", "boost": 99, "detail": "(text)"}], "managed_policies": [{"label": "domain", "info": "System or manager-chosen domain key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uuid", "info": "Optional UUID assigned to policy set", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Policy key name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Policy value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "username", "info": "Policy applies only this user", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "manual", "info": "1 if policy was loaded manually, otherwise 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "md_devices": [{"label": "device_name", "info": "md device name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "Current state of the array", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "raid_level", "info": "Current raid level of the array", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "size", "info": "size of the array in blocks", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "chunk_size", "info": "chunk size in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "raid_disks", "info": "Number of configured RAID disks in array", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "nr_raid_disks", "info": "Number of partitions or disk devices to comprise the array", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "working_disks", "info": "Number of working disks in array", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "active_disks", "info": "Number of active disks in array", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "failed_disks", "info": "Number of failed disks in array", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "spare_disks", "info": "Number of idle disks in array", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "superblock_state", "info": "State of the superblock", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "superblock_version", "info": "Version of the superblock", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "superblock_update_time", "info": "Unix timestamp of last update", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "bitmap_on_mem", "info": "Pages allocated in in-memory bitmap, if enabled", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bitmap_chunk_size", "info": "Bitmap chunk size", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bitmap_external_file", "info": "External referenced bitmap file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "recovery_progress", "info": "Progress of the recovery activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "recovery_finish", "info": "Estimated duration of recovery activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "recovery_speed", "info": "Speed of recovery activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "resync_progress", "info": "Progress of the resync activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "resync_finish", "info": "Estimated duration of resync activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "resync_speed", "info": "Speed of resync activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "reshape_progress", "info": "Progress of the reshape activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "reshape_finish", "info": "Estimated duration of reshape activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "reshape_speed", "info": "Speed of reshape activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "check_array_progress", "info": "Progress of the check array activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "check_array_finish", "info": "Estimated duration of the check array activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "check_array_speed", "info": "Speed of the check array activity", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "unused_devices", "info": "Unused devices", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "other", "info": "Other information associated with array from /proc/mdstat", "section": "Column", "boost": 99, "detail": "(text)"}], "md_drives": [{"label": "md_device_name", "info": "md device name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "drive_name", "info": "Drive device name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "slot", "info": "Slot position of disk", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "state", "info": "State of the drive", "section": "Column", "boost": 99, "detail": "(text)"}], "md_personalities": [{"label": "name", "info": "Name of personality supported by kernel", "section": "Column", "boost": 99, "detail": "(text)"}], "mdfind": [{"label": "path", "info": "Path of the file returned from spotlight", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "query", "info": "The query that was run to find the fileRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}], "mdls": [{"label": "path", "info": "Path of the fileRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Name of the metadata key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Value stored in the metadata key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "valuetype", "info": "CoreFoundation type of data stored in value", "section": "Column", "boost": 99, "detail": "(text)"}], "memory_array_mapped_addresses": [{"label": "handle", "info": "Handle, or instance number, associated with the structure", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "memory_array_handle", "info": "Handle of the memory array associated with this structure", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "starting_address", "info": "Physical stating address, in kilobytes, of a range of memory mapped to physical memory array", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ending_address", "info": "Physical ending address of last kilobyte of a range of memory mapped to physical memory array", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "partition_width", "info": "Number of memory devices that form a single row of memory for the address partition of this structure", "section": "Column", "boost": 99, "detail": "(integer)"}], "memory_arrays": [{"label": "handle", "info": "Handle, or instance number, associated with the array", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "location", "info": "Physical location of the memory array", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "use", "info": "Function for which the array is used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "memory_error_correction", "info": "Primary hardware error correction or detection method supported", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "max_capacity", "info": "Maximum capacity of array in gigabytes", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "memory_error_info_handle", "info": "Handle, or instance number, associated with any error that was detected for the array", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "number_memory_devices", "info": "Number of memory devices on array", "section": "Column", "boost": 99, "detail": "(integer)"}], "memory_device_mapped_addresses": [{"label": "handle", "info": "Handle, or instance number, associated with the structure", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "memory_device_handle", "info": "Handle of the memory device structure associated with this structure", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "memory_array_mapped_address_handle", "info": "Handle of the memory array mapped address to which this device range is mapped to", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "starting_address", "info": "Physical stating address, in kilobytes, of a range of memory mapped to physical memory array", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ending_address", "info": "Physical ending address of last kilobyte of a range of memory mapped to physical memory array", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "partition_row_position", "info": "Identifies the position of the referenced memory device in a row of the address partition", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "interleave_position", "info": "The position of the device in a interleave, i.e. 0 indicates non-interleave, 1 indicates 1st interleave, 2 indicates 2nd interleave, etc.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "interleave_data_depth", "info": "The max number of consecutive rows from memory device that are accessed in a single interleave transfer; 0 indicates device is non-interleave", "section": "Column", "boost": 99, "detail": "(integer)"}], "memory_devices": [{"label": "handle", "info": "Handle, or instance number, associated with the structure in SMBIOS", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "array_handle", "info": "The memory array that the device is attached to", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "form_factor", "info": "Implementation form factor for this memory device", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "total_width", "info": "Total width, in bits, of this memory device, including any check or error-correction bits", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "data_width", "info": "Data width, in bits, of this memory device", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "size", "info": "Size of memory device in Megabyte", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "set", "info": "Identifies if memory device is one of a set of devices.  A value of 0 indicates no set affiliation.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "device_locator", "info": "String number of the string that identifies the physically-labeled socket or board position where the memory device is located", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bank_locator", "info": "String number of the string that identifies the physically-labeled bank where the memory device is located", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "memory_type", "info": "Type of memory used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "memory_type_details", "info": "Additional details for memory device", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "max_speed", "info": "Max speed of memory device in megatransfers per second (MT/s)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "configured_clock_speed", "info": "Configured speed of memory device in megatransfers per second (MT/s)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "manufacturer", "info": "Manufacturer ID string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial_number", "info": "Serial number of memory device", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "asset_tag", "info": "Manufacturer specific asset tag of memory device", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "part_number", "info": "Manufacturer specific serial number of memory device", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "min_voltage", "info": "Minimum operating voltage of device in millivolts", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "max_voltage", "info": "Maximum operating voltage of device in millivolts", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "configured_voltage", "info": "Configured operating voltage of device in millivolts", "section": "Column", "boost": 99, "detail": "(integer)"}], "memory_error_info": [{"label": "handle", "info": "Handle, or instance number, associated with the structure", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "error_type", "info": "type of error associated with current error status for array or device", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "error_granularity", "info": "Granularity to which the error can be resolved", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "error_operation", "info": "Memory access operation that caused the error", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vendor_syndrome", "info": "Vendor specific ECC syndrome or CRC data associated with the erroneous access", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "memory_array_error_address", "info": "32 bit physical address of the error based on the addressing of the bus to which the memory array is connected", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "device_error_address", "info": "32 bit physical address of the error relative to the start of the failing memory address, in bytes", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "error_resolution", "info": "Range, in bytes, within which this error can be determined, when an error address is given", "section": "Column", "boost": 99, "detail": "(text)"}], "memory_info": [{"label": "memory_total", "info": "Total amount of physical RAM, in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "memory_free", "info": "The amount of physical RAM, in bytes, left unused by the system", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "memory_available", "info": "The amount of physical RAM, in bytes, available for starting new applications, without swapping", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "buffers", "info": "The amount of physical RAM, in bytes, used for file buffers", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "cached", "info": "The amount of physical RAM, in bytes, used as cache memory", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "swap_cached", "info": "The amount of swap, in bytes, used as cache memory", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "active", "info": "The total amount of buffer or page cache memory, in bytes, that is in active use", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "inactive", "info": "The total amount of buffer or page cache memory, in bytes, that are free and available", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "swap_total", "info": "The total amount of swap available, in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "swap_free", "info": "The total amount of swap free, in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}], "memory_map": [{"label": "name", "info": "Region name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "start", "info": "Start address of memory region", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "end", "info": "End address of memory region", "section": "Column", "boost": 99, "detail": "(text)"}], "mounts": [{"label": "device", "info": "Mounted device", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "device_alias", "info": "Mounted device alias", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Mounted device path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Mounted device type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "blocks_size", "info": "Block size in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "blocks", "info": "Mounted device used blocks", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "blocks_free", "info": "Mounted device free blocks", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "blocks_available", "info": "Mounted device available blocks", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "inodes", "info": "Mounted device used inodes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "inodes_free", "info": "Mounted device free inodes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "flags", "info": "Mounted device flags", "section": "Column", "boost": 99, "detail": "(text)"}], "msr": [{"label": "processor_number", "info": "The processor number as reported in /proc/cpuinfo", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "turbo_disabled", "info": "Whether the turbo feature is disabled.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "turbo_ratio_limit", "info": "The turbo feature ratio limit.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "platform_info", "info": "Platform information.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "perf_ctl", "info": "Performance setting for the processor.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "perf_status", "info": "Performance status for the processor.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "feature_control", "info": "Bitfield controlling enabled features.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "rapl_power_limit", "info": "Run Time Average Power Limiting power limit.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "rapl_energy_status", "info": "Run Time Average Power Limiting energy status.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "rapl_power_units", "info": "Run Time Average Power Limiting power units.", "section": "Column", "boost": 99, "detail": "(bigint)"}], "nfs_shares": [{"label": "share", "info": "Filesystem path to the share", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "options", "info": "Options string set on the export share", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "readonly", "info": "1 if the share is exported readonly else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "npm_packages": [{"label": "name", "info": "Package display name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Package-supplied version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Package-supplied description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "author", "info": "Package-supplied author", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "license", "info": "License under which package is launched", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "homepage", "info": "Package supplied homepage", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path at which this module resides", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "directory", "info": "Directory where node_modules are located", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mount_namespace_id", "info": "Mount namespace id", "section": "Column", "boost": 99, "detail": "(text)"}], "ntdomains": [{"label": "name", "info": "The label by which the object is known.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "client_site_name", "info": "The name of the site where the domain controller is configured.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dc_site_name", "info": "The name of the site where the domain controller is located.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dns_forest_name", "info": "The name of the root of the DNS tree.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "domain_controller_address", "info": "The IP Address of the discovered domain controller..", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "domain_controller_name", "info": "The name of the discovered domain controller.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "domain_name", "info": "The name of the domain.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "The current status of the domain object.", "section": "Column", "boost": 99, "detail": "(text)"}], "ntfs_acl_permissions": [{"label": "path", "info": "Path to the file or directory.Required in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Type of access mode for the access control entry.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "principal", "info": "User or group to which the ACE applies.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "access", "info": "Specific permissions that indicate the rights described by the ACE.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "inherited_from", "info": "The inheritance policy of the ACE.", "section": "Column", "boost": 99, "detail": "(text)"}], "ntfs_journal_events": [{"label": "action", "info": "Change action (Write, Delete, etc)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "category", "info": "The category that the event originated from", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "old_path", "info": "Old path (renames only)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "record_timestamp", "info": "Journal record timestamp", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "record_usn", "info": "The update sequence number that identifies the journal record", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "node_ref_number", "info": "The ordinal that associates a journal record with a filename", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "parent_ref_number", "info": "The ordinal that associates a journal record with a filename's parent directory", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "drive_letter", "info": "The drive letter identifying the source journal", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "file_attributes", "info": "File attributes", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "partial", "info": "Set to 1 if either path or old_path only contains the file or folder name", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "time", "info": "Time of file event", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "nvram": [{"label": "name", "info": "Variable name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Data type (CFData, CFString, etc)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Raw variable data", "section": "Column", "boost": 99, "detail": "(text)"}], "oem_strings": [{"label": "handle", "info": "Handle, or instance number, associated with the Type 11 structure", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "number", "info": "The string index of the structure", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "value", "info": "The value of the OEM string", "section": "Column", "boost": 99, "detail": "(text)"}], "office_mru": [{"label": "application", "info": "Associated Office application", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Office application version number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "File path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_opened_time", "info": "Most recent opened time file was opened", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "sid", "info": "User SID", "section": "Column", "boost": 99, "detail": "(text)"}], "os_version": [{"label": "name", "info": "Distribution or product name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Pretty, suitable for presentation, OS version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "major", "info": "Major release version", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "minor", "info": "Minor release version", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "patch", "info": "Optional patch release", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "build", "info": "Optional build-specific or variant string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "platform", "info": "OS Platform or ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "platform_like", "info": "Closely related platforms", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "codename", "info": "OS version codename", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "arch", "info": "OS Architecture", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "extra", "info": "Optional extra release specification", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "install_date", "info": "The install date of the OS.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mount_namespace_id", "info": "Mount namespace id", "section": "Column", "boost": 99, "detail": "(text)"}], "osquery_events": [{"label": "name", "info": "Event publisher or subscriber name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "publisher", "info": "Name of the associated publisher", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Either publisher or subscriber", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subscriptions", "info": "Number of subscriptions the publisher received or subscriber used", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "events", "info": "Number of events emitted or received since osquery started", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "refreshes", "info": "Publisher only: number of runloop restarts", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "active", "info": "1 if the publisher or subscriber is active else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "osquery_extensions": [{"label": "uuid", "info": "The transient ID assigned for communication", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "name", "info": "<PERSON>'s name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Extension's version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sdk_version", "info": "osquery SDK version used to build the extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path of the extension's Thrift connection or library path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "SDK extension type: core, extension, or module", "section": "Column", "boost": 99, "detail": "(text)"}], "osquery_flags": [{"label": "name", "info": "Flag name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Flag type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Flag description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "default_value", "info": "Flag default value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Flag value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "shell_only", "info": "Is the flag shell only?", "section": "Column", "boost": 99, "detail": "(integer)"}], "osquery_info": [{"label": "pid", "info": "Process (or thread/handle) ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "uuid", "info": "Unique ID provided by the system", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "instance_id", "info": "Unique, long-lived ID per instance of osquery", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "osquery toolkit version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "config_hash", "info": "Hash of the working configuration state", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "config_valid", "info": "1 if the config was loaded and considered valid, else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "extensions", "info": "osquery extensions status", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "build_platform", "info": "osquery toolkit build platform", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "build_distro", "info": "osquery toolkit platform distribution name (os version)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "start_time", "info": "UNIX time in seconds when the process started", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "watcher", "info": "Process (or thread/handle) ID of optional watcher process", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "platform_mask", "info": "The osquery platform bitmask", "section": "Column", "boost": 99, "detail": "(integer)"}], "osquery_packs": [{"label": "name", "info": "The given name for this query pack", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "platform", "info": "Platforms this query is supported on", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Minimum osquery version that this query will run on", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "shard", "info": "Shard restriction limit, 1-100, 0 meaning no restriction", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "discovery_cache_hits", "info": "The number of times that the discovery query used cached values since the last time the config was reloaded", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "discovery_executions", "info": "The number of times that the discovery queries have been executed since the last time the config was reloaded", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "active", "info": "Whether this pack is active (the version, platform and discovery queries match) yes=1, no=0.", "section": "Column", "boost": 99, "detail": "(integer)"}], "osquery_registry": [{"label": "registry", "info": "Name of the osquery registry", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Name of the plugin item", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "owner_uuid", "info": "Extension route UUID (0 for core)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "internal", "info": "1 If the plugin is internal else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "active", "info": "1 If this plugin is active else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "osquery_schedule": [{"label": "name", "info": "The given name for this query", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "query", "info": "The exact query to run", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "interval", "info": "The interval in seconds to run this query, not an exact interval", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "executions", "info": "Number of times the query was executed", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "last_executed", "info": "UNIX time stamp in seconds of the last completed execution", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "denylisted", "info": "1 if the query is denylisted else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "output_size", "info": "Cumulative total number of bytes generated by the resultant rows of the query", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "wall_time", "info": "Total wall time in seconds spent executing (deprecated), hidden=True", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "wall_time_ms", "info": "Total wall time in milliseconds spent executing", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "last_wall_time_ms", "info": "Wall time in milliseconds of the latest execution", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "user_time", "info": "Total user time in milliseconds spent executing", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "last_user_time", "info": "User time in milliseconds of the latest execution", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "system_time", "info": "Total system time in milliseconds spent executing", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "last_system_time", "info": "System time in milliseconds of the latest execution", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "average_memory", "info": "Average of the bytes of resident memory left allocated after collecting results", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "last_memory", "info": "Resident memory in bytes left allocated after collecting results of the latest execution", "section": "Column", "boost": 99, "detail": "(bigint)"}], "package_bom": [{"label": "filepath", "info": "Package file or directory", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "Expected user of file or directory", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "gid", "info": "Expected group of file or directory", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mode", "info": "Expected permissions", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "size", "info": "Expected file size", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "modified_time", "info": "Timestamp the file was installed", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "path", "info": "Path of package bomRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}], "package_install_history": [{"label": "package_id", "info": "Label packageIdentifiers", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Label date as UNIX timestamp", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "name", "info": "Package display name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Package display version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Install source: usually the installer process name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "content_type", "info": "Package content_type (optional)", "section": "Column", "boost": 99, "detail": "(text)"}], "package_receipts": [{"label": "package_id", "info": "Package domain identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "package_filename", "info": "Filename of original .pkg file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Installed package version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "location", "info": "Optional relative install path on volume", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "install_time", "info": "Timestamp of install time", "section": "Column", "boost": 99, "detail": "(double)"}, {"label": "installer_name", "info": "Name of installer process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path of receipt plist", "section": "Column", "boost": 99, "detail": "(text)"}], "password_policy": [{"label": "uid", "info": "User ID for the policy, -1 for policies that are global", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "policy_identifier", "info": "Policy Identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "policy_content", "info": "Policy content", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "policy_description", "info": "Policy description", "section": "Column", "boost": 99, "detail": "(text)"}], "patches": [{"label": "csname", "info": "The name of the host the patch is installed on.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hotfix_id", "info": "The KB ID of the patch.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "caption", "info": "Short description of the patch.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "<PERSON> description of the patch.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "fix_comments", "info": "Additional comments about the patch.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "installed_by", "info": "The system context in which the patch as installed.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "install_date", "info": "Indicates when the patch was installed. Lack of a value does not indicate that the patch was not installed.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "installed_on", "info": "The date when the patch was installed.", "section": "Column", "boost": 99, "detail": "(text)"}], "pci_devices": [{"label": "pci_slot", "info": "PCI Device used slot", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pci_class", "info": "PCI Device class", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "driver", "info": "PCI Device used driver", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vendor", "info": "PCI Device vendor", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vendor_id", "info": "Hex encoded PCI Device vendor identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "model", "info": "PCI Device model", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "model_id", "info": "Hex encoded PCI Device model identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pci_class_id", "info": "PCI Device class ID in hex format", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pci_subclass_id", "info": "PCI Device  subclass in hex format", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pci_subclass", "info": "PCI Device subclass", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subsystem_vendor_id", "info": "Vendor ID of PCI device subsystem", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subsystem_vendor", "info": "Vendor of PCI device subsystem", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subsystem_model_id", "info": "Model ID of PCI device subsystem", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subsystem_model", "info": "Device description of PCI device subsystem", "section": "Column", "boost": 99, "detail": "(text)"}], "physical_disk_performance": [{"label": "name", "info": "Name of the physical disk", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "avg_disk_bytes_per_read", "info": "Average number of bytes transferred from the disk during read operations", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "avg_disk_bytes_per_write", "info": "Average number of bytes transferred to the disk during write operations", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "avg_disk_read_queue_length", "info": "Average number of read requests that were queued for the selected disk during the sample interval", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "avg_disk_write_queue_length", "info": "Average number of write requests that were queued for the selected disk during the sample interval", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "avg_disk_sec_per_read", "info": "Average time, in seconds, of a read operation of data from the disk", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "avg_disk_sec_per_write", "info": "Average time, in seconds, of a write operation of data to the disk", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "current_disk_queue_length", "info": "Number of requests outstanding on the disk at the time the performance data is collected", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "percent_disk_read_time", "info": "Percentage of elapsed time that the selected disk drive is busy servicing read requests", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "percent_disk_write_time", "info": "Percentage of elapsed time that the selected disk drive is busy servicing write requests", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "percent_disk_time", "info": "Percentage of elapsed time that the selected disk drive is busy servicing read or write requests", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "percent_idle_time", "info": "Percentage of time during the sample interval that the disk was idle", "section": "Column", "boost": 99, "detail": "(bigint)"}], "pipes": [{"label": "pid", "info": "Process ID of the process to which the pipe belongs", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "name", "info": "Name of the pipe", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "instances", "info": "Number of instances of the named pipe", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "max_instances", "info": "The maximum number of instances creatable for this pipe", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "flags", "info": "The flags indicating whether this pipe connection is a server or client end, and if the pipe for sending messages or bytes", "section": "Column", "boost": 99, "detail": "(text)"}], "platform_info": [{"label": "vendor", "info": "Platform code vendor", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Platform code version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "date", "info": "Self-reported platform code update date", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "revision", "info": "BIOS major and minor revision", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "extra", "info": "Platform-specific additional information", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "firmware_type", "info": "The type of firmware (uefi, bios, iboot, openfirmware, unknown).", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "address", "info": "Relative address of firmware mapping", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Size in bytes of firmware", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "volume_size", "info": "(Optional) size of firmware volume", "section": "Column", "boost": 99, "detail": "(integer)"}], "plist": [{"label": "key", "info": "Preference top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subkey", "info": "Intermediate key path, includes lists/dicts", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "String value of most CF types", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "(required) read preferences from a plistRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}], "portage_keywords": [{"label": "package", "info": "Package name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "The version which are affected by the use flags, empty means all", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "keyword", "info": "The keyword applied to the package", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mask", "info": "If the package is masked", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "unmask", "info": "If the package is unmasked", "section": "Column", "boost": 99, "detail": "(integer)"}], "portage_packages": [{"label": "package", "info": "Package name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "The version which are affected by the use flags, empty means all", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "slot", "info": "The slot used by package", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "build_time", "info": "Unix time when package was built", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "repository", "info": "From which repository the ebuild was used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "eapi", "info": "The eapi for the ebuild", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "size", "info": "The size of the package", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "world", "info": "If package is in the world file", "section": "Column", "boost": 99, "detail": "(integer)"}], "portage_use": [{"label": "package", "info": "Package name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "The version of the installed package", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "use", "info": "USE flag which has been enabled for package", "section": "Column", "boost": 99, "detail": "(text)"}], "power_sensors": [{"label": "key", "info": "The SMC key on macOS", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "category", "info": "The sensor category: currents, voltage, wattage", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Name of power source", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Power in Watts", "section": "Column", "boost": 99, "detail": "(text)"}], "powershell_events": [{"label": "time", "info": "Timestamp the event was received by the osquery event publisher", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "datetime", "info": "System time at which the Powershell script event occurred", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "script_block_id", "info": "The unique GUID of the powershell script to which this block belongs", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "script_block_count", "info": "The total number of script blocks for this script", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "script_text", "info": "The text content of the Powershell script", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "script_name", "info": "The name of the Powershell script", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "script_path", "info": "The path for the Powershell script", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cosine_similarity", "info": "How similar the Powershell script is to a provided 'normal' character frequency", "section": "Column", "boost": 99, "detail": "(double)"}], "preferences": [{"label": "domain", "info": "Application ID usually in com.name.product format", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Preference top-level key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subkey", "info": "Intemediate key path, includes lists/dicts", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "String value of most CF types", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "forced", "info": "1 if the value is forced/managed, else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "username", "info": "(optional) read preferences for a specific user", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "host", "info": "'current' or 'any' host, where 'current' takes precedence", "section": "Column", "boost": 99, "detail": "(text)"}], "prefetch": [{"label": "path", "info": "Prefetch file path.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "filename", "info": "Executable filename.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hash", "info": "Prefetch CRC hash.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_run_time", "info": "Most recent time application was run.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "other_run_times", "info": "Other execution times in prefetch file.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "run_count", "info": "Number of times the application has been run.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "size", "info": "Application file size.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "volume_serial", "info": "Volume serial number.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "volume_creation", "info": "Volume creation time.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "accessed_files_count", "info": "Number of files accessed.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "accessed_directories_count", "info": "Number of directories accessed.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "accessed_files", "info": "Files accessed by application within ten seconds of launch.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "accessed_directories", "info": "Directories accessed by application within ten seconds of launch.", "section": "Column", "boost": 99, "detail": "(text)"}], "process_envs": [{"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "key", "info": "Environment variable name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Environment variable value", "section": "Column", "boost": 99, "detail": "(text)"}], "process_etw_events": [{"label": "type", "info": "Event Type (ProcessStart, ProcessStop)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ppid", "info": "Parent Process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "session_id", "info": "Session ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "flags", "info": "Process Flags", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "exit_code", "info": "Exit Code - Present only on ProcessStop events", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "path", "info": "Path of executed binary", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cmdline", "info": "Command Line", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "username", "info": "User rights - primary token username", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "token_elevation_type", "info": "Primary token elevation type - Present only on ProcessStart events", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "token_elevation_status", "info": "Primary token elevation status - Present only on ProcessStart events", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mandatory_label", "info": "Primary token mandatory label sid - Present only on ProcessStart events", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "datetime", "info": "Event timestamp in DATETIME format", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time_windows", "info": "Event timestamp in Windows format", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "time", "info": "Event timestamp in Unix format", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "header_pid", "info": "Process ID of the process reporting the event", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "process_sequence_number", "info": "Process Sequence Number - Present only on ProcessStart events", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "parent_process_sequence_number", "info": "Parent Process Sequence Number - Present only on ProcessStart events", "section": "Column", "boost": 99, "detail": "(bigint)"}], "process_events": [{"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "path", "info": "Path of executed file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mode", "info": "File mode permissions", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cmdline", "info": "Command line arguments (argv)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cmdline_size", "info": "Actual size (bytes) of command line arguments", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "env", "info": "Environment variables delimited by spaces", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "env_count", "info": "Number of environment variables", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "env_size", "info": "Actual size (bytes) of environment list", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "cwd", "info": "The process current working directory", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "auid", "info": "Audit User ID at process start", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uid", "info": "User ID at process start", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "euid", "info": "Effective user ID at process start", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Group ID at process start", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "egid", "info": "Effective group ID at process start", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "owner_uid", "info": "File owner user ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "owner_gid", "info": "File owner group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "atime", "info": "File last access in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "mtime", "info": "File modification in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ctime", "info": "File last metadata change in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "btime", "info": "File creation in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "overflows", "info": "List of structures that overflowed", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "parent", "info": "Process parent's PID, or -1 if cannot be determined.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uptime", "info": "Time of execution in system uptime", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "OpenBSM Attribute: Status of the process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "fsuid", "info": "Filesystem user ID at process start", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "suid", "info": "Saved user ID at process start", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "fsgid", "info": "Filesystem group ID at process start", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "sgid", "info": "Saved group ID at process start", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "syscall", "info": "Syscall name: fork, vfork, clone, execve, execveat", "section": "Column", "boost": 99, "detail": "(text)"}], "process_file_events": [{"label": "operation", "info": "Operation type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ppid", "info": "Parent process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "executable", "info": "The executable path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "partial", "info": "True if this is a partial event (i.e.: this process existed before we started osquery)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cwd", "info": "The current working directory of the process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "The path associated with the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "dest_path", "info": "The canonical path associated with the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "The uid of the process performing the action", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "gid", "info": "The gid of the process performing the action", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "auid", "info": "Audit user ID of the process using the file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "euid", "info": "Effective user ID of the process using the file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "egid", "info": "Effective group ID of the process using the file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "fsuid", "info": "Filesystem user ID of the process using the file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "fsgid", "info": "Filesystem group ID of the process using the file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "suid", "info": "Saved user ID of the process using the file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sgid", "info": "Saved group ID of the process using the file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uptime", "info": "Time of execution in system uptime", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "process_memory_map": [{"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "start", "info": "Virtual start address (hex)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "end", "info": "Virtual end address (hex)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "permissions", "info": "r=read, w=write, x=execute, p=private (cow)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "offset", "info": "Offset into mapped path", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "device", "info": "MA:MI Major/minor device ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "inode", "info": "Mapped path inode, 0 means uninitialized (BSS)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "path", "info": "Path to mapped file or mapped type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pseudo", "info": "1 If path is a pseudo path, else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "process_namespaces": [{"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cgroup_namespace", "info": "cgroup namespace inode", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ipc_namespace", "info": "ipc namespace inode", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mnt_namespace", "info": "mnt namespace inode", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "net_namespace", "info": "net namespace inode", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_namespace", "info": "pid namespace inode", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user_namespace", "info": "user namespace inode", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uts_namespace", "info": "uts namespace inode", "section": "Column", "boost": 99, "detail": "(text)"}], "process_open_files": [{"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "fd", "info": "Process-specific file descriptor number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "path", "info": "Filesystem path of descriptor", "section": "Column", "boost": 99, "detail": "(text)"}], "process_open_pipes": [{"label": "pid", "info": "Process ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "fd", "info": "File descriptor", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "mode", "info": "Pipe open mode (r/w)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "inode", "info": "Pipe inode number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "type", "info": "Pipe Type: named vs unnamed/anonymous", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "partner_pid", "info": "Process ID of partner process sharing a particular pipe", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "partner_fd", "info": "File descriptor of shared pipe at partner's end", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "partner_mode", "info": "Mode of shared pipe at partner's end", "section": "Column", "boost": 99, "detail": "(text)"}], "process_open_sockets": [{"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "fd", "info": "Socket file descriptor number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "socket", "info": "Socket handle or inode number", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "family", "info": "Network protocol (IPv4, IPv6)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "protocol", "info": "Transport protocol (TCP/UDP)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "local_address", "info": "Socket local address", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "remote_address", "info": "Socket remote address", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "local_port", "info": "Socket local port", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "remote_port", "info": "Socket remote port", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "path", "info": "For UNIX sockets (family=AF_UNIX), the domain path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state", "info": "TCP socket state", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "net_namespace", "info": "The inode number of the network namespace", "section": "Column", "boost": 99, "detail": "(text)"}], "processes": [{"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "name", "info": "The process path or shorthand argv[0]", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path to executed binary", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cmdline", "info": "Complete argv", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state", "info": "Process state", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cwd", "info": "Process current working directory", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "root", "info": "Process virtual root directory", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uid", "info": "Unsigned user ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Unsigned group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "euid", "info": "Unsigned effective user ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "egid", "info": "Unsigned effective group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "suid", "info": "Unsigned saved user ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "sgid", "info": "Unsigned saved group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "on_disk", "info": "The process path exists yes=1, no=0, unknown=-1", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "wired_size", "info": "Bytes of unpageable memory used by process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "resident_size", "info": "Bytes of private memory used by process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "total_size", "info": "Total virtual memory size", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "user_time", "info": "CPU time in milliseconds spent in user space", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "system_time", "info": "CPU time in milliseconds spent in kernel space", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "disk_bytes_read", "info": "Bytes read from disk", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "disk_bytes_written", "info": "Bytes written to disk", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "start_time", "info": "Process start time in seconds since Epoch, in case of error -1", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "parent", "info": "Process parent's PID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pgroup", "info": "Process group", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "threads", "info": "Number of threads used by process", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "nice", "info": "Process nice level (-20 to 20, default 0)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "elevated_token", "info": "Process uses elevated token yes=1, no=0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "secure_process", "info": "Process is secure (IUM) yes=1, no=0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "protection_type", "info": "The protection type of the process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "virtual_process", "info": "Process is virtual (e.g. System, Registry, vmmem) yes=1, no=0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "elapsed_time", "info": "Elapsed time in seconds this process has been running.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "handle_count", "info": "Total number of handles that the process has open. This number is the sum of the handles currently opened by each thread in the process.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "percent_processor_time", "info": "Returns elapsed time that all of the threads of this process used the processor to execute instructions in 100 nanoseconds ticks.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "upid", "info": "A 64bit pid that is never reused. Returns -1 if we couldn't gather them from the system.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uppid", "info": "The 64bit parent pid that is never reused. Returns -1 if we couldn't gather them from the system.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "cpu_type", "info": "Indicates the specific processor designed for installation.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_subtype", "info": "Indicates the specific processor on which an entry may be used.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "translated", "info": "Indicates whether the process is running under the Rosetta Translation Environment, yes=1, no=0, error=-1.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cgroup_path", "info": "The full hierarchical path of the process's control group", "section": "Column", "boost": 99, "detail": "(text)"}], "programs": [{"label": "name", "info": "Commonly used product name.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Product version information.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "install_location", "info": "The installation location directory of the product.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "install_source", "info": "The installation source of the product.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "language", "info": "The language of the product.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "publisher", "info": "Name of the product supplier.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uninstall_string", "info": "Path and filename of the uninstaller.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "install_date", "info": "Date that this product was installed on the system. ", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "identifying_number", "info": "Product identification such as a serial number on software, or a die number on a hardware chip.", "section": "Column", "boost": 99, "detail": "(text)"}], "prometheus_metrics": [{"label": "target_name", "info": "Address of prometheus target", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "metric_name", "info": "Name of collected Prometheus metric", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "metric_value", "info": "Value of collected Prometheus metric", "section": "Column", "boost": 99, "detail": "(double)"}, {"label": "timestamp_ms", "info": "Unix timestamp of collected data in MS", "section": "Column", "boost": 99, "detail": "(bigint)"}], "python_packages": [{"label": "name", "info": "Package display name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Package-supplied version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "summary", "info": "Package-supplied summary", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "author", "info": "Optional package author", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "license", "info": "License under which package is launched", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path at which this module resides", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "directory", "info": "Directory where Python modules are located", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "quicklook_cache": [{"label": "path", "info": "Path of file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "rowid", "info": "Quicklook file rowid key", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "fs_id", "info": "Quicklook file fs_id key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "volume_id", "info": "Parsed volume ID from fs_id", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "inode", "info": "Parsed file ID (inode) from fs_id", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mtime", "info": "Parsed version date field", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "size", "info": "Parsed version size field", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "label", "info": "Parsed version 'gen' field", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_hit_date", "info": "Apple date format for last thumbnail cache hit", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "hit_count", "info": "Number of cache hits on thumbnail", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "icon_mode", "info": "Thumbnail icon mode", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "cache_path", "info": "Path to cache data", "section": "Column", "boost": 99, "detail": "(text)"}], "registry": [{"label": "key", "info": "Name of the key to search for", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Full path to the value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Name of the registry value entry", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Type of the registry value, or 'subkey' if item is a subkey", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "data", "info": "Data content of registry value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mtime", "info": "timestamp of the most recent registry write", "section": "Column", "boost": 99, "detail": "(bigint)"}], "routes": [{"label": "destination", "info": "Destination IP address", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "netmask", "info": "Netmask length", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "gateway", "info": "Route gateway", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Route source", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "flags", "info": "Flags to describe route", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "interface", "info": "Route local interface", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mtu", "info": "Maximum Transmission Unit for the route", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "metric", "info": "Cost of route. Lowest is preferred", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "Type of route", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hopcount", "info": "Max hops expected", "section": "Column", "boost": 99, "detail": "(integer)"}], "rpm_package_files": [{"label": "package", "info": "RPM package name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "File path within the package", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "username", "info": "File default username from info DB", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "groupname", "info": "File default groupname from info DB", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mode", "info": "File permissions mode from info DB", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Expected file size in bytes from RPM info DB", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "sha256", "info": "SHA256 file digest from RPM info DB", "section": "Column", "boost": 99, "detail": "(text)"}], "rpm_packages": [{"label": "name", "info": "RPM package name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Package version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "release", "info": "Package release", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Source RPM package name (optional)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Package size in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "sha1", "info": "SHA1 hash of the package contents", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "arch", "info": "Architecture(s) supported", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "epoch", "info": "Package epoch value", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "install_time", "info": "When the package was installed", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "vendor", "info": "Package vendor", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "package_group", "info": "Package group", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mount_namespace_id", "info": "Mount namespace id", "section": "Column", "boost": 99, "detail": "(text)"}], "running_apps": [{"label": "pid", "info": "The pid of the application", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "bundle_identifier", "info": "The bundle identifier of the application", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "is_active", "info": "(DEPRECATED)", "section": "Column", "boost": 99, "detail": "(integer)"}], "safari_extensions": [{"label": "uid", "info": "The local user that owns the extension", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "name", "info": "Extension display name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "identifier", "info": "Extension identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Extension long version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sdk", "info": "Bundle SDK used to compile extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "update_url", "info": "Extension-supplied update URI", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "author", "info": "Optional extension author", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "developer_id", "info": "Optional developer identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Optional extension description text", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path to extension XAR bundle", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bundle_version", "info": "The version of the build that identifies an iteration of the bundle", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "copyright", "info": "A human-readable copyright notice for the bundle", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "extension_type", "info": "Extension Type: WebOrAppExtension or LegacyExtension", "section": "Column", "boost": 99, "detail": "(text)"}], "sandboxes": [{"label": "label", "info": "UTI-format bundle or label ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user", "info": "Sandbox owner", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "enabled", "info": "Application sandboxings enabled on container", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "build_id", "info": "Sandbox-specific identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bundle_path", "info": "Application bundle used by the sandbox", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path to sandbox container directory", "section": "Column", "boost": 99, "detail": "(text)"}], "scheduled_tasks": [{"label": "name", "info": "Name of the scheduled task", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "action", "info": "Actions executed by the scheduled task", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path to the executable to be run", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "enabled", "info": "Whether or not the scheduled task is enabled", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "state", "info": "State of the scheduled task", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hidden", "info": "Whether or not the task is visible in the UI", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "last_run_time", "info": "Timestamp the task last ran", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "next_run_time", "info": "Timestamp the task is scheduled to run next", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "last_run_message", "info": "Exit status message of the last task run", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_run_code", "info": "Exit status code of the last task run", "section": "Column", "boost": 99, "detail": "(text)"}], "screenlock": [{"label": "enabled", "info": "1 If a password is required after sleep or the screensaver begins; else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "grace_period", "info": "The amount of time in seconds the screen must be asleep or the screensaver on before a password is required on-wake. 0 = immediately; -1 = no password is required on-wake", "section": "Column", "boost": 99, "detail": "(integer)"}], "seccomp_events": [{"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uptime", "info": "Time of execution in system uptime", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "auid", "info": "Audit user ID (loginuid) of the user who started the analyzed process", "section": "Column", "boost": 99, "detail": "(unsigned_bigint)"}, {"label": "uid", "info": "User ID of the user who started the analyzed process", "section": "Column", "boost": 99, "detail": "(unsigned_bigint)"}, {"label": "gid", "info": "Group ID of the user who started the analyzed process", "section": "Column", "boost": 99, "detail": "(unsigned_bigint)"}, {"label": "ses", "info": "Session ID of the session from which the analyzed process was invoked", "section": "Column", "boost": 99, "detail": "(unsigned_bigint)"}, {"label": "pid", "info": "Process ID", "section": "Column", "boost": 99, "detail": "(unsigned_bigint)"}, {"label": "comm", "info": "Command-line name of the command that was used to invoke the analyzed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "exe", "info": "The path to the executable that was used to invoke the analyzed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sig", "info": "Signal value sent to process by seccomp", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "arch", "info": "Information about the CPU architecture", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "syscall", "info": "Type of the system call", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "compat", "info": "Is system call in compatibility mode", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ip", "info": "Instruction pointer value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "code", "info": "The seccomp action", "section": "Column", "boost": 99, "detail": "(text)"}], "secureboot": [{"label": "secure_boot", "info": "Whether secure boot is enabled", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "secure_mode", "info": "(Intel) Secure mode: 0 disabled, 1 full security, 2 medium security", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "description", "info": "(Apple Silicon) Human-readable description: 'Full Security', 'Reduced Security', or 'Permissive Security'", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "kernel_extensions", "info": "(Apple Silicon) Allow user management of kernel extensions from identified developers (1 if allowed)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "mdm_operations", "info": "(Apple Silicon) Allow remote (MDM) management of kernel extensions and automatic software updates (1 if allowed)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "setup_mode", "info": "Whether setup mode is enabled", "section": "Column", "boost": 99, "detail": "(integer)"}], "security_profile_info": [{"label": "minimum_password_age", "info": "Determines the minimum number of days that a password must be used before the user can change it", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "maximum_password_age", "info": "Determines the maximum number of days that a password can be used before the client requires the user to change it", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "minimum_password_length", "info": "Determines the least number of characters that can make up a password for a user account", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "password_complexity", "info": "Determines whether passwords must meet a series of strong-password guidelines", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "password_history_size", "info": "Number of unique new passwords that must be associated with a user account before an old password can be reused", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "lockout_bad_count", "info": "Number of failed logon attempts after which a user account MUST be locked out", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "logon_to_change_password", "info": "Determines if logon session is required to change the password", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "force_logoff_when_expire", "info": "Determines whether SMB client sessions with the SMB server will be forcibly disconnected when the client's logon hours expire", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "new_administrator_name", "info": "Determines the name of the Administrator account on the local computer", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "new_guest_name", "info": "Determines the name of the <PERSON> account on the local computer", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "clear_text_password", "info": "Determines whether passwords MUST be stored by using reversible encryption", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "lsa_anonymous_name_lookup", "info": "Determines if an anonymous user is allowed to query the local LSA policy", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "enable_admin_account", "info": "Determines whether the Administrator account on the local computer is enabled", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "enable_guest_account", "info": "Determines whether the Guest account on the local computer is enabled", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "audit_system_events", "info": "Determines whether the operating system MUST audit System Change, System Startup, System Shutdown, Authentication Component Load, and Loss or Excess of Security events", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "audit_logon_events", "info": "Determines whether the operating system MUST audit each instance of a user attempt to log on or log off this computer", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "audit_object_access", "info": "Determines whether the operating system MUST audit each instance of user attempts to access a non-Active Directory object that has its own SACL specified", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "audit_privilege_use", "info": "Determines whether the operating system MUST audit each instance of user attempts to exercise a user right", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "audit_policy_change", "info": "Determines whether the operating system MUST audit each instance of user attempts to change user rights assignment policy, audit policy, account policy, or trust policy", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "audit_account_manage", "info": "Determines whether the operating system MUST audit each event of account management on a computer", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "audit_process_tracking", "info": "Determines whether the operating system MUST audit process-related events", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "audit_ds_access", "info": "Determines whether the operating system MUST audit each instance of user attempts to access an Active Directory object that has its own system access control list (SACL) specified", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "audit_account_logon", "info": "Determines whether the operating system MUST audit each time this computer validates the credentials of an account", "section": "Column", "boost": 99, "detail": "(integer)"}], "selinux_events": [{"label": "type", "info": "Event type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "message", "info": "Message", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uptime", "info": "Time of execution in system uptime", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "selinux_settings": [{"label": "scope", "info": "Where the key is located inside the SELinuxFS mount point.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "key", "info": "Key or class name.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Active value.", "section": "Column", "boost": 99, "detail": "(text)"}], "services": [{"label": "name", "info": "Service name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "service_type", "info": "Service Type: OWN_PROCESS, SHARE_PROCESS and maybe Interactive (can interact with the desktop)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "display_name", "info": "Service Display name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "Service Current status: STOPPED, START_PENDING, STOP_PENDING, RUNNING, CONTINUE_PENDING, PAUSE_PENDING, PAUSED", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "the Process ID of the service", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "start_type", "info": "Service start type: BOOT_START, SYSTEM_START, AUTO_START, DEMAND_START, DISABLED", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "win32_exit_code", "info": "The error code that the service uses to report an error that occurs when it is starting or stopping", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "service_exit_code", "info": "The service-specific error code that the service returns when an error occurs while the service is starting or stopping", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "path", "info": "Path to Service Executable", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "module_path", "info": "Path to ServiceDll", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Service Description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user_account", "info": "The name of the account that the service process will be logged on as when it runs. This name can be of the form Domain\\UserName. If the account belongs to the built-in domain, the name can be of the form .\\UserName.", "section": "Column", "boost": 99, "detail": "(text)"}], "shadow": [{"label": "password_status", "info": "Password status", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hash_alg", "info": "Password hashing algorithm", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_change", "info": "Date of last password change (starting from UNIX epoch date)", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "min", "info": "Minimal number of days between password changes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "max", "info": "Maximum number of days between password changes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "warning", "info": "Number of days before password expires to warn user about it", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "inactive", "info": "Number of days after password expires until account is blocked", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "expire", "info": "Number of days since UNIX epoch date until account is disabled", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "flag", "info": "Reserved", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "username", "info": "Username", "section": "Column", "boost": 99, "detail": "(text)"}], "shared_folders": [{"label": "name", "info": "The shared name of the folder as it appears to other users", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Absolute path of shared folder on the local system", "section": "Column", "boost": 99, "detail": "(text)"}], "shared_memory": [{"label": "shmid", "info": "Shared memory segment ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "owner_uid", "info": "User ID of owning process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "creator_uid", "info": "User ID of creator process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pid", "info": "Process ID to last use the segment", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "creator_pid", "info": "Process ID that created the segment", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "atime", "info": "Attached time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "dtime", "info": "Detached time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "ctime", "info": "Changed time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "permissions", "info": "Memory segment permissions", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Size in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "attached", "info": "Number of attached processes", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "status", "info": "Destination/attach status", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "locked", "info": "1 if segment is locked else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "shared_resources": [{"label": "description", "info": "A textual description of the object", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "install_date", "info": "Indicates when the object was installed. Lack of a value does not indicate that the object is not installed.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "String that indicates the current status of the object.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "allow_maximum", "info": "Number of concurrent users for this resource has been limited. If True, the value in the MaximumAllowed property is ignored.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "maximum_allowed", "info": "Limit on the maximum number of users allowed to use this resource concurrently. The value is only valid if the AllowMaximum property is set to FALSE.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "name", "info": "Alias given to a path set up as a share on a computer system running Windows.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Local path of the Windows share.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Type of resource being shared. Types include: disk drives, print queues, interprocess communications (IPC), and general devices.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "type_name", "info": "Human readable value for the 'type' column", "section": "Column", "boost": 99, "detail": "(text)"}], "sharing_preferences": [{"label": "screen_sharing", "info": "1 If screen sharing is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "file_sharing", "info": "1 If file sharing is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "printer_sharing", "info": "1 If printer sharing is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "remote_login", "info": "1 If remote login is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "remote_management", "info": "1 If remote management is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "remote_apple_events", "info": "1 If remote apple events are enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "internet_sharing", "info": "1 If internet sharing is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "bluetooth_sharing", "info": "1 If bluetooth sharing is enabled for any user else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "disc_sharing", "info": "1 If CD or DVD sharing is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "content_caching", "info": "1 If content caching is enabled else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "shell_history": [{"label": "uid", "info": "Shell history owner", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "time", "info": "Entry timestamp. It could be absent, default value is 0.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "command", "info": "Unparsed date/line/command history line", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "history_file", "info": "Path to the .*_history for this user", "section": "Column", "boost": 99, "detail": "(text)"}], "shellbags": [{"label": "sid", "info": "User SID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Shellbags source Registry file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Directory name.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "modified_time", "info": "Directory Modified time.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "created_time", "info": "Directory Created time.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "accessed_time", "info": "Directory Accessed time.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "mft_entry", "info": "Directory master file table entry.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "mft_sequence", "info": "Directory master file table sequence.", "section": "Column", "boost": 99, "detail": "(integer)"}], "shimcache": [{"label": "entry", "info": "Execution order.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "path", "info": "This is the path to the executed file.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "modified_time", "info": "File Modified time.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "execution_flag", "info": "Boolean Execution flag, 1 for execution, 0 for no execution, -1 for missing (this flag does not exist on Windows 10 and higher).", "section": "Column", "boost": 99, "detail": "(integer)"}], "signature": [{"label": "path", "info": "Must provide a path or directoryRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hash_resources", "info": "Set to 1 to also hash resources, or 0 otherwise. Default is 1", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "arch", "info": "If applicable, the arch of the signed code", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "signed", "info": "1 If the file is signed else 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "identifier", "info": "The signing identifier sealed into the signature", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "c<PERSON>sh", "info": "Hash of the application Code Directory", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "team_identifier", "info": "The team signing identifier sealed into the signature", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "authority", "info": "Certificate Common Name", "section": "Column", "boost": 99, "detail": "(text)"}], "sip_config": [{"label": "config_flag", "info": "The System Integrity Protection config flag", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "enabled", "info": "1 if this configuration is enabled, otherwise 0", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "enabled_nvram", "info": "1 if this configuration is enabled, otherwise 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "smbios_tables": [{"label": "number", "info": "Table entry number", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "Table entry type", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "description", "info": "Table entry description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "handle", "info": "Table entry handle", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "header_size", "info": "Header size in bytes", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "size", "info": "Table entry size in bytes", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "md5", "info": "MD5 hash of table entry", "section": "Column", "boost": 99, "detail": "(text)"}], "smc_keys": [{"label": "key", "info": "4-character key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "SMC-reported type literal type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "Reported size of data in bytes", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "value", "info": "A type-encoded representation of the key value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hidden", "info": "1 if this key is normally hidden, otherwise 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "socket_events": [{"label": "action", "info": "The socket action (bind, listen, close)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "path", "info": "Path of executed file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "fd", "info": "The file description for the process socket", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "auid", "info": "Audit User ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "status", "info": "Either 'succeeded', 'failed', 'in_progress' (connect() on non-blocking socket) or 'no_client' (null accept() on non-blocking socket)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "family", "info": "The Internet protocol family ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "protocol", "info": "The network protocol ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "local_address", "info": "Local address associated with socket", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "remote_address", "info": "Remote address associated with socket", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "local_port", "info": "Local network protocol port number", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "remote_port", "info": "Remote network protocol port number", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "socket", "info": "The local path (UNIX domain socket only)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uptime", "info": "Time of execution in system uptime", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "success", "info": "Deprecated. Use the 'status' column instead", "section": "Column", "boost": 99, "detail": "(integer)"}], "ssh_configs": [{"label": "uid", "info": "The local owner of the ssh_config file", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "block", "info": "The host or match block", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "option", "info": "The option and value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ssh_config_file", "info": "Path to the ssh_config file", "section": "Column", "boost": 99, "detail": "(text)"}], "startup_items": [{"label": "name", "info": "Name of startup item", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path of startup item", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "args", "info": "Arguments provided to startup executable", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Startup Item or Login Item", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Directory or plist containing startup item", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "status", "info": "Startup status; either enabled or disabled", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "username", "info": "The user associated with the startup item", "section": "Column", "boost": 99, "detail": "(text)"}], "sudoers": [{"label": "source", "info": "Source file containing the given rule", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "header", "info": "Symbol for given rule", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "rule_details", "info": "Rule definition", "section": "Column", "boost": 99, "detail": "(text)"}], "suid_bin": [{"label": "path", "info": "Binary path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "username", "info": "Binary owner username", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "groupname", "info": "Binary owner group", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "permissions", "info": "Binary permissions", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "syslog_events": [{"label": "time", "info": "Current unix epoch time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "datetime", "info": "Time known to syslog", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "host", "info": "Hostname configured for syslog", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "severity", "info": "Syslog severity", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "facility", "info": "Syslog facility", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "tag", "info": "The syslog tag", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "message", "info": "The syslog message", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "system_controls": [{"label": "name", "info": "Full sysctl MIB name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "oid", "info": "Control MIB", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subsystem", "info": "Subsystem ID, control type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "current_value", "info": "Value of setting", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "config_value", "info": "The MIB value set in /etc/sysctl.conf", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Data type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "field_name", "info": "Specific attribute of opaque type", "section": "Column", "boost": 99, "detail": "(text)"}], "system_extensions": [{"label": "path", "info": "Original path of system extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "UUID", "info": "Extension unique id", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state", "info": "System extension state", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "identifier", "info": "Identifier name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "System extension version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "category", "info": "System extension category", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bundle_path", "info": "System extension bundle path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "team", "info": "Signing team ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mdm_managed", "info": "1 if managed by MDM system extension payload configuration, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}], "system_info": [{"label": "hostname", "info": "Network hostname including domain", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uuid", "info": "Unique ID provided by the system", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cpu_type", "info": "CPU type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cpu_subtype", "info": "CPU subtype", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cpu_brand", "info": "CPU brand string, contains vendor and model", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cpu_physical_cores", "info": "Number of physical CPU cores in to the system", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_logical_cores", "info": "Number of logical CPU cores available to the system", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_sockets", "info": "Number of processor sockets in the system", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "cpu_microcode", "info": "Microcode version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "physical_memory", "info": "Total physical memory in bytes", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "hardware_vendor", "info": "Hardware vendor", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hardware_model", "info": "Hardware model", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hardware_version", "info": "Hardware version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hardware_serial", "info": "Device serial number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "board_vendor", "info": "Board vendor", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "board_model", "info": "Board model", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "board_version", "info": "Board version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "board_serial", "info": "Board serial number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "computer_name", "info": "Friendly computer name (optional)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "local_hostname", "info": "Local hostname (optional)", "section": "Column", "boost": 99, "detail": "(text)"}], "systemd_units": [{"label": "id", "info": "Unique unit identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Unit description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "load_state", "info": "Reflects whether the unit definition was properly loaded", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "active_state", "info": "The high-level unit activation state, i.e. generalization of SUB", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sub_state", "info": "The low-level unit activation state, values depend on unit type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "unit_file_state", "info": "Whether the unit file is enabled, e.g. `enabled`, `masked`, `disabled`, etc", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "following", "info": "The name of another unit that this unit follows in state", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "object_path", "info": "The object path for this unit", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "job_id", "info": "Next queued job id", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "job_type", "info": "Job type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "job_path", "info": "The object path for the job", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "fragment_path", "info": "The unit file path this unit was read from, if there is any", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user", "info": "The configured user, if any", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source_path", "info": "Path to the (possibly generated) unit configuration file", "section": "Column", "boost": 99, "detail": "(text)"}], "temperature_sensors": [{"label": "key", "info": "The SMC key on macOS", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Name of temperature source", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "celsius", "info": "Temperature in Celsius", "section": "Column", "boost": 99, "detail": "(double)"}, {"label": "fahrenheit", "info": "Temperature in Fahrenheit", "section": "Column", "boost": 99, "detail": "(double)"}], "time": [{"label": "weekday", "info": "Current weekday in UTC", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "year", "info": "Current year in UTC", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "month", "info": "Current month in UTC", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "day", "info": "Current day in UTC", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "hour", "info": "Current hour in UTC", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "minutes", "info": "Current minutes in UTC", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "seconds", "info": "Current seconds in UTC", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "timezone", "info": "Timezone for reported time (hardcoded to UTC)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "local_timezone", "info": "Current local timezone in of the system", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "unix_time", "info": "Current UNIX time in UTC", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "timestamp", "info": "Current timestamp (log format) in UTC", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "datetime", "info": "Current date and time (ISO format) in UTC", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "iso_8601", "info": "Current time (ISO format) in UTC", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "win_timestamp", "info": "Timestamp value in 100 nanosecond units", "section": "Column", "boost": 99, "detail": "(bigint)"}], "time_machine_backups": [{"label": "destination_id", "info": "Time Machine destination ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "backup_date", "info": "Backup Date", "section": "Column", "boost": 99, "detail": "(integer)"}], "time_machine_destinations": [{"label": "alias", "info": "Human readable name of drive", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "destination_id", "info": "Time Machine destination ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "consistency_scan_date", "info": "Consistency scan date", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "root_volume_uuid", "info": "Root UUID of backup volume", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bytes_available", "info": "Bytes available on volume", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "bytes_used", "info": "Bytes used on volume", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "encryption", "info": "Last known encrypted state", "section": "Column", "boost": 99, "detail": "(text)"}], "tpm_info": [{"label": "activated", "info": "TPM is activated", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "enabled", "info": "TPM is enabled", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "owned", "info": "TPM is owned", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "manufacturer_version", "info": "TPM version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "manufacturer_id", "info": "TPM manufacturers ID", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "manufacturer_name", "info": "TPM manufacturers name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "product_name", "info": "Product name of the TPM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "physical_presence_version", "info": "Version of the Physical Presence Interface", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "spec_version", "info": "Trusted Computing Group specification that the TPM supports", "section": "Column", "boost": 99, "detail": "(text)"}], "ulimit_info": [{"label": "type", "info": "System resource to be limited", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "soft_limit", "info": "Current limit value", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hard_limit", "info": "Maximum limit value", "section": "Column", "boost": 99, "detail": "(text)"}], "unified_log": [{"label": "timestamp", "info": "unix timestamp associated with the entry", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "storage", "info": "the storage category for the entry", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "message", "info": "composed message", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "activity", "info": "the activity ID associate with the entry", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "process", "info": "the name of the process that made the entry", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "the pid of the process that made the entry", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "sender", "info": "the name of the binary image that made the entry", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "tid", "info": "the tid of the thread that made the entry", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "category", "info": "the category of the os_log_t used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subsystem", "info": "the subsystem of the os_log_t used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "level", "info": "the severity level of the entry", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "max_rows", "info": "the max number of rows returned (defaults to 100)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "predicate", "info": "predicate to search (see `log help predicates`), note that this is merged into the predicate created from the column constraints", "section": "Column", "boost": 99, "detail": "(text)"}], "uptime": [{"label": "days", "info": "Days of uptime", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "hours", "info": "Hours of uptime", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "minutes", "info": "Minutes of uptime", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "seconds", "info": "Seconds of uptime", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "total_seconds", "info": "Total uptime seconds", "section": "Column", "boost": 99, "detail": "(bigint)"}], "usb_devices": [{"label": "usb_address", "info": "USB Device used address", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "usb_port", "info": "USB Device used port", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "vendor", "info": "USB Device vendor string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "vendor_id", "info": "Hex encoded USB Device vendor identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "USB Device version number", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "model", "info": "USB Device model string", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "model_id", "info": "Hex encoded USB Device model identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial", "info": "USB Device serial connection", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "class", "info": "USB Device class", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "subclass", "info": "USB Device subclass", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "protocol", "info": "USB Device protocol", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "removable", "info": "1 If USB device is removable else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "user_events": [{"label": "uid", "info": "User ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "auid", "info": "Audit User ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "pid", "info": "Process (or thread) ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "message", "info": "Message from the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "The file description for the process socket", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "path", "info": "Supplied path from event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "address", "info": "The Internet protocol address or family ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "terminal", "info": "The network protocol ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time of execution in UNIX time", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uptime", "info": "Time of execution in system uptime", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "user_groups": [{"label": "uid", "info": "User ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Group ID", "section": "Column", "boost": 99, "detail": "(bigint)"}], "user_interaction_events": [{"label": "time", "info": "Time", "section": "Column", "boost": 99, "detail": "(bigint)"}], "user_ssh_keys": [{"label": "uid", "info": "The local user that owns the key file", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "path", "info": "Path to key file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "encrypted", "info": "1 if key is encrypted, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "key_type", "info": "The type of the private key. One of [rsa, dsa, dh, ec, hmac, cmac], or the empty string.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "userassist": [{"label": "path", "info": "Application file path.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_execution_time", "info": "Most recent time application was executed.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "count", "info": "Number of times the application has been executed.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "sid", "info": "User SID.", "section": "Column", "boost": 99, "detail": "(text)"}], "users": [{"label": "uid", "info": "User ID", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid", "info": "Group ID (unsigned)", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uid_signed", "info": "User ID as int64 signed (Apple)", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "gid_signed", "info": "Default group ID as int64 signed (Apple)", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "username", "info": "Username", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Optional user description", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "directory", "info": "User's home directory", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "shell", "info": "User's configured default shell", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uuid", "info": "User's UUID (Apple) or SID (Windows)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Whether the account is roaming (domain), local, or a system profile", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "is_hidden", "info": "IsHidden attribute set in OpenDirectory", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "video_info": [{"label": "color_depth", "info": "The amount of bits per pixel to represent color.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "driver", "info": "The driver of the device.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "driver_date", "info": "The date listed on the installed driver.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "driver_version", "info": "The version of the installed driver.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "manufacturer", "info": "The manufacturer of the gpu.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "model", "info": "The model of the gpu.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "series", "info": "The series of the gpu.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "video_mode", "info": "The current resolution of the display.", "section": "Column", "boost": 99, "detail": "(text)"}], "virtual_memory_info": [{"label": "free", "info": "Total number of free pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "active", "info": "Total number of active pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "inactive", "info": "Total number of inactive pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "speculative", "info": "Total number of speculative pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "throttled", "info": "Total number of throttled pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "wired", "info": "Total number of wired down pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "purgeable", "info": "Total number of purgeable pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "faults", "info": "Total number of calls to vm_faults.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "copy", "info": "Total number of copy-on-write pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "zero_fill", "info": "Total number of zero filled pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "reactivated", "info": "Total number of reactivated pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "purged", "info": "Total number of purged pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "file_backed", "info": "Total number of file backed pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "anonymous", "info": "Total number of anonymous pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "uncompressed", "info": "Total number of uncompressed pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "compressor", "info": "The number of pages used to store compressed VM pages.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "decompressed", "info": "The total number of pages that have been decompressed by the VM compressor.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "compressed", "info": "The total number of pages that have been compressed by the VM compressor.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "page_ins", "info": "The total number of requests for pages from a pager.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "page_outs", "info": "Total number of pages paged out.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "swap_ins", "info": "The total number of compressed pages that have been swapped out to disk.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "swap_outs", "info": "The total number of compressed pages that have been swapped back in from disk.", "section": "Column", "boost": 99, "detail": "(bigint)"}], "vscode_extensions": [{"label": "name", "info": "Extension Name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "uuid", "info": "Extension UUID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "version", "info": "Extension version", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Extension path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "publisher", "info": "Publisher Name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "publisher_id", "info": "Publisher ID", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "installed_at", "info": "Installed Timestamp", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "prerelease", "info": "Pre release version", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "uid", "info": "The local user that owns the plugin", "section": "Column", "boost": 99, "detail": "(bigint)"}], "wifi_networks": [{"label": "ssid", "info": "SSID octets of the network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "network_name", "info": "Name of the network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "security_type", "info": "Type of security on this network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "last_connected", "info": "Last time this network was connected to as a unix_time", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "passpoint", "info": "1 if Passpoint is supported, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "possibly_hidden", "info": "1 if network is possibly a hidden network, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "roaming", "info": "1 if roaming is supported, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "roaming_profile", "info": "Describe the roaming profile, usually one of Single, Dual  or Multi", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "auto_login", "info": "1 if auto login is enabled, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "temporarily_disabled", "info": "1 if this network is temporarily disabled, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "disabled", "info": "1 if this network is disabled, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "add_reason", "info": "Shows why this network was added, via menubar or command line or something else ", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "added_at", "info": "Time this network was added as a unix_time", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "captive_portal", "info": "1 if this network has a captive portal, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "captive_login_date", "info": "Time this network logged in to a captive portal as unix_time", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "was_captive_network", "info": "1 if this network was previously a captive network, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "auto_join", "info": "1 if this network set to join automatically, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "personal_hotspot", "info": "1 if this network is a personal hotspot, 0 otherwise", "section": "Column", "boost": 99, "detail": "(integer)"}], "wifi_status": [{"label": "interface", "info": "Name of the interface", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ssid", "info": "SSID octets of the network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bssid", "info": "The current basic service set identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "network_name", "info": "Name of the network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "country_code", "info": "The country code (ISO/IEC 3166-1:1997) for the network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "security_type", "info": "Type of security on this network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "rssi", "info": "The current received signal strength indication (dbm)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "noise", "info": "The current noise measurement (dBm)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "channel", "info": "Channel number", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "channel_width", "info": "Channel width", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "channel_band", "info": "Channel band", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "transmit_rate", "info": "The current transmit rate", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mode", "info": "The current operating mode for the Wi-Fi interface", "section": "Column", "boost": 99, "detail": "(text)"}], "wifi_survey": [{"label": "interface", "info": "Name of the interface", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ssid", "info": "SSID octets of the network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "bssid", "info": "The current basic service set identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "network_name", "info": "Name of the network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "country_code", "info": "The country code (ISO/IEC 3166-1:1997) for the network", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "rssi", "info": "The current received signal strength indication (dbm)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "noise", "info": "The current noise measurement (dBm)", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "channel", "info": "Channel number", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "channel_width", "info": "Channel width", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "channel_band", "info": "Channel band", "section": "Column", "boost": 99, "detail": "(integer)"}], "winbaseobj": [{"label": "session_id", "info": "Terminal Services Session Id", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "object_name", "info": "Object Name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "object_type", "info": "Object Type", "section": "Column", "boost": 99, "detail": "(text)"}], "windows_crashes": [{"label": "datetime", "info": "Timestamp (log format) of the crash", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "module", "info": "Path of the crashed module within the process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "Path of the executable file for the crashed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Process ID of the crashed process", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "tid", "info": "Thread ID of the crashed thread", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "version", "info": "File version info of the crashed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "process_uptime", "info": "Uptime of the process in seconds", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "stack_trace", "info": "Multiple stack frames from the stack trace", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "exception_code", "info": "The Windows exception code", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "exception_message", "info": "The NTSTATUS error message associated with the exception code", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "exception_address", "info": "Address (in hex) where the exception occurred", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "registers", "info": "The values of the system registers", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "command_line", "info": "Command-line string passed to the crashed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "current_directory", "info": "Current working directory of the crashed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "username", "info": "Userna<PERSON> of the user who ran the crashed process", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "machine_name", "info": "Name of the machine where the crash happened", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "major_version", "info": "Windows major version of the machine", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "minor_version", "info": "Windows minor version of the machine", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "build_number", "info": "Windows build number of the crashing machine", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "type", "info": "Type of crash log", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "crash_path", "info": "Path of the log file", "section": "Column", "boost": 99, "detail": "(text)"}], "windows_eventlog": [{"label": "channel", "info": "Source or channel of the eventRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "datetime", "info": "System time at which the event occurred", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "task", "info": "Task value associated with the event", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "level", "info": "Severity level associated with the event", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "provider_name", "info": "Provider name of the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "provider_guid", "info": "Provider guid of the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "computer_name", "info": "Hostname of system where event was generated", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "eventid", "info": "Event ID of the event", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "keywords", "info": "A bitmask of the keywords defined in the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "data", "info": "Data associated with the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid", "info": "Process ID which emitted the event record", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "tid", "info": "Thread ID which emitted the event record", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "time_range", "info": "System time to selectively filter the events", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "timestamp", "info": "Timestamp to selectively filter the events", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "xpath", "info": "The custom query to filter eventsRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}], "windows_events": [{"label": "time", "info": "Timestamp the event was received", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "datetime", "info": "System time at which the event occurred", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "source", "info": "Source or channel of the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "provider_name", "info": "Provider name of the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "provider_guid", "info": "Provider guid of the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "computer_name", "info": "Hostname of system where event was generated", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "eventid", "info": "Event ID of the event", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "task", "info": "Task value associated with the event", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "level", "info": "The severity level associated with the event", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "keywords", "info": "A bitmask of the keywords defined in the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "data", "info": "Data associated with the event", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "windows_firewall_rules": [{"label": "name", "info": "Friendly name of the rule", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "app_name", "info": "Friendly name of the application to which the rule applies", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "action", "info": "Action for the rule or default setting", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "enabled", "info": "1 if the rule is enabled", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "grouping", "info": "Group to which an individual rule belongs", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "direction", "info": "Direction of traffic for which the rule applies", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "protocol", "info": "IP protocol of the rule", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "local_addresses", "info": "Local addresses for the rule", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "remote_addresses", "info": "Remote addresses for the rule", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "local_ports", "info": "Local ports for the rule", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "remote_ports", "info": "Remote ports for the rule", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "icmp_types_codes", "info": "ICMP types and codes for the rule", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "profile_domain", "info": "1 if the rule profile type is domain", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "profile_private", "info": "1 if the rule profile type is private", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "profile_public", "info": "1 if the rule profile type is public", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "service_name", "info": "Service name property of the application", "section": "Column", "boost": 99, "detail": "(text)"}], "windows_optional_features": [{"label": "name", "info": "Name of the feature", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "caption", "info": "Caption of feature in settings UI", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state", "info": "Installation state value. 1 == Enabled, 2 == Disabled, 3 == Absent", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "statename", "info": "Installation state name. 'Enabled','Disabled','Absent'", "section": "Column", "boost": 99, "detail": "(text)"}], "windows_search": [{"label": "name", "info": "The name of the item", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "path", "info": "The full path of the item.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "size", "info": "The item size in bytes.", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "date_created", "info": "The unix timestamp of when the item was created.", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "date_modified", "info": "The unix timestamp of when the item was last modified", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "owner", "info": "The owner of the item", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "The item type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "properties", "info": "Additional property values JSON", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "query", "info": "Windows search query", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sort", "info": "Sort for windows api", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "max_results", "info": "Maximum number of results returned by windows api, set to -1 for unlimited", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "additional_properties", "info": "Comma separated list of columns to include in properties JSON", "section": "Column", "boost": 99, "detail": "(text)"}], "windows_security_center": [{"label": "firewall", "info": "The health of the monitored Firewall (see windows_security_products)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "autoupdate", "info": "The health of the Windows Autoupdate feature", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "antivirus", "info": "The health of the monitored Antivirus solution (see windows_security_products)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "antispyware", "info": "Deprecated (always 'Good').", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "internet_settings", "info": "The health of the Internet Settings", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "windows_security_center_service", "info": "The health of the Windows Security Center Service", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user_account_control", "info": "The health of the User Account Control (UAC) capability in Windows", "section": "Column", "boost": 99, "detail": "(text)"}], "windows_security_products": [{"label": "type", "info": "Type of security product", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Name of product", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state", "info": "State of protection", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "state_timestamp", "info": "Timestamp for the product state", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "remediation_path", "info": "Remediation path", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "signatures_up_to_date", "info": "1 if product signatures are up to date, else 0", "section": "Column", "boost": 99, "detail": "(integer)"}], "windows_update_history": [{"label": "client_app_id", "info": "Identifier of the client application that processed an update", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "date", "info": "Date and the time an update was applied", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "description", "info": "Description of an update", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hresult", "info": "HRESULT value that is returned from the operation on an update", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "operation", "info": "Operation on an update", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "result_code", "info": "Result of an operation on an update", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "server_selection", "info": "Value that indicates which server provided an update", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "service_id", "info": "Service identifier of an update service that is not a Windows update", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "support_url", "info": "Hyperlink to the language-specific support information for an update", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "title", "info": "Title of an update", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "update_id", "info": "Revision-independent identifier of an update", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "update_revision", "info": "Revision number of an update", "section": "Column", "boost": 99, "detail": "(bigint)"}], "wmi_bios_info": [{"label": "name", "info": "Name of the Bios setting", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "value", "info": "Value of the Bios setting", "section": "Column", "boost": 99, "detail": "(text)"}], "wmi_cli_event_consumers": [{"label": "name", "info": "Unique name of a consumer.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "command_line_template", "info": "Standard string template that specifies the process to be started. This property can be NULL, and the ExecutablePath property is used as the command line.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "executable_path", "info": "Module to execute. The string can specify the full path and file name of the module to execute, or it can specify a partial name. If a partial name is specified, the current drive and current directory are assumed.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "class", "info": "The name of the class.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "relative_path", "info": "Relative path to the class or instance.", "section": "Column", "boost": 99, "detail": "(text)"}], "wmi_event_filters": [{"label": "name", "info": "Unique identifier of an event filter.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "query", "info": "Windows Management Instrumentation Query Language (WQL) event query that specifies the set of events for consumer notification, and the specific conditions for notification.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "query_language", "info": "Query language that the query is written in.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "class", "info": "The name of the class.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "relative_path", "info": "Relative path to the class or instance.", "section": "Column", "boost": 99, "detail": "(text)"}], "wmi_filter_consumer_binding": [{"label": "consumer", "info": "Reference to an instance of __EventConsumer that represents the object path to a logical consumer, the recipient of an event.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "filter", "info": "Reference to an instance of __EventFilter that represents the object path to an event filter which is a query that specifies the type of event to be received.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "class", "info": "The name of the class.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "relative_path", "info": "Relative path to the class or instance.", "section": "Column", "boost": 99, "detail": "(text)"}], "wmi_script_event_consumers": [{"label": "name", "info": "Unique identifier for the event consumer. ", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "scripting_engine", "info": "Name of the scripting engine to use, for example, 'VBScript'. This property cannot be NULL.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "script_file_name", "info": "Name of the file from which the script text is read, intended as an alternative to specifying the text of the script in the ScriptText property.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "script_text", "info": "Text of the script that is expressed in a language known to the scripting engine. This property must be NULL if the ScriptFileName property is not NULL.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "class", "info": "The name of the class.", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "relative_path", "info": "Relative path to the class or instance.", "section": "Column", "boost": 99, "detail": "(text)"}], "xprotect_entries": [{"label": "name", "info": "Description of XProtected malware", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "launch_type", "info": "Launch services content type", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "identity", "info": "XProtect identity (SHA1) of content", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "filename", "info": "Use this file name to match", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "filetype", "info": "Use this file type to match", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "optional", "info": "Match any of the identities/patterns for this XProtect name", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "uses_pattern", "info": "Uses a match pattern instead of identity", "section": "Column", "boost": 99, "detail": "(integer)"}], "xprotect_meta": [{"label": "identifier", "info": "Browser plugin or extension identifier", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "type", "info": "Either plugin or extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "developer_id", "info": "Developer identity (SHA1) of extension", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "min_version", "info": "The minimum allowed plugin version.", "section": "Column", "boost": 99, "detail": "(text)"}], "xprotect_reports": [{"label": "name", "info": "Description of XProtected malware", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "user_action", "info": "Action taken by user after prompted", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Quarantine alert time", "section": "Column", "boost": 99, "detail": "(text)"}], "yara": [{"label": "path", "info": "The path scannedRequired in WHERE clause", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "matches", "info": "List of YARA matches", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "count", "info": "Number of YARA matches", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "sig_group", "info": "Signature group used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sigfile", "info": "Signature file used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sigrule", "info": "Signature strings used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "strings", "info": "Matching strings", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "tags", "info": "Matching tags", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "sigurl", "info": "Signature url", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}], "yara_events": [{"label": "target_path", "info": "The path scanned", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "category", "info": "The category of the file", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "action", "info": "Change action (UPDATE, REMOVE, etc)", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "transaction_id", "info": "ID used during bulk update", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "matches", "info": "List of YARA matches", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "count", "info": "Number of YARA matches", "section": "Column", "boost": 99, "detail": "(integer)"}, {"label": "strings", "info": "Matching strings", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "tags", "info": "Matching tags", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "time", "info": "Time of the scan", "section": "Column", "boost": 99, "detail": "(bigint)"}, {"label": "eid", "info": "Event ID", "section": "Column", "boost": 99, "detail": "(text)"}], "ycloud_instance_metadata": [{"label": "instance_id", "info": "Unique identifier for the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "folder_id", "info": "Folder identifier for the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "cloud_id", "info": "Cloud identifier for the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "name", "info": "Name of the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "description", "info": "Description of the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "hostname", "info": "Hostname of the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "zone", "info": "Availability zone of the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "ssh_public_key", "info": "SSH public key. Only available if supplied at instance launch time", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "serial_port_enabled", "info": "Indicates if serial port is enabled for the VM", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "metadata_endpoint", "info": "Endpoint used to fetch VM metadata", "section": "Column", "boost": 99, "detail": "(text)"}], "yum_sources": [{"label": "name", "info": "Repository name", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "baseurl", "info": "Repository base URL", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "mirrorlist", "info": "Mirrorlist URL", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "enabled", "info": "Whether the repository is used", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "gpgcheck", "info": "Whether packages are GPG checked", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "gpgkey", "info": "URL to GPG key", "section": "Column", "boost": 99, "detail": "(text)"}, {"label": "pid_with_namespace", "info": "Pids that contain a namespace", "section": "Column", "boost": 99, "detail": "(integer)"}]}