[{"label": "account_policy_data", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Additional macOS user account data from the AccountPolicy section of OpenDirectory."}, {"label": "acpi_tables", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Firmware ACPI functional table common metadata and content."}, {"label": "ad_config", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS Active Directory configuration."}, {"label": "alf", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS application layer firewall (ALF) service details."}, {"label": "alf_exceptions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS application layer firewall (ALF) service exceptions."}, {"label": "alf_explicit_auths", "section": "Table", "boost": 99, "detail": "(live table)", "info": "ALF services explicitly allowed to perform networking."}, {"label": "app_schemes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS application schemes and handlers (e.g., http, file, mailto)."}, {"label": "apparmor_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track AppArmor events."}, {"label": "apparmor_profiles", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track active AppArmor profiles."}, {"label": "appcompat_shims", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Application Compatibility shims are a way to persist malware. This table presents the AppCompat Shim information from the registry in a nice format. See http://files.brucon.org/2015/<PERSON><PERSON><PERSON>_and_<PERSON>enthin_Shims_for_the_Win.pdf for more details."}, {"label": "apps", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS applications installed in known search paths (e.g., /Applications)."}, {"label": "apt_sources", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Current list of APT repositories or software channels."}, {"label": "arp_cache", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Address resolution cache, both static and dynamic (from ARP, NDP)."}, {"label": "asl", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Queries the Apple System Log data structure for system events."}, {"label": "augeas", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Configuration files parsed by augeas."}, {"label": "authenticode", "section": "Table", "boost": 99, "detail": "(live table)", "info": "File (executable, bundle, installer, disk) code signing status."}, {"label": "authorization_mechanisms", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS Authorization mechanisms database."}, {"label": "authorizations", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS Authorization rights database."}, {"label": "authorized_keys", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A line-delimited authorized_keys table."}, {"label": "autoexec", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Aggregate of executables that will automatically execute on the target machine. This is an amalgamation of other tables like services, scheduled_tasks, startup_items and more."}, {"label": "azure_instance_metadata", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Azure instance metadata."}, {"label": "azure_instance_tags", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Azure instance tags."}, {"label": "background_activities_moderator", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Background Activities Moderator (BAM) tracks application execution."}, {"label": "battery", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Provides information about the internal battery of a Macbook."}, {"label": "bitlocker_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Retrieve bitlocker status of the machine."}, {"label": "block_devices", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Block (buffered access) device file nodes: disks, ramdisks, and DMG containers."}, {"label": "bpf_process_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track time/action process executions."}, {"label": "bpf_socket_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track network socket opens and closes."}, {"label": "browser_plugins", "section": "Table", "boost": 99, "detail": "(live table)", "info": "All C/NPAPI browser plugin details for all users. C/NPAPI has been deprecated on all major browsers. To query for plugins on modern browsers, try: `chrome_extensions` `firefox_addons` `safari_extensions`."}, {"label": "carbon_black_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Returns info about a Carbon Black sensor install."}, {"label": "carves", "section": "Table", "boost": 99, "detail": "(live table)", "info": "List the set of completed and in-progress carves. If carve=1 then the query is treated as a new carve request."}, {"label": "certificates", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Certificate Authorities installed in Keychains/ca-bundles. NOTE: o<PERSON>ry limits frequent access to keychain files on macOS. This limit is controlled by keychain_access_interval flag."}, {"label": "chassis_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Display information pertaining to the chassis and its security status."}, {"label": "chocolatey_packages", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Chocolatey packages installed in a system."}, {"label": "chrome_extension_content_scripts", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Chrome browser extension content scripts."}, {"label": "chrome_extensions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Chrome-based browser extensions."}, {"label": "connected_displays", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Provides information about the connected displays of the machine."}, {"label": "connectivity", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Provides the overall system's network state."}, {"label": "cpu_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Retrieve cpu hardware info of the machine."}, {"label": "cpu_time", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Displays information from /proc/stat file about the time the cpu cores spent in different parts of the system."}, {"label": "cpuid", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Useful CPU features from the cpuid ASM call."}, {"label": "crashes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Application, System, and Mobile App crash logs."}, {"label": "crontab", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Line parsed values from system and user cron/tab."}, {"label": "cups_destinations", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Returns all configured printers."}, {"label": "cups_jobs", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Returns all completed print jobs from cups."}, {"label": "curl", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Perform an http request and return stats about it."}, {"label": "curl_certificate", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Inspect TLS certificates by connecting to input hostnames."}, {"label": "deb_packages", "section": "Table", "boost": 99, "detail": "(live table)", "info": "The installed DEB package database."}, {"label": "default_environment", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Default environment variables and values."}, {"label": "device_file", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Similar to the file table, but use TSK and allow block address access."}, {"label": "device_firmware", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A best-effort list of discovered firmware versions."}, {"label": "device_hash", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Similar to the hash table, but use TSK and allow block address access."}, {"label": "device_partitions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Use TSK to enumerate details about partitions on a disk device."}, {"label": "disk_encryption", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Disk encryption status and information."}, {"label": "disk_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track DMG disk image events (appearance/disappearance) when opened."}, {"label": "disk_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Retrieve basic information about the physical disks of a system."}, {"label": "dns_cache", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Enumerate the DNS cache using the undocumented DnsGetCacheDataTable function in dnsapi.dll."}, {"label": "dns_resolvers", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Resolvers used by this host."}, {"label": "docker_container_envs", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker container environment variables."}, {"label": "docker_container_fs_changes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Changes to files or directories on container's filesystem."}, {"label": "docker_container_labels", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker container labels."}, {"label": "docker_container_mounts", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker container mounts."}, {"label": "docker_container_networks", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker container networks."}, {"label": "docker_container_ports", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker container ports."}, {"label": "docker_container_processes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker container processes."}, {"label": "docker_container_stats", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker container statistics. Queries on this table take at least one second."}, {"label": "docker_containers", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker containers information."}, {"label": "docker_image_history", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker image history information."}, {"label": "docker_image_labels", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker image labels."}, {"label": "docker_image_layers", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker image layers information."}, {"label": "docker_images", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker images information."}, {"label": "docker_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker system information."}, {"label": "docker_network_labels", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker network labels."}, {"label": "docker_networks", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker networks information."}, {"label": "docker_version", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker version information."}, {"label": "docker_volume_labels", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker volume labels."}, {"label": "docker_volumes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Docker volumes information."}, {"label": "drivers", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Details for in-use Windows device drivers. This does not display installed but unused drivers."}, {"label": "ec2_instance_metadata", "section": "Table", "boost": 99, "detail": "(live table)", "info": "EC2 instance metadata."}, {"label": "ec2_instance_tags", "section": "Table", "boost": 99, "detail": "(live table)", "info": "EC2 instance tag key value pairs."}, {"label": "es_process_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Process execution events from EndpointSecurity."}, {"label": "es_process_file_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "File integrity monitoring events from EndpointSecurity including process context."}, {"label": "etc_hosts", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Line-parsed /etc/hosts."}, {"label": "etc_protocols", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Line-parsed /etc/protocols."}, {"label": "etc_services", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Line-parsed /etc/services."}, {"label": "event_taps", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Returns information about installed event taps."}, {"label": "extended_attributes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Returns the extended attributes for files (similar to Windows ADS)."}, {"label": "fan_speed_sensors", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Fan speeds."}, {"label": "file", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Interactive filesystem attributes and metadata."}, {"label": "file_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track time/action changes to files specified in configuration data."}, {"label": "firefox_addons", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Firefox browser extensions, webapps, and addons."}, {"label": "gatekeeper", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS Gatekeeper Details."}, {"label": "gatekeeper_approved_apps", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Gatekeeper apps a user has allowed to run."}, {"label": "groups", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Local system groups."}, {"label": "hardware_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Hardware (PCI/USB/HID) events from UDEV or IOKit."}, {"label": "hash", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Filesystem hash data."}, {"label": "homebrew_packages", "section": "Table", "boost": 99, "detail": "(live table)", "info": "The installed homebrew package database."}, {"label": "hvci_status", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Retrieve HVCI info of the machine."}, {"label": "ibridge_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Information about the Apple iBridge hardware controller."}, {"label": "ie_extensions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Internet Explorer browser extensions."}, {"label": "intel_me_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Intel ME/CSE Info."}, {"label": "interface_addresses", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Network interfaces and relevant metadata."}, {"label": "interface_details", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Detailed information and stats of network interfaces."}, {"label": "interface_ipv6", "section": "Table", "boost": 99, "detail": "(live table)", "info": "IPv6 configuration and stats of network interfaces."}, {"label": "iokit_devicetree", "section": "Table", "boost": 99, "detail": "(live table)", "info": "The IOKit registry matching the DeviceTree plane."}, {"label": "iokit_registry", "section": "Table", "boost": 99, "detail": "(live table)", "info": "The full IOKit registry without selecting a plane."}, {"label": "iptables", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Linux IP packet filtering and NAT tool."}, {"label": "kernel_extensions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS's kernel extensions, both loaded and within the load search path."}, {"label": "kernel_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Basic active kernel information."}, {"label": "kernel_keys", "section": "Table", "boost": 99, "detail": "(live table)", "info": "List of security data, authentication keys and encryption keys."}, {"label": "kernel_modules", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Linux kernel modules both loaded and within the load search path."}, {"label": "kernel_panics", "section": "Table", "boost": 99, "detail": "(live table)", "info": "System kernel panic logs."}, {"label": "keychain_acls", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Applications that have ACL entries in the keychain. NOTE: osquery limits frequent access to keychain files. This limit is controlled by keychain_access_interval flag."}, {"label": "keychain_items", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Generic details about keychain items. NOTE: osquery limits frequent access to keychain files. This limit is controlled by keychain_access_interval flag."}, {"label": "known_hosts", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A line-delimited known_hosts table."}, {"label": "kva_speculative_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Display kernel virtual address and speculative execution information for the system."}, {"label": "last", "section": "Table", "boost": 99, "detail": "(live table)", "info": "System logins and logouts."}, {"label": "launchd", "section": "Table", "boost": 99, "detail": "(live table)", "info": "LaunchAgents and LaunchDaemons from default search paths."}, {"label": "launchd_overrides", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Override keys, per user, for LaunchDaemons and Agents."}, {"label": "listening_ports", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Processes with listening (bound) network sockets/ports."}, {"label": "load_average", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Displays information about the system wide load averages."}, {"label": "location_services", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Reports the status of the Location Services feature of the OS."}, {"label": "logged_in_users", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Users with an active shell on the system."}, {"label": "logical_drives", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Details for logical drives on the system. A logical drive generally represents a single partition."}, {"label": "logon_sessions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Windows Logon Session."}, {"label": "lxd_certificates", "section": "Table", "boost": 99, "detail": "(live table)", "info": "LXD certificates information."}, {"label": "lxd_cluster", "section": "Table", "boost": 99, "detail": "(live table)", "info": "LXD cluster information."}, {"label": "lxd_cluster_members", "section": "Table", "boost": 99, "detail": "(live table)", "info": "LXD cluster members information."}, {"label": "lxd_images", "section": "Table", "boost": 99, "detail": "(live table)", "info": "LXD images information."}, {"label": "lxd_instance_config", "section": "Table", "boost": 99, "detail": "(live table)", "info": "LXD instance configuration information."}, {"label": "lxd_instance_devices", "section": "Table", "boost": 99, "detail": "(live table)", "info": "LXD instance devices information."}, {"label": "lxd_instances", "section": "Table", "boost": 99, "detail": "(live table)", "info": "LXD instances information."}, {"label": "lxd_networks", "section": "Table", "boost": 99, "detail": "(live table)", "info": "LXD network information."}, {"label": "lxd_storage_pools", "section": "Table", "boost": 99, "detail": "(live table)", "info": "LXD storage pool information."}, {"label": "magic", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Magic number recognition library table."}, {"label": "managed_policies", "section": "Table", "boost": 99, "detail": "(live table)", "info": "The managed configuration policies from AD, MDM, MCX, etc."}, {"label": "md_devices", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Software RAID array settings."}, {"label": "md_drives", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Drive devices used for Software RAID."}, {"label": "md_personalities", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Software RAID setting supported by the kernel."}, {"label": "mdfind", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Run searches against the spotlight database."}, {"label": "mdls", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Query file metadata in the Spotlight database."}, {"label": "memory_array_mapped_addresses", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Data associated for address mapping of physical memory arrays."}, {"label": "memory_arrays", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Data associated with collection of memory devices that operate to form a memory address."}, {"label": "memory_device_mapped_addresses", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Data associated for address mapping of physical memory devices."}, {"label": "memory_devices", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Physical memory device (type 17) information retrieved from SMBIOS."}, {"label": "memory_error_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Data associated with errors of a physical memory array."}, {"label": "memory_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Main memory information in bytes."}, {"label": "memory_map", "section": "Table", "boost": 99, "detail": "(live table)", "info": "OS memory region map."}, {"label": "mounts", "section": "Table", "boost": 99, "detail": "(live table)", "info": "System mounted devices and filesystems (not process specific)."}, {"label": "msr", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Various pieces of data stored in the model specific register per processor. NOTE: the msr kernel module must be enabled, and osquery must be run as root."}, {"label": "nfs_shares", "section": "Table", "boost": 99, "detail": "(live table)", "info": "NFS shares exported by the host."}, {"label": "npm_packages", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Node packages installed in a system."}, {"label": "ntdomains", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Display basic NT domain information of a Windows machine."}, {"label": "ntfs_acl_permissions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Retrieve NTFS ACL permission information for files and directories."}, {"label": "ntfs_journal_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track time/action changes to files specified in configuration data."}, {"label": "nvram", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Apple NVRAM variable listing."}, {"label": "oem_strings", "section": "Table", "boost": 99, "detail": "(live table)", "info": "OEM defined strings retrieved from SMBIOS."}, {"label": "office_mru", "section": "Table", "boost": 99, "detail": "(live table)", "info": "View recently opened Office documents."}, {"label": "os_version", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A single row containing the operating system name and version."}, {"label": "osquery_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Information about the event publishers and subscribers."}, {"label": "osquery_extensions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "List of active osquery extensions."}, {"label": "osquery_flags", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Configurable flags that modify osquery's behavior."}, {"label": "osquery_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Top level information about the running version of osquery."}, {"label": "osquery_packs", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Information about the current query packs that are loaded in osquery."}, {"label": "osquery_registry", "section": "Table", "boost": 99, "detail": "(live table)", "info": "List the osquery registry plugins."}, {"label": "osquery_schedule", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Information about the current queries that are scheduled in osquery."}, {"label": "package_bom", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS package bill of materials (BOM) file list."}, {"label": "package_install_history", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS package install history."}, {"label": "package_receipts", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS package receipt details."}, {"label": "password_policy", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Password Policies for macOS."}, {"label": "patches", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Lists all the patches applied. Note: This does not include patches applied via MSI or downloaded from Windows Update (e.g. Service Packs)."}, {"label": "pci_devices", "section": "Table", "boost": 99, "detail": "(live table)", "info": "PCI devices active on the host system."}, {"label": "physical_disk_performance", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Provides provides raw data from performance counters that monitor hard or fixed disk drives on the system."}, {"label": "pipes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Named and Anonymous pipes."}, {"label": "platform_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Information about EFI/UEFI/ROM and platform/boot."}, {"label": "plist", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Read and parse a plist file."}, {"label": "portage_keywords", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A summary about portage configurations like keywords, mask and unmask."}, {"label": "portage_packages", "section": "Table", "boost": 99, "detail": "(live table)", "info": "List of currently installed packages."}, {"label": "portage_use", "section": "Table", "boost": 99, "detail": "(live table)", "info": "List of enabled portage USE values for specific package."}, {"label": "power_sensors", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Machine power (currents, voltages, wattages, etc) sensors."}, {"label": "powershell_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Powershell script blocks reconstructed to their full script content, this table requires script block logging to be enabled."}, {"label": "preferences", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS defaults and managed preferences."}, {"label": "prefetch", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Prefetch files show metadata related to file execution."}, {"label": "process_envs", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A key/value table of environment variables for each process."}, {"label": "process_etw_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Windows process execution events."}, {"label": "process_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track time/action process executions."}, {"label": "process_file_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A File Integrity Monitor implementation using the audit service."}, {"label": "process_memory_map", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Process memory mapped files and pseudo device/regions."}, {"label": "process_namespaces", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Linux namespaces for processes running on the host system."}, {"label": "process_open_files", "section": "Table", "boost": 99, "detail": "(live table)", "info": "File descriptors for each process."}, {"label": "process_open_pipes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Pipes and partner processes for each process."}, {"label": "process_open_sockets", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Processes which have open network sockets on the system."}, {"label": "processes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "All running processes on the host system."}, {"label": "programs", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Represents products as they are installed by Windows Installer. A product generally correlates to one installation package on Windows. Some fields may be blank as Windows installation details are left to the discretion of the product author."}, {"label": "prometheus_metrics", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Retrieve metrics from a Prometheus server."}, {"label": "python_packages", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Python packages installed in a system."}, {"label": "quicklook_cache", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Files and thumbnails within macOS's Quicklook Cache."}, {"label": "registry", "section": "Table", "boost": 99, "detail": "(live table)", "info": "All of the Windows registry hives."}, {"label": "routes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "The active route table for the host system."}, {"label": "rpm_package_files", "section": "Table", "boost": 99, "detail": "(live table)", "info": "RPM packages that are currently installed on the host system."}, {"label": "rpm_packages", "section": "Table", "boost": 99, "detail": "(live table)", "info": "RPM packages that are currently installed on the host system."}, {"label": "running_apps", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS applications currently running on the host system."}, {"label": "safari_extensions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Safari browser extension details for all users. This table requires Full Disk Access (FDA) permission."}, {"label": "sandboxes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS application sandboxes container details."}, {"label": "scheduled_tasks", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Lists all of the tasks in the Windows task scheduler."}, {"label": "screenlock", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS screenlock status. Note: only fetches results for o<PERSON>ry's current logged-in user context. The user must also have recently logged in."}, {"label": "seccomp_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A virtual table that tracks seccomp events."}, {"label": "secureboot", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Secure Boot UEFI Settings."}, {"label": "security_profile_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Information on the security profile of a given system by listing the system Account and Audit Policies. This table mimics the exported securitypolicy output from the secedit tool."}, {"label": "selinux_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track SELinux events."}, {"label": "selinux_settings", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track active SELinux settings."}, {"label": "services", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Lists all installed Windows services and their relevant data."}, {"label": "shadow", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Local system users encrypted passwords and related information. Please note, that you usually need superuser rights to access `/etc/shadow`."}, {"label": "shared_folders", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Folders available to others via SMB or AFP."}, {"label": "shared_memory", "section": "Table", "boost": 99, "detail": "(live table)", "info": "OS shared memory regions."}, {"label": "shared_resources", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Displays shared resources on a computer system running Windows. This may be a disk drive, printer, interprocess communication, or other sharable device."}, {"label": "sharing_preferences", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS Sharing preferences."}, {"label": "shell_history", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A line-delimited (command) table of per-user .*_history data."}, {"label": "shellbags", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Shows directories accessed via Windows Explorer."}, {"label": "shimcache", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Application Compatibility Cache, contains artifacts of execution."}, {"label": "signature", "section": "Table", "boost": 99, "detail": "(live table)", "info": "File (executable, bundle, installer, disk) code signing status."}, {"label": "sip_config", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Apple's System Integrity Protection (rootless) status."}, {"label": "smbios_tables", "section": "Table", "boost": 99, "detail": "(live table)", "info": "BIOS (DMI) structure common details and content."}, {"label": "smc_keys", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Apple's system management controller keys."}, {"label": "socket_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track network socket opens and closes."}, {"label": "ssh_configs", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A table of parsed ssh_configs."}, {"label": "startup_items", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Applications and binaries set as user/login startup items."}, {"label": "sudoers", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Rules for running commands as other users via sudo."}, {"label": "suid_bin", "section": "Table", "boost": 99, "detail": "(live table)", "info": "suid binaries in common locations."}, {"label": "syslog_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": ""}, {"label": "system_controls", "section": "Table", "boost": 99, "detail": "(live table)", "info": "sysctl names, values, and settings information."}, {"label": "system_extensions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS (>= 10.15) system extension table."}, {"label": "system_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "System information for identification."}, {"label": "systemd_units", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track systemd units."}, {"label": "temperature_sensors", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Machine's temperature sensors."}, {"label": "time", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track current date and time in UTC."}, {"label": "time_machine_backups", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Backups to drives using TimeMachine."}, {"label": "time_machine_destinations", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Locations backed up to using Time Machine."}, {"label": "tpm_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "A table that lists the TPM related information."}, {"label": "ulimit_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "System resource usage limits."}, {"label": "unified_log", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Queries the OSLog framework for entries in the system log. The maximum number of rows returned is limited for performance issues. Use timestamp > or >= constraints to optimize query performance. This table introduces a new idiom for extracting sequential data in batches using multiple queries, ordered by timestamp. To trigger it, the user should include the condition \"timestamp > -1\", and the table will handle pagination. Note that the saved pagination counter is incremented globally across all queries and table invocations within a query. To avoid multiple table invocations within a query, use only AND and = constraints in WHERE clause."}, {"label": "uptime", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track time passed since last boot. Some systems track this as calendar time, some as runtime."}, {"label": "usb_devices", "section": "Table", "boost": 99, "detail": "(live table)", "info": "USB devices that are actively plugged into the host system."}, {"label": "user_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track user events from the audit framework."}, {"label": "user_groups", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Local system user group relationships."}, {"label": "user_interaction_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track user interaction events from macOS' event tapping framework."}, {"label": "user_ssh_keys", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Returns the private keys in the users ~/.ssh directory and whether or not they are encrypted."}, {"label": "userassist", "section": "Table", "boost": 99, "detail": "(live table)", "info": "UserAssist Registry Key tracks when a user executes an application from Windows Explorer."}, {"label": "users", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Local user accounts (including domain accounts that have logged on locally (Windows))."}, {"label": "video_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Retrieve video card information of the machine."}, {"label": "virtual_memory_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Darwin Virtual Memory statistics."}, {"label": "vscode_extensions", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Lists all vscode extensions."}, {"label": "wifi_networks", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS known/remembered Wi-Fi networks list."}, {"label": "wifi_status", "section": "Table", "boost": 99, "detail": "(live table)", "info": "macOS current WiFi status."}, {"label": "wifi_survey", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Scan for nearby WiFi networks."}, {"label": "winbaseobj", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Lists named Windows objects in the default object directories, across all terminal services sessions.  Example Windows ojbect types include Mutexes, Events, Jobs and Semaphors."}, {"label": "windows_crashes", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Extracted information from Windows crash logs (Minidumps)."}, {"label": "windows_eventlog", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Table for querying all recorded Windows event logs."}, {"label": "windows_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Windows Event logs."}, {"label": "windows_firewall_rules", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Provides the list of Windows firewall rules."}, {"label": "windows_optional_features", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Lists names and installation states of windows features. Maps to Win32_OptionalFeature WMI class."}, {"label": "windows_search", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Run searches against the Windows system index database using Advanced Query Syntax. See https://learn.microsoft.com/en-us/windows/win32/search/-search-3x-advancedquerysyntax for details."}, {"label": "windows_security_center", "section": "Table", "boost": 99, "detail": "(live table)", "info": "The health status of Window Security features. Health values can be \"Good\", \"Poor\". \"Snoozed\", \"Not Monitored\", and \"Error\"."}, {"label": "windows_security_products", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Enumeration of registered Windows security products. Note: Not compatible with Windows Server."}, {"label": "windows_update_history", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Provides the history of the windows update events."}, {"label": "wmi_bios_info", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Lists important information from the system bios."}, {"label": "wmi_cli_event_consumers", "section": "Table", "boost": 99, "detail": "(live table)", "info": "WMI CommandLineEventConsumer, which can be used for persistence on Windows. See https://www.blackhat.com/docs/us-15/materials/us-15-Graeber-Abusing-Windows-Management-Instrumentation-WMI-To-Build-A-Persistent%20Asynchronous-And-Fileless-Backdoor-wp.pdf for more details."}, {"label": "wmi_event_filters", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Lists WMI event filters."}, {"label": "wmi_filter_consumer_binding", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Lists the relationship between event consumers and filters."}, {"label": "wmi_script_event_consumers", "section": "Table", "boost": 99, "detail": "(live table)", "info": "WMI ActiveScriptEventConsumer, which can be used for persistence on Windows. See https://www.blackhat.com/docs/us-15/materials/us-15-Graeber-Abusing-Windows-Management-Instrumentation-WMI-To-Build-A-Persistent%20Asynchronous-And-Fileless-Backdoor-wp.pdf for more details."}, {"label": "xprotect_entries", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Database of the machine's XProtect signatures."}, {"label": "xprotect_meta", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Database of the machine's XProtect browser-related signatures."}, {"label": "xprotect_reports", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Database of XProtect matches (if user generated/sent an XProtect report)."}, {"label": "yara", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Triggers one-off YARA query for files at the specified path. Requires one of `sig_group`, `sigfile`, or `sigrule`."}, {"label": "yara_events", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Track YARA matches for files specified in configuration data."}, {"label": "ycloud_instance_metadata", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Yandex.Cloud instance metadata."}, {"label": "yum_sources", "section": "Table", "boost": 99, "detail": "(live table)", "info": "Current list of Yum repositories or software channels."}]