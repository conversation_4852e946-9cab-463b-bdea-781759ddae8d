/**
 * to extract full schema
 */

function extractFullSchema() {
  const tables = {};

  const tablesDivs = document.querySelectorAll('.schema__table');

  for (let i = 0; i < tablesDivs.length; i++) {
    let tableDiv = tablesDivs[i];

    let titleTd = tableDiv.querySelector('.osquery-table__table-name');
    let tableName = '';
    for (let i = 0; i < titleTd.childNodes.length; ++i) {
      if (titleTd.childNodes[i].nodeType === Node.TEXT_NODE) {
        tableName += titleTd.childNodes[i].textContent;
      }
    }

    let columns = [];

    tableDiv.querySelectorAll('tbody tr').forEach((node) => {
      columns.push({
        label: node.querySelector('td:first-child').textContent,
        info: node.querySelector('td.osquery-table__description').textContent,
        section: 'Column',
        boost: 99,
        detail: `(${node.querySelector('td.osquery-table__type').textContent})`
      });
    });

    tables[tableName] = columns;
  }

  console.log(JSON.stringify(tables));
}

function extractTableDetails() {
  const tables = [];

  const tablesDivs = document.querySelectorAll('.schema__table');

  for (let i = 0; i < tablesDivs.length; i++) {
    let tableDiv = tablesDivs[i];

    let titleTd = tableDiv.querySelector('.osquery-table__table-name');
    let label = '';
    for (let i = 0; i < titleTd.childNodes.length; ++i) {
      if (titleTd.childNodes[i].nodeType === Node.TEXT_NODE) {
        label += titleTd.childNodes[i].textContent;
      }
    }
    tables.push({
      label,
      section: 'Table',
      boost: 99,
      detail: '(live table)',
      info: tableDiv.querySelector('.osquery-table__table-description').textContent
    });
  }

  console.log(JSON.stringify(tables));
}
