import { useEffect } from 'react';

const ApiPoller = ({ apiFunction, timer = 20000, onUpdate, useInitialFatch = false }) => {
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await apiFunction();
        onUpdate(response); // Send the data to the parent component
      } catch (err) {
        // console.error(err); // Handle error accordingly
      }
    };

    if (useInitialFatch) {
      // Fetch data initially and set interval for repeated API calls
      fetchData();
    }

    const intervalId = setInterval(fetchData, timer);

    // Cleanup function to clear the interval when the component unmounts
    return () => clearInterval(intervalId);
  }, [apiFunction, timer, onUpdate, useInitialFatch]);

  return null; // The component doesn't render anything
};

export default ApiPoller;
