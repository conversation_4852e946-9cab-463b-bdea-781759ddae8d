import React from 'react';
import { useLayout } from '@/src/layouts/Layout';

const CopyOnDoubleClick = ({ text, children, className }) => {
  const { message } = useLayout();

  const handleDoubleClick = () => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        return message.success(`Text copied to clipboard!`);
      })
      .catch((err) => {
        return message.error(`Failed to copy text: ${err}`);
      });
  };

  return (
    <div
      onDoubleClick={handleDoubleClick}
      className={className}
      style={{
        cursor: 'copy',
        textWrap: 'nowrap',
        textOverflow: 'ellipsis',
        maxWidth: '200px',
        overflow: 'hidden'
      }}
      title={text}>
      {children || text}
    </div>
  );
};

export default CopyOnDoubleClick;
