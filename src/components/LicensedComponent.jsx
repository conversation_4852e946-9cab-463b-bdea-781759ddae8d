import { License } from './LicenseProvider';
import NotFound from './NotFound';

LicensedComponent.EndpointOps = 'EndpointOps';
LicensedComponent.ZiroXpose = 'ZiroXpose';
LicensedComponent.ZiroPatch = 'ZiroPatch';

export default function LicensedComponent({
  children,
  allowedProducts = [],
  excludedProducts = [],
  useNotFound
}) {
  const { license } = License.useLicense();

  if (
    license.licenseProduct !== LicensedComponent.EndpointOps &&
    ((allowedProducts.length && allowedProducts.includes(license.licenseProduct) === false) ||
      excludedProducts.includes(license.licenseProduct))
  ) {
    if (useNotFound) {
      return <NotFound redirectTo="/dashboard" />;
    }
    return null;
  }

  return children;
}
