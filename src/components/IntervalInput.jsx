import { Input, InputNumber, Select, Form } from 'antd';

export default function IntervalInput({ value, onChange, unitOptions, parentName = 'interval' }) {
  const appliedUnitOptions = unitOptions || [
    {
      label: 'Seconds',
      value: 'seconds'
    },
    {
      label: 'Minutes',
      value: 'minutes'
    },
    {
      label: 'Hours',
      value: 'hours'
    },
    {
      label: 'Days',
      value: 'days'
    }
  ];

  return (
    <Input.Group compact>
      <Form.Item
        name={[...(Array.isArray(parentName) ? parentName : [parentName]), 'value']}
        rules={[{ required: true }]}
        style={{ display: 'inline-block', width: '60%' }}>
        <InputNumber
          placeholder={(value || {}).unit || 'Interval'}
          min={1}
          precision={0}
          style={{ borderTopRightRadius: 0, borderBottomRightRadius: 0, width: '100%' }}
          value={(value || {}).value}
          onChange={(e) => onChange({ ...(value || {}), value: e })}
        />
      </Form.Item>
      <Form.Item
        name={[...(Array.isArray(parentName) ? parentName : [parentName]), 'unit']}
        rules={[{ required: true }]}
        style={{ display: 'inline-block', width: '40%' }}>
        <Select
          options={appliedUnitOptions}
          value={(value || {}).unit}
          placeholder="Please Select"
          style={{ borderTopLeftRadius: 0, borderBottomLeftRadius: 0 }}
          onChange={(e) => onChange({ ...(value || {}), unit: e })}
        />
      </Form.Item>
    </Input.Group>
  );
}
