import { Tooltip } from 'antd';
import Moment from 'moment';

export default function VulnerabilityDot({ eolDate, vulnerability }) {
  let eolRender = null;
  let vulnerabilityRender = null;
  if (eolDate) {
    eolRender = (
      <Tooltip title={`EOL ${eolDate}`}>
        <div
          className={`dot ${Moment(eolDate).diff(Moment()) >= 0 ? 'dot-success' : 'dot-error'}`}
        />
      </Tooltip>
    );
  }
  if (vulnerability > 0) {
    vulnerabilityRender = (
      <Tooltip title={`Vulnerabilities ${vulnerability}`}>
        <div className={`dot ${'dot-error'}`} />
      </Tooltip>
    );
  } else {
    vulnerabilityRender = (
      <Tooltip title={`No Vulnerabilities found`}>
        <div className={`dot ${'dot-success'}`} />
      </Tooltip>
    );
  }
  return (
    <div className="flex items-center">
      {eolRender}
      {vulnerabilityRender}
    </div>
  );
}
