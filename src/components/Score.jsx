import { Tag, Progress } from 'antd';
import { severityColors } from '../design/theme';

export const VulnerabilityScore = ({ score, isPercentage = false }) => {
  if (!score || score === 'null') {
    return null;
  }
  let color = 'none';
  let computedScore = isPercentage ? score / 10 : score;
  if (computedScore < 4) {
    color = 'vulnerability-low';
  } else if (computedScore < 7) {
    color = 'vulnerability-medium';
  } else if (computedScore < 9) {
    color = 'vulnerability-high';
  } else if (computedScore >= 9) {
    color = 'vulnerability-critical';
  }
  return <div className={`vulnerability-score-card ${color}`}>{score}</div>;
};

export default function Score({
  value,
  category = 'cvss3',
  size = 'lg',
  useCircle = false,
  useLineProgress = false,
  allowZero = false
}) {
  if (!value && !allowZero) {
    return null;
  }
  if (!value) {
    value = 0;
  }
  const convertValueInPercent = () => {
    if (['cvss3', 'cvss2'].includes(category)) {
      return value * 10;
    }
    return value;
  };
  const severityType = {
    epss() {
      return { 0: '#AA4559', '100%': 'rgba(53, 132, 220, 0.40)' };
    },
    risk() {
      let severity = 'low';
      if (value > 80) {
        severity = 'critical';
      } else if (value > 50) {
        severity = 'high';
      } else if (value > 30) {
        severity = 'medium';
      } else {
        severity = 'low';
      }
      return severityColors[severity];
    },
    cvss3() {
      let severity = 'critical';
      if (value < 4) {
        severity = 'low';
      } else if (value < 7) {
        severity = 'medium';
      } else if (value < 9) {
        severity = 'high';
      }
      return severityColors[severity];
    },
    cvss2() {
      let severity = 'high';
      if (value < 4) {
        severity = 'low';
      } else if (value < 7) {
        severity = 'medium';
      }
      return severityColors[severity];
    }
  };

  const type = severityType[category]();

  const tagColorMap = {
    low: 'success',
    critical: 'error',
    high: 'orange',
    medium: 'cyan'
  };

  return useCircle ? (
    <Progress
      percent={value}
      status="normal"
      format={(p) => p}
      strokeColor={type}
      size={size}
      type="circle"
    />
  ) : useLineProgress ? (
    <Progress
      percent={convertValueInPercent()}
      format={(p) => p}
      showInfo={false}
      status="normal"
      strokeColor={type}
      size={size}
      type="line"
    />
  ) : (
    <Tag color={tagColorMap[type]} style={{ minWidth: '80px', textAlign: 'center' }}>
      <div
        className={`flex items-center rounded ${
          size === 'lg' ? 'text-base px-2 py-1' : ''
        } justify-center`}>
        {value}
      </div>
    </Tag>
  );
}
