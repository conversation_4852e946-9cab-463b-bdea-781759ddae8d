import generateId from '../utils/id';

export function HeatDot({
  tooltip,
  color,
  styleColor,
  itemStyle = { width: '1.2rem', height: '1.2rem' }
}) {
  return (
    <div
      style={{ ...itemStyle, ...(styleColor ? { background: styleColor } : {}) }}
      title={tooltip}
      className={`${color || ''} mr-1 mb-1 rounded`}></div>
  );
}

function HeatmapBox({ item, itemStyle }) {
  const pass_percent = Math.round((item.success * 100) / item.total);
  let color = 'bg-success';
  if (pass_percent > 100) {
    color = 'bg-success-700';
  } else if (pass_percent > 90) {
    color = 'bg-success-600';
  } else if (pass_percent > 80) {
    color = 'bg-success-500';
  } else if (pass_percent > 70) {
    color = 'bg-success-400';
  } else if (pass_percent > 60) {
    color = 'bg-warning-600';
  } else if (pass_percent > 50) {
    color = 'bg-warning-400';
  } else if (pass_percent > 40) {
    color = 'bg-danger-400';
  } else if (pass_percent > 30) {
    color = 'bg-danger-500';
  } else if (pass_percent > 20) {
    color = 'bg-danger-600';
  } else if (pass_percent > 10) {
    color = 'bg-danger-700';
  } else if (pass_percent >= 0) {
    color = 'bg-danger-800';
  }
  return (
    <HeatDot
      itemStyle={itemStyle}
      tooltip={decodeURI(
        `${item.name}%0A${encodeURI(
          `bindings:${item.bindings}`
        )}%0A${pass_percent.toFixed()}%25 endpoinds passed%0A${
          item.success
        } endpoints passed out of ${item.total}`
      )}
      color={color}
    />
  );
}

export default function Heatmap({ data, itemStyle = { width: '1.2rem', height: '1.2rem' } }) {
  return (
    <div className="flex flex-row flex-wrap mt-1 ml-1">
      {data.map((d) => (
        <HeatmapBox itemStyle={itemStyle} item={d} key={d.id || generateId()} />
      ))}
    </div>
  );
}
