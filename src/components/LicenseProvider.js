import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { getLicenseApi } from '../modules/settings/api/system-settings/license';
import Loading from './Loading';
import LicensedComponent from './LicensedComponent';

const LicenseContext = createContext();

const Provider = (props) => {
  const [license, setLicense] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    getLicenseApi().then((license) => {
      setLicense(license);
      if (props.onLicenseDayReceived) {
        props.onLicenseDayReceived(license.remainingDays);
      }
      setLoading(false);
    });
  }, [props]);

  // eslint-disable-next-line
  const value = useMemo(
    () => ({
      license,
      isEndpointOps() {
        return license.licenseProduct === LicensedComponent.EndpointOps;
      },
      isZiroXpose() {
        return license.licenseProduct === LicensedComponent.ZiroXpose;
      },
      isZiroPatch() {
        return license.licenseProduct === LicensedComponent.ZiroPatch;
      }
    }),
    [license]
  );

  return loading ? (
    <div className="flex-1 flex items-center justify-center h-full">
      <Loading />
    </div>
  ) : (
    <LicenseContext.Provider value={value}>{props.children}</LicenseContext.Provider>
  );
};

function contextHook() {
  // eslint-disable-next-line
  return useContext(LicenseContext);
}

export const License = {
  useLicense: contextHook,
  Provider: Provider
};
