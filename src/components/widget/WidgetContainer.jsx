// import Omit from 'lodash/omit';
import {
  transformTimelineForServer,
  transformWidgetForServer
} from '@/src/modules/dashboard/widget-api';
import SocketEventHandler from '../SocketEventHandler';
import Chart from './views/chart/Chart';
import WidgetGrid from './views/grid/WidgetGrid';
import GaugeView from './views/gauge/GaugeView';
import { useCallback } from 'react';
import VulnerabilityMatrix from './views/vulnerability-matrix/VulnerabilityMatrix';

export default function WidgetContainer({ widget, isPreview, ...props }) {
  let Component = () => (
    <div className="flex-1 flex items-center justify-center text-danger">
      Failed to Resolve component
    </div>
  );

  if (
    [
      'BarChart',
      'AreaChart',
      'LineChart',
      'PieChart',
      'StackedAreaChart',
      'StackedLineChart',
      'StackedBarChart',
      'Sankey'
    ].includes(widget.type)
  ) {
    Component = Chart;
  } else if (widget.type === 'Grid') {
    Component = WidgetGrid;
  } else if (widget.type === 'Gauge') {
    Component = GaugeView;
  } else if (widget.type === 'VulnerabilityMatrix') {
    Component = VulnerabilityMatrix;
  }

  const context = useCallback(
    () =>
      isPreview
        ? transformWidgetForServer({
            ...widget,
            ...(props.timeline ? { timeline: props.timeline } : {})
          })
        : {
            widgetId: widget.id,
            ...(props.timeline ? { timeline: transformTimelineForServer(props.timeline) } : {})
          },
    // eslint-disable-next-line
    [props.timeline, isPreview]
  );

  return (
    <SocketEventHandler event="analytics" context={context} {...props.socketEventHandlerProps}>
      {(payload) =>
        payload.error ? (
          <div className="flex flex-col justify-center items-center flex-1">
            <h2 className="text-danger text-center">{payload.error}</h2>
          </div>
        ) : (
          <Component data={payload} widget={widget} isPreview={isPreview} {...props} />
        )
      }
    </SocketEventHandler>
  );
}
