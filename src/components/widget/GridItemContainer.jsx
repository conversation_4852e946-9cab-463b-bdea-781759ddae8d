import { useEffect, useState } from 'react';
import { Spin } from 'antd';
import WidgetContainer from './WidgetContainer';
import { getWidgetApi } from '@/src/modules/dashboard/widget-api';
import Bus from '@/src/utils/emitter';
import GridItemTitle from './GridItemTitle';

export default function GridItemContainer({ widgetId, timeline, onRemove, onEdit }) {
  const [loading, setLoading] = useState(true);
  const [widget, setWidget] = useState({});

  function fetchWidget() {
    if (widgetId) {
      getWidgetApi(widgetId).then((widget) => {
        setWidget(widget);
        setLoading(false);
      });
    }
  }

  useEffect(() => {
    fetchWidget();
    // eslint-disable-next-line
  }, [widgetId]);

  function handleWidgetUpdate(response) {
    if (widgetId === response.id) {
      setWidget(response);
    }
  }

  useEffect(() => {
    Bus.on('widget:updated', handleWidgetUpdate);
    return () => Bus.off('widget:updated', handleWidgetUpdate);
    // eslint-disable-next-line
  }, []);

  return (
    <div className="flex flex-1 min-h-0 flex-col">
      {widget.type === 'Gauge' ? null : (
        <GridItemTitle onEdit={onEdit} onRemove={onRemove} widget={widget} />
      )}
      {loading ? (
        <div className="flex flex-1 items-center justify-center">
          <Spin spinning={true} />
        </div>
      ) : (
        <div
          className={`flex flex-1 min-h-0 flex-col ${
            widget.type === 'Gauge' ? '' : 'overflow-auto'
          }`}>
          <WidgetContainer
            timeline={timeline}
            widget={widget}
            onEdit={onEdit}
            onRemove={onRemove}
          />
        </div>
      )}
    </div>
  );
}
