import { Dropdown, Tooltip } from 'antd';
import { MoreOutlined, InfoCircleOutlined } from '@ant-design/icons';
import PermissionChecker from '../PermissionChecker';
import constants from '@/src/constants/index';
export default function GridItemTitle({ widget, onEdit, onRemove, isPreview }) {
  const items = [
    {
      key: 'edit',
      label: (
        <div className="cursor-pointer" onClick={onEdit}>
          Edit
        </div>
      )
    },
    {
      key: 'remove',
      label: (
        <div className="cursor-pointer" onClick={onRemove}>
          Remove
        </div>
      )
    }
  ];
  return (
    <div className="flex justify-between px-2 py-1">
      <div className="flex-1 min-w-0">{widget.name}</div>
      {isPreview ? null : (
        <Tooltip title={widget.description}>
          <InfoCircleOutlined />
        </Tooltip>
      )}
      {isPreview ? null : (
        <PermissionChecker permission={[constants.Update_Dashboard]} hasAny>
          <Dropdown
            menu={{
              items,
              onClick: (e) => {
                if (e.key === 'edit') {
                  onEdit();
                } else if (e.key === 'remove') {
                  onRemove();
                }
              }
            }}
            placement="bottomRight"
            trigger="click">
            <MoreOutlined />
          </Dropdown>
        </PermissionChecker>
      )}
    </div>
  );
}
