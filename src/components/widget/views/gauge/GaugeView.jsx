import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import GridItemTitle from '../../GridItemTitle';
import MetroTile from './MetroTile';

let navigations = {
  'total endpoints': `/inventory/endpoints`,
  'total windows endpoints': `/inventory/endpoints?platform=Windows`,
  'total apple mac endpoint': `/inventory/endpoints?platform=Mac`,
  'total linux endpoints': `/inventory/endpoints?platform=Linux`,
  'total network devices': `/inventory/network-devices`
};

export default function GaugeView({ widget, onRemove, onEdit, ...props }) {
  const [style, setStyle] = useState({});
  const navigate = useNavigate();
  const hasNavigation = Boolean(navigations[(widget.name || '').toLowerCase()]);
  function handleNavigation() {
    if (hasNavigation) {
      navigate(navigations[(widget.name || '').toLowerCase()]);
    }
  }
  return (
    <div className={`flex flex-col w-full flex-1 min-h-0 rounded`} style={style}>
      {props.forPrint ? null : (
        <GridItemTitle
          onEdit={onEdit}
          onRemove={onRemove}
          widget={widget}
          isPreview={props.isPreview}
        />
      )}
      <div
        className={`flex flex-col flex-1 min-h-0 ${hasNavigation ? 'cursor-pointer' : ''}`}
        onClick={props.isPreview ? undefined : handleNavigation}>
        <MetroTile widget={widget} onStyleReceived={setStyle} {...props} />
      </div>
    </div>
  );
}
