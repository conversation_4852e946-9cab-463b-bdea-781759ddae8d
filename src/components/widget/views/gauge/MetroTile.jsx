import { useEffect, useState } from 'react';
import { Textfit } from 'react-textfit';
import buildResult from '../result-builder';

export default function MetroTile({ widget, data, onStyleReceived }) {
  const [result, setResult] = useState({});

  useEffect(() => {
    const result = buildResult(data, widget);
    setResult(result);
    if (onStyleReceived) {
      onStyleReceived(result.style);
    }
  }, [widget, data, onStyleReceived]);

  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end'
  };

  const vAlignClasses = {
    top: 'items-start',
    center: 'items-center',
    bottom: 'items-end'
  };

  return (
    <div
      className="flex-1 w-full flex flex-col overflow-hidden min-h-0 rounded"
      style={{ ...result.style }}>
      {/* {title} */}
      <div className={`flex flex-1 metro-tile-widget-value px-2 h-full`}>
        <Textfit
          mode="single"
          forceSingleModeWidth={false}
          max={500}
          min={16}
          className={`flex flex-1 h-full w-full ${
            alignClasses[widget.widgetProperties?.align] || 'justify-center'
          } ${vAlignClasses[widget.widgetProperties?.valign] || 'items-center'}`}>
          {result.data}
          {widget.widgetProperties?.unit || widget.widgetProperties?.label}
        </Textfit>
      </div>
    </div>
  );
}
