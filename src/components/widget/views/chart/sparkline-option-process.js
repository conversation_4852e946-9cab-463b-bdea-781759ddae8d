import Merge from 'lodash/merge';
import Omit from 'lodash/omit';
import getChartCommonOptions from './chart-common-options';
import { buildSeries } from '../result-builder';
import TooltipBuilder from './tooltip-builder';

export default function processSparklineOptions(data, widget, options) {
  options = Merge(options, { dateFormat: options.dateFormat || 'ddd, MMM D,YYYY hh:mm a' });
  const isStacked = options.stacked;
  const dateTime = options.dateTime;
  const widgetProperties = widget.widgetProperties || {};

  const commonOptions = getChartCommonOptions(
    buildSeries(data, {
      type: widget.type,
      dateTime: dateTime,
      isStacked,
      widgetProperties
    }),
    widgetProperties,
    {
      hasDateTime: dateTime,
      timezone: options.timezone,
      dateFormat: options.dateFormat || 'ddd, MMM D,YYYY hh:mm a',
      serverSideZoom: options.serverSideZoom,
      unit: options.unit,
      sharedTooltip: !options.disableSharedTooltip,
      outsideTooltip: options.tooltipOutSide
    }
  );

  let appliedOptions = Merge(commonOptions, {
    grid: {
      left: 0,
      top: 0,
      right: 0,
      bottom: 0,
      containLabel: false
    },
    title: {
      text: ''
    },
    series: data.map((i) => ({
      ...i,
      pointStart: 1,
      color: options.color
    })),
    xAxis: {
      show: false,
      gridLineWidth: 0,
      labels: {
        enabled: false
      },
      title: {
        text: null
      },
      startOnTick: false,
      endOnTick: false,
      tickPositions: [],
      ...(options.categories && !dateTime
        ? {
            type: 'category',
            data: options.categories
          }
        : {
            type: 'time'
          })
    },
    yAxis: {
      show: false,
      endOnTick: false,
      startOnTick: false,
      gridLineWidth: 0,
      labels: {
        enabled: false
      },
      title: {
        text: null
      },
      tickPositions: [0],
      min: options.minValue
    },
    legend: {
      enabled: false
    },
    tooltip: {
      borderWidth: 1,
      borderColor: 'var(--border-color)',
      className: 'shadow-lg hc-tooltip-bg',
      animation: false,
      borderRadius: 10,
      ...(options.outsideTooltip
        ? {
            appendToBody: true
          }
        : {}),
      // valueFormatter: (value) =>
      //   `${value}${
      //     options.unit || uniqUnits.length > 0 ? ` ${uniqUnits[0]}` : undefined
      //   }`,
      ...(data.length > 0 && options.sharedTooltip ? { shared: true } : {}),
      ...(options.omitTooltipFormatter
        ? {}
        : {
            formatter: function (value) {
              var tooltip = new TooltipBuilder(data, options, value);
              return tooltip.html();
            }
          })
    }
  });

  if (!appliedOptions.dataZoom) {
    appliedOptions = Omit(appliedOptions, 'toolbox');
  }

  return appliedOptions;
}
