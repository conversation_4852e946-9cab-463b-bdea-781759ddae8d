import ReactEChartsCore from 'echarts-for-react/lib/core';
import * as echarts from 'echarts/core';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'echarts/charts';
// import components, all suffixed with Component
import {
  TitleComponent,
  Tooltip<PERSON>omponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  MarkLineComponent,
  DataZoomComponent,
  ToolboxComponent
} from 'echarts/components';
import {
  // CanvasRenderer
  SVGRenderer
} from 'echarts/renderers';

// Register the required components
echarts.use([
  // CanvasRenderer,
  DataZoomComponent,
  SVGRenderer,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>key<PERSON><PERSON>,
  TitleComponent,
  Tooltip<PERSON>omponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  MarkLineComponent,
  ToolboxComponent
]);

export default function ChartRender({ options, ...props }) {
  return (
    <ReactEChartsCore
      {...props}
      echarts={echarts}
      option={options}
      notMerge={true}
      lazyUpdate={true}
      theme={'dark'}
    />
  );
}
