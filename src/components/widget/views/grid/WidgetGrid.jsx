import { useEffect, useState } from 'react';
import { Table, Drawer } from 'antd';
import Omit from 'lodash/omit';
import buildResult, { buildColumns } from '../result-builder';
import SeverityBorder from '@/src/components/SeverityBorder';
import VulnerabilityDetail from '@/src/modules/vulnerability/components/VulnerabilityDetail';
import { AffectedEndpointsDrawer } from '@/src/modules/vulnerability/components/AffectedEndpointsDrawer';

export default function WidgetGrid({ widget, data, onDataReceived, isPreview, isReport }) {
  const [rows, setRows] = useState(null);
  const [columns, setColumns] = useState(null);
  const [showingCVEDetailsFor, setShowingCVEDetailsFor] = useState(null);
  const [showingAffectedEndpointsForCVE, setAffectedEndpointsForCVE] = useState(null);
  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 20,
      showSizeChanger: true,
      showTotal: (total, range) => `showing ${range[0]}-${range[1]} of ${total} items`
    }
  });

  useEffect(() => {
    buildResultForDisplay();
    // eslint-disable-next-line
  }, [data]);

  useEffect(() => {
    buildColumnsForDisplay();
    buildResultForDisplay();
    // eslint-disable-next-line
  }, [widget, data]);

  function buildResultForDisplay() {
    const result = buildResult(data, widget);
    setRows(result.data);
    setTableParams((prev) => ({
      ...prev,
      pagination: { ...prev.pagination, total: result.data.length }
    }));
    setColumns(result.columns);
    if (onDataReceived) {
      onDataReceived(result);
    }
  }

  function buildColumnsForDisplay() {
    const columns = buildColumns(data, widget, {
      setCVEDetail: setShowingCVEDetailsFor,
      setAffectedEndpointsCVE: setAffectedEndpointsForCVE
    });
    setColumns(columns);
  }
  const handleTableChange = (pagination, filters, sorter) => {
    setTableParams({
      ...tableParams,
      pagination: {
        ...tableParams.pagination,
        ...pagination
      },
      filters,
      sorter
    });
  };

  return (
    rows && (
      <div className="flex flex-1 min-h-0 flex-col">
        <Table
          className="crud-table"
          size="small"
          columns={columns
            .filter((c) => c.hidden !== true)
            .map((c) => ({
              ...c,
              onCell(record) {
                if (record[`${c.key}_style`]) {
                  return { style: Omit(record[`${c.key}_style`], 'type') };
                }
                return {};
              }
            }))}
          rowKey={(record) => record.guid}
          dataSource={rows}
          onChange={handleTableChange}
          {...(isReport ? { pagination: false } : tableParams)}
        />
        {showingCVEDetailsFor ? (
          <Drawer
            width="95%"
            onClose={() => setShowingCVEDetailsFor(null)}
            destroyOnClose
            maskClosable={false}
            open={true}
            title={
              <SeverityBorder severity={showingCVEDetailsFor.severity}>
                <div className="flex justify-between">
                  <div>{showingCVEDetailsFor.cve}</div>
                </div>
              </SeverityBorder>
            }>
            <VulnerabilityDetail
              onReceived={(v) => setShowingCVEDetailsFor(v)}
              vulnerability={showingCVEDetailsFor}
            />
          </Drawer>
        ) : null}
        {showingAffectedEndpointsForCVE ? (
          <AffectedEndpointsDrawer
            currentCategory={'software'}
            onClose={() => setAffectedEndpointsForCVE(null)}
            vulnerability={{ ...showingAffectedEndpointsForCVE, softwareId: -1 }}
          />
        ) : null}
      </div>
    )
  );
}
