import { useEffect, useState } from 'react';
import buildResult from '../result-builder';
import VulnerabilityMatrixRenderer from './VulnerabilityMatrixRenderer';

export default function VulnerabilityMatrix({ data, widget }) {
  const [result, setResult] = useState(null);

  useEffect(() => {
    const result = buildResult(data, widget);
    setResult(result);
  }, [widget, data]);

  return result && <VulnerabilityMatrixRenderer data={result} widget={widget} />;
}
