import { Row, Col } from 'antd';
import { Textfit } from 'react-textfit';

export default function VulnerabilityMatrixRenderer({ data, widget }) {
  const severityOrder = ['Critical', 'High', 'Medium', 'Low'];

  return (
    <div className="flex flex-1 flex-col min-h-0 h-full">
      <Row className="flex-shrink-0">
        <Col span={widget?.widgetProperties?.labelSpan || 4}></Col>
        <Col span={24 - (widget?.widgetProperties?.labelSpan || 4)}>
          <Row>
            {severityOrder.map((s) => (
              <Col key={s} span={6} className="text-center  h-8">
                <Textfit
                  mode="single"
                  forceSingleModeWidth={false}
                  max={16}
                  min={12}
                  className={`flex flex-1 h-full w-full justify-center items-center`}>
                  {s}
                </Textfit>
              </Col>
            ))}
          </Row>
        </Col>
      </Row>
      <div className="flex-1 min-h-0 flex flex-col">
        {Object.keys(data).map((key) => (
          <div className="flex-1 min-h-0 flex flex-col" key={key}>
            <Row className="h-full">
              <Col span={widget?.widgetProperties?.labelSpan || 4} className="text-right pr-2">
                <div className="flex flex-1 h-full justify-end items-center ">
                  <Textfit
                    mode="single"
                    forceSingleModeWidth={false}
                    max={16}
                    min={12}
                    className={`flex flex-1 h-full w-full justify-end items-center`}>
                    {key}
                  </Textfit>
                </div>
              </Col>
              <Col span={24 - (widget?.widgetProperties?.labelSpan || 4)} className="h-full">
                <Row className="h-full">
                  {severityOrder.map((severity) => (
                    <Col
                      key={severity}
                      span={6}
                      className="mb-[2px] px-[1px]"
                      style={{ height: `calc(100% - 2px)` }}>
                      <div
                        className={`${severity.toLowerCase()}  bg w-full inline-flex items-center justify-center h-full`}>
                        <Textfit
                          mode="single"
                          forceSingleModeWidth={false}
                          max={16}
                          min={12}
                          className={`flex flex-1 h-full w-full justify-center items-center`}>
                          {data[key][severity] || 0}
                        </Textfit>
                      </div>
                    </Col>
                  ))}
                </Row>
              </Col>
            </Row>
          </div>
        ))}
      </div>
    </div>
  );
}
