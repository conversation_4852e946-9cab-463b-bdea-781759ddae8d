import classnames from 'classnames';

export default function LiveStatus({ status, children, fillBg = false }) {
  const dot = (
    <div
      className={classnames([
        { 'mr-1': !fillBg, 'ml-1': fillBg },
        {
          'bg-success': ['online', 'active'].includes(status.toLowerCase()),
          'bg-danger': !['online', 'active'].includes(status.toLowerCase())
        }
      ])}
      style={{ height: '13px', width: '13px', borderRadius: '50%' }}></div>
  );
  return (
    <div
      className="inline-flex items-center rounded-lg"
      style={
        fillBg
          ? {
              padding: '4px 8px',
              background: ['online', 'active'].includes(status.toLowerCase())
                ? `var(--severity-up-lighter)`
                : `var(--severity-critical-lighter)`
            }
          : {}
      }>
      {fillBg ? null : dot}
      {children || <span className="font-semibold text-sm">{status}</span>}
      {fillBg ? dot : null}
    </div>
  );
}
