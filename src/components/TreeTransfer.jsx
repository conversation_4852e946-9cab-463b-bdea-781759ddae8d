import { theme, Transfer, Tree } from 'antd';

export default function TreeTransfer({ dataSource, targetKeys, ...restProps }) {
  const { token } = theme.useToken();

  const transferDataSource = [];
  function flatten(list = []) {
    list.forEach((item) => {
      transferDataSource.push(item);
      flatten(item.children);
    });
  }
  flatten(dataSource);

  const generateTree = (treeNodes = [], checkedKeys = []) =>
    treeNodes.map(({ children, ...props }) => ({
      ...props,
      disabled: checkedKeys.includes(props.key),
      checkable: true,
      children: generateTree(children, checkedKeys)
    }));

  // const isChecked = (selectedKeys, eventKey) => selectedKeys.includes(eventKey);
  return (
    <Transfer
      {...restProps}
      targetKeys={targetKeys}
      dataSource={transferDataSource}
      className="tree-transfer"
      render={(item) => item.label}
      showSelectAll={true}>
      {({ direction, onItemSelectAll, selectedKeys }) => {
        if (direction === 'left') {
          const checkedKeys = [...selectedKeys, ...(targetKeys || [])];
          return (
            <div style={{ padding: token.paddingXS }}>
              <Tree
                blockNode
                checkable
                fieldNames={{
                  title: 'label',
                  children: 'children',
                  key: 'key'
                }}
                defaultExpandAll
                checkedKeys={checkedKeys}
                treeData={generateTree(dataSource, targetKeys)}
                onCheck={(checked, { node: { key } }) => {
                  if (/^[a-zA-Z]+$/.test(key)) {
                    onItemSelectAll(
                      dataSource.find((i) => i.key === key).children.map(({ key }) => key),
                      checked.includes(key)
                    );
                  } else {
                    onItemSelectAll(
                      [key],
                      checked.length ? checked.includes(key) : !checkedKeys.includes(key)
                    );
                  }
                }}
                onSelect={(checked, { node: { key } }) => {
                  if (/^[a-zA-Z]+$/.test(key)) {
                    onItemSelectAll(
                      dataSource.find((i) => i.key === key).children.map(({ key }) => key),
                      checked.includes(key)
                    );
                  } else {
                    onItemSelectAll(
                      [key],
                      checked.length ? checked.includes(key) : !checkedKeys.includes(key)
                    );
                  }
                }}
              />
            </div>
          );
        }
      }}
    </Transfer>
  );
}
