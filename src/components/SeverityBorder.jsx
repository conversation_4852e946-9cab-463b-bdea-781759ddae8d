export default function SeverityBorder({ severity, children, color }) {
  const colorMap = {
    low: 'border-success',
    critical: 'border-danger',
    high: 'border-warning',
    medium: 'border-yellow'
  };

  return (
    <div
      className={`flex flex-col border border-solid border-l-4 pl-2 border-r-0 border-t-0 border-b-0 ${
        colorMap[color || (severity || '').toLowerCase()]
      }`}>
      {children}
    </div>
  );
}
