import Icon from './Icon';

export default function InfoTable({ data, icon }) {
  return (
    <table className="info-table">
      <colgroup>
        <col style={{ width: '50%' }} />
        <col style={{ width: '50%' }} />
      </colgroup>
      <tbody>
        {Object.keys(data).map((key) => (
          <tr key={key}>
            <td>
              {icon && <Icon name={data[key].icon || icon} className="mr-1 text-primary" />} {key}
            </td>
            <td>{data[key].value}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
