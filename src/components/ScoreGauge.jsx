// import GaugeComponent from 'react-gauge-component';
import { Progress } from 'antd';

export default function ScoreGauge({ value, useReverseColor, size, ...props }) {
  const colors = ['#c84235', '#f58518', '#f5bc18', '#89c540'];
  // const reverseColors = { '30%': '#89c540', '50%': '#f5bc18', '80%': '#f58518', '100%': '#c84235' };

  let index = 0;

  if (value > 80) {
    index = 3;
  } else if (value > 50) {
    index = 2;
  } else if (value > 30) {
    index = 1;
  } else {
    index = 0;
  }

  let color = (useReverseColor ? colors.reverse() : colors)[index];

  return (
    <Progress
      type="dashboard"
      steps={8}
      format={(p) => p}
      strokeWidth={15}
      strokeColor={color}
      trailColor="var(--gauge-trail-color)"
      size={size}
      percent={value}
    />
  );

  // return (
  //   <GaugeComponent
  //     {...props}
  //     value={value}
  //     type="radial"
  //     labels={{
  //       valueLabel: {
  //         formatTextValue(v) {
  //           return v;
  //         }
  //       },
  //       tickLabels: {
  //         hideMinMax: true
  //       }
  //     }}
  //     arc={{
  //       colorArray: useReverseColor ? colors.reverse() : colors,
  //       subArcs: [{ limit: 30 }, { limit: 50 }, { limit: 80 }, { limit: 100 }],
  //       padding: 0.03,
  //       // cornerRadius: 0,
  //       width: 0.3
  //     }}
  //     pointer={{
  //       elastic: true,
  //       animationDelay: 0
  //     }}
  //   />
  // );
}
