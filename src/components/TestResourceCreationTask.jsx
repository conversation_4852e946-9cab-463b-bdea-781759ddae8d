import { Row, Col, <PERSON>, But<PERSON>, Drawer, Tag } from 'antd';
import { Asset } from './pickers/AssetPicker';
import Capitalize from 'lodash/capitalize';
import { createContext, useContext, useMemo, useState } from 'react';
import { getTaskContextApi } from '../modules/compliance/api/compliance-rules';
import { useLayout } from '../layouts/Layout';
import Icon from './Icon';

const TestResourceCreationContext = createContext();

export const TestResourceCreationProvider = ({ children, testApiFn }) => {
  const form = Form.useFormInstance();
  const { message } = useLayout();

  const [testContext, setTestContext] = useState(null);
  const [isTesting, setTsTesting] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const tagColorMap = {
    waiting: 'warning',
    initiated: 'processing',
    in_progress: 'processing',
    failed: 'error',
    cancelled: 'error',
    success: 'success'
  };

  function refreshTask() {
    if (testContext && testContext.id) {
      getTaskContextApi(testContext.id).then((context) => {
        setTestContext(context);
      });
    }
  }

  function triggerTest() {
    if (
      form.getFieldValue('test_asset') === undefined ||
      form.getFieldValue('test_asset') === null
    ) {
      return message.error('Please Select Asset');
    }
    setTestContext(null);
    setTsTesting(true);
    testApiFn(form.getFieldsValue()).then((context) => {
      setTestContext(context);
      setTsTesting(false);
    });
  }

  function result() {
    return (
      <>
        {testContext ? (
          <Row>
            <Col span={24} className="flex mt-4">
              <Row gutter={32}>
                <Col span={6}>RESULT</Col>
                <Col span={10}>
                  <Tag
                    color={tagColorMap[testContext?.taskStatus?.toLowerCase()]}
                    className="inline-flex items-center justify-center"
                    style={{
                      textAlign: 'center',
                      textTransform: 'uppercase',
                      minWidth: '80px'
                    }}>
                    {Capitalize((testContext?.taskStatus || '').toLowerCase()).replaceAll('_', ' ')}
                  </Tag>
                </Col>
                <Col span={2}>
                  <Button
                    loading={isTesting}
                    shape="circle"
                    type="link"
                    className="ml-2"
                    title="View Test Result"
                    onClick={() => setIsDrawerOpen(true)}>
                    <Icon name="info-circle" className="text-primaray text-lg cursor-pointer" />
                  </Button>
                </Col>
                <Col span={4}>
                  <Button
                    className="ml-2"
                    title="Refresh Test Result"
                    onClick={() => refreshTask()}>
                    Refresh Test Result
                  </Button>
                </Col>
              </Row>
            </Col>
          </Row>
        ) : null}
        <Drawer
          title="View Test Result "
          placement={'right'}
          onClose={() => {
            setIsDrawerOpen(false);
          }}
          destroyOnClose
          width="70%"
          maskClosable={false}
          open={isDrawerOpen}>
          <div className="py-4">
            <div style={{ whiteSpace: 'pre-line' }}>{testContext?.result}</div>
          </div>
        </Drawer>
      </>
    );
  }

  function testControl() {
    return (
      <Row>
        <Col span={20}>
          <div className="">
            <Form.Item name="test_asset" label="Test Endpoint">
              <Asset.Picker />
            </Form.Item>
          </div>
        </Col>
        <Col span={4} className="flex items-center justify-start pl-1">
          <Button
            loading={isTesting}
            type="primary"
            shape="round"
            className="ml-2"
            title="Test"
            onClick={() => triggerTest()}>
            <Icon name="play" />
          </Button>
        </Col>
      </Row>
    );
  }

  const value = useMemo(
    () => ({
      triggerTest,
      testContext,
      result,
      testControl
    }),
    // eslint-disable-next-line
    [testContext, isDrawerOpen]
  );
  return (
    <TestResourceCreationContext.Provider value={value}>
      {children}
    </TestResourceCreationContext.Provider>
  );
};

export function useTestResourceCreation() {
  return useContext(TestResourceCreationContext);
}
