import { InboxOutlined, UploadOutlined } from '@ant-design/icons';
import { Upload, Button, message as MessageToast } from 'antd';
import api from '@api';
import { useAuth } from '../hooks/auth';

export const ALLOWED_SCRIPT_EXTENSIONS = [
  'sh',
  'ps1',
  'bat',
  'vbs',
  'reg',
  'vbe',
  'wsf',
  'wsc',
  'ps1xml',
  'psm1',
  'cab'
];

export const ALLOWED_PACKAGE_EXTENSIONS = ['exe', 'msi', 'zip', 'cab', 'pkg', 'dmg', 'deb', 'rpm'];

export default function PackageUploader({
  children,
  onChange,
  value,
  message,
  onUploaded,
  accept,
  useDragger = false,
  multiple = false,
  extensions = [],
  ...props
}) {
  const { token } = useAuth();
  const uploaderProps = {
    accept,
    multiple,
    fileList: (value || []).map((v) => ({
      ...v,
      ...(v.url
        ? {
            url: `${v.url}${token.access_token}`
          }
        : v.response
        ? {
            url: v.response.url
          }
        : {})
    })),
    beforeUpload(file) {
      if (extensions && extensions.length && !extensions.includes(file.name.split('.').pop())) {
        MessageToast.error(`You can only upload ${extensions.join(', ')} file!`);
        return Upload.LIST_IGNORE;
      } else if (accept && accept.indexOf('image') >= 0) {
        const isJpgOrPng =
          file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png';
        if (!isJpgOrPng) {
          MessageToast.error('You can only upload JPG/PNG file!');
          return Upload.LIST_IGNORE;
        }
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isLt2M) {
          MessageToast.error('Image must smaller than 2MB!');
          return Upload.LIST_IGNORE;
        }
      } else if (accept && accept !== file.type) {
        MessageToast.error(`You can only upload ${accept} type of file!`);
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    customRequest({ onProgress, file, onSuccess }) {
      const headers = api.getHeaders();
      const formData = new FormData();
      formData.append('file', file);
      return api
        .getNewClient(
          `${process.env.REACT_APP_API_URL}${process.env.REACT_APP_API_BASE_PATH}/patch`,
          { timeout: 0 }
        )
        .post(`${props.url || '/upload'}`, formData, {
          onUploadProgress: (event) => {
            onProgress({ percent: (event.loaded / event.total) * 100 });
          },
          headers: {
            Authorization: headers.common.Authorization,
            'Content-Type': 'multipart/form-data'
          }
        })
        .then(({ data }) => {
          const obj = {
            ref: data.refName,
            name: data.realName,
            url: `/api/patch/download/${data.refName}?mid=${token.access_token}`,
            status: 'done'
          };

          onSuccess(obj);

          if (onUploaded) {
            onUploaded(obj);
          }
        });
    },
    onChange(data) {
      if (!multiple) {
        for (let i = 0; i < data.fileList.length - 1; i++) {
          data.fileList.splice(i, 1);
        }
      }
      if (onChange) {
        onChange(data.fileList);
      }
    }
  };

  return useDragger ? (
    <Upload.Dragger {...uploaderProps} {...props}>
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">Click or drag file to this area to upload</p>
      <p className="ant-upload-hint">
        {message} {message ? <br /> : null} Support for a {multiple ? 'File' : 'Single File'}{' '}
        upload.
      </p>
    </Upload.Dragger>
  ) : (
    <Upload {...uploaderProps} {...(multiple ? {} : { maxCount: 1 })} {...props}>
      {children || <Button icon={<UploadOutlined />}>Upload {multiple ? '' : '(Max: 1)'}</Button>}
    </Upload>
  );
}
