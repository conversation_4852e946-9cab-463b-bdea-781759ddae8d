import { getInventoryPackageByIdApi } from '@/src/modules/inventory/api/hosts.js';
import Picker from './Picker.jsx';
import buildPicker from './pickerBuilder';
import { getEligibleSoftwaresApi } from '@/src/modules/inventory/api/software-metering.js';
import { useEffect } from 'react';

const { Provider, contextHook } = buildPicker(
  (offset, limit, filters, props) => getEligibleSoftwaresApi(offset, limit, filters, props.scope),
  SoftwarePicker,
  (i) => ({
    value: i.id,
    label: i.name,
    version: i.version,
    vendor: i.vendor
  }),
  getInventoryPackageByIdApi
);

function SoftwarePicker({ value, onChange, ...props }) {
  const { filteredOptions, allOptions, search, searchTerm, getOptionById } = contextHook();
  useEffect(() => {
    if (value) {
      getOptionById(value);
    }
    // eslint-disable-next-line
  }, []);
  if (props.disabled && props.textOnly) {
    return allOptions.has(value) ? allOptions.get(value).label : null;
  }
  return (
    <Picker
      {...props}
      style={{ width: '100%' }}
      placeholder="Please Select"
      value={value}
      searchValue={searchTerm}
      onChange={onChange}
      onSearch={search}
      options={filteredOptions || allOptions}
    />
  );
}

export const Software = {
  useSoftware: contextHook,
  Provider: Provider,
  Picker: SoftwarePicker
};
