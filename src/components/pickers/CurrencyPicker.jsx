import { getAllCurrencyApi } from '@/src/modules/settings/api/system-settings/currency.js';
import Picker from './Picker.jsx';
import buildPicker from './pickerBuilder';

const { Provider, contextHook } = buildPicker(getAllCurrencyApi, CurrencyPicker, (i) => ({
  value: i.name,
  label: i.name,
  exchange_rate: i.exchange_rate
}));

function CurrencyPicker({ value, onChange, ...props }) {
  const { filteredOptions, allOptions, search } = contextHook();
  if (props.disabled && props.textOnly) {
    return allOptions.has(value) ? allOptions.get(value).label : null;
  }
  return (
    <Picker
      {...props}
      style={{ width: '100%' }}
      placeholder="Please Select"
      value={value}
      onChange={onChange}
      onSearch={search}
      options={filteredOptions || allOptions}
    />
  );
}

export const Currency = {
  useCurrencys: contextHook,
  Provider: Provider,
  Picker: <PERSON>urrencyPicker
};
