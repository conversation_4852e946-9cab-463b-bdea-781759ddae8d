import Picker from './Picker.jsx';
import buildPicker from './pickerBuilder';

const { Provider, contextHook } = buildPicker(
  () =>
    Promise.resolve({
      result: [
        {
          id: 1,
          name: 'Critical'
        },
        {
          id: 2,
          name: 'High'
        },
        {
          id: 3,
          name: 'Medium'
        },
        {
          id: 4,
          name: 'Low'
        }
      ]
    }),
  SeverityPicker
);

function SeverityPicker({ value, onChange, ...props }) {
  const { filteredOptions, allOptions, search } = contextHook();
  if (props.disabled && props.textOnly) {
    return allOptions.has(value) ? allOptions.get(value).label : null;
  }
  return (
    <Picker
      {...props}
      style={{ width: '100%' }}
      placeholder="Please Select"
      value={value}
      onChange={onChange}
      onSearch={search}
      options={filteredOptions || allOptions}
    />
  );
}

export const Severity = {
  useLocations: contextHook,
  Provider: Provider,
  Picker: SeverityPicker
};
