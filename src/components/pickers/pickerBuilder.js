import { useAuth } from '@/src/hooks/auth';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';

export default function buildPickerContext(apiFn, PickerComponent, transformer, getByIdFn) {
  const Context = createContext();

  function contextHook() {
    // eslint-disable-next-line
    return useContext(Context);
  }

  let searchTimeout = null;

  const Provider = (props) => {
    const [options, setOptions] = useState(new Map());
    const [searchTerm, setSearchTerm] = useState(null);
    const { token } = useAuth();
    let fetchedCounts = 1;

    useEffect(() => {
      if (!props.refetchOnSearch && fetchedCounts > 1) {
        return;
      }
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
      searchTimeout = setTimeout(
        () => {
          (props.apiFn || apiFn)(0, props.maxRecords || 1000, { searchTerm }, props).then(
            ({ result }) => {
              fetchedCounts++;
              const m = new Map();
              result.forEach((i) => {
                if (transformer) {
                  m.set(i.id, transformer(i, { token }));
                } else {
                  m.set(i.id, { value: i.id, label: i.name });
                }
              });
              setOptions(m);
              searchTimeout = null;
            }
          );
        },
        props.refetchOnSearch ? 700 : 0
      );
      // eslint-disable-next-line
    }, [token, props, searchTerm]);

    function getOptionById(id) {
      if (options.has(id)) {
        return Promise.resolve(options.get(id));
      }
      return getByIdFn
        ? getByIdFn(id).then((item) => {
            const transformed = transformer ? transformer(item, { token }) : item;
            setOptions((prev) => {
              const newMap = new Map(prev);
              newMap.set(id, transformed);
              return newMap;
            });
            return transformed;
          })
        : Promise.resolve(null);
    }

    const value = useMemo(
      () => ({
        ...(searchTerm
          ? {
              filteredOptions: Array.from(options.values()).filter(
                (i) =>
                  (i.filterLabel || i.label).toLowerCase().indexOf(searchTerm.toLowerCase()) >= 0
              )
            }
          : {}),
        searchTerm,
        allOptions: options,
        getOptionById,
        search: setSearchTerm
      }),
      // eslint-disable-next-line
      [options, searchTerm]
    );

    return (
      <Context.Provider value={value}>
        {props.children || <PickerComponent {...props} />}
      </Context.Provider>
    );
  };

  return { Provider, contextHook };
}
