import Picker from './Picker.jsx';
import buildPicker from './pickerBuilder';
import Api from '@api';

const { Provider, contextHook } = buildPicker(
  () =>
    Api.post(`/patch/patch-language/search`).then(({ result }) => ({
      result
    })),
  PatchLanguagePicker,
  (i) => ({
    value: i.id,
    filterLabel: `${i.englishName}`,
    label: i.englishName
  })
);

function PatchLanguagePicker({ value, onChange, linkToAsset, resolveId, ...props }) {
  const { filteredOptions, allOptions, search } = contextHook();

  return (
    <Picker
      {...props}
      style={{ width: '100%' }}
      placeholder="Please Select"
      value={value}
      onChange={onChange}
      onSearch={search}
      options={filteredOptions || allOptions}
    />
  );
}

export const PatchLanguage = {
  useAssets: contextHook,
  Provider: Provider,
  Picker: PatchLanguagePicker
};
