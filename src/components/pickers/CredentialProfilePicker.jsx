import { getAllCredentialProfilesApi } from '@/src/modules/settings/api/inventory-profile/credential-profile.js';
import Picker from './Picker.jsx';
import buildPicker from './pickerBuilder';

const { Provider, contextHook } = buildPicker(
  getAllCredentialProfilesApi,
  CredentialProfilePicker,
  (item) => ({
    value: item.id,
    label: item.name
  })
);

function CredentialProfilePicker({ value, onChange, ...props }) {
  const { filteredOptions, allOptions, search } = contextHook();
  if (props.disabled && props.textOnly) {
    return allOptions.has(value) ? allOptions.get(value).label : null;
  }
  return (
    <Picker
      {...props}
      style={{ width: '100%' }}
      placeholder="Please Select"
      value={value}
      onChange={onChange}
      onSearch={search}
      options={
        props.organization !== undefined
          ? (filteredOptions || Array.from(allOptions.values())).filter(
              ({ organization }) => organization === props.organization
            )
          : props.hideWithoutOrganization
          ? []
          : filteredOptions || allOptions
      }
    />
  );
}

export const CredentialProfile = {
  useDepartments: contextHook,
  Provider: Provider,
  Picker: CredentialProfilePicker
};
