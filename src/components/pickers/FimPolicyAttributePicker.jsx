import Picker from './Picker.jsx';
import buildPicker from './pickerBuilder';
import { getFimPolicyAttributesApi } from '@/src/modules/settings/api/policy-management/policies.js';

const { Provider, contextHook } = buildPicker(getFimPolicyAttributesApi, FimPolicyAttributePicker);

function FimPolicyAttributePicker({ value, onChange, ...props }) {
  const { filteredOptions, allOptions, search } = contextHook();
  if (props.disabled && props.textOnly) {
    return allOptions.has(value) ? allOptions.get(value).label : null;
  }
  return (
    <Picker
      {...props}
      style={{ width: '100%' }}
      placeholder="Please Select"
      value={value}
      onChange={onChange}
      onSearch={search}
      options={filteredOptions || allOptions}
    />
  );
}

export const FimPolicyAttributes = {
  Provider: Provider,
  Picker: FimPolicyAttributePicker,
  contextHook
};
