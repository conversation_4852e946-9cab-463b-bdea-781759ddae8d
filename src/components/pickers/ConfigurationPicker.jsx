import { Row, Col, Transfer } from 'antd';
import buildPicker from './pickerBuilder';
import Icon from '../Icon.jsx';
import {
  getRemediationConfigurationsApi,
  getAllConfigurationsApi
} from '@/src/modules/device-automation/configurations.js';

const { Provider, contextHook } = buildPicker(
  (offset, limit, sortFilters, props) =>
    (props.excludeRemediation ? getAllConfigurationsApi : getRemediationConfigurationsApi)(
      offset,
      limit,
      sortFilters
    ),
  ConfigurationPicker,
  (i, options) => ({
    key: i.id,
    os: i.os,
    title: `${i.name} - ${i.displayName}`,
    nodeRenderer: (
      <Row gutter={16} wrap={false} align="middle">
        <Col flex="none" className="items-center inline-flex">
          <Icon name={`platform_${i.os}`} className="text-base" />
          <span className="ml-1">({i.arch})</span>
        </Col>
        <Col flex="auto">
          {i.name}: {i.displayName}
        </Col>
      </Row>
    )
  })
);

function ConfigurationPicker({ target, ...props }) {
  const { filteredOptions, allOptions } = contextHook();

  let options = filteredOptions || allOptions;

  if (target === 4) {
    options = Array.isArray(options)
      ? options
      : Array.from(options.values()).filter((i) => i.os.toLowerCase() === 'windows');
  }
  if (target === 5) {
    options = Array.isArray(options)
      ? options
      : Array.from(options.values()).filter((i) => i.os.toLowerCase() === 'linux');
  }
  if (target === 6) {
    options = Array.isArray(options)
      ? options
      : Array.from(options.values()).filter((i) => i.os.toLowerCase() === 'mac');
  }

  return (
    <Transfer
      {...props}
      showSearch
      filterOption={(searchTerm, node) => {
        return node.title.toLowerCase().indexOf(searchTerm.toLowerCase()) >= 0;
      }}
      render={(i) => i.nodeRenderer}
      dataSource={Array.isArray(options) ? options : Array.from(options.values())}
      listStyle={{
        width: '45%',
        height: 350
      }}
    />
  );
}

export const Configuration = {
  useUsers: contextHook,
  Provider: Provider,
  Picker: ConfigurationPicker
};
