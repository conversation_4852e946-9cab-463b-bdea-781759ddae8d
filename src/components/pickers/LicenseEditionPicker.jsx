import { getAllLicenseEditionApi } from '@/src/modules/settings/api/system-settings/license-edition.js';
import Picker from './Picker.jsx';
import buildPicker from './pickerBuilder';

const { Provider, contextHook } = buildPicker(
  getAllLicenseEditionApi,
  LicenseEditionPicker,
  (i) => ({
    value: i.id,
    label: i.license_edition,
    license_subscription_type: i.license_subscription_type
  })
);

function LicenseEditionPicker({ value, onChange, emitFullObject, ...props }) {
  const { filteredOptions, allOptions, search } = contextHook();
  const handleChange = (selectedValue) => {
    const selectedOption = allOptions.get(selectedValue);
    if (emitFullObject) {
      onChange(selectedOption); // Emit the whole object if the prop is true
    } else {
      onChange(selectedValue); // Emit only the value if the prop is false
    }
  };

  if (props.disabled && props.textOnly) {
    return allOptions.has(value) ? allOptions.get(value).label : null;
  }
  return (
    <Picker
      {...props}
      style={{ width: '100%' }}
      placeholder="Please Select"
      value={value}
      onChange={handleChange}
      onSearch={search}
      options={filteredOptions || allOptions}
    />
  );
}

export const LicenseEdition = {
  useLicenseEdition: contextHook,
  Provider: Provider,
  Picker: LicenseEditionPicker
};
