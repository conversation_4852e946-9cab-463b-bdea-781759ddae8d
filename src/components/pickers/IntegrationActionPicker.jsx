import SystemLogo from '../SystemLogo.jsx';
import Picker from './Picker.jsx';
import buildPicker from './pickerBuilder';
import { getAllActionsApi } from '@/src/modules/settings/api/integrations/actions.js';

const { Provider, contextHook } = buildPicker(
  getAllActionsApi,
  IntegrationActionPicker,
  (i, options) => ({
    value: i.id,
    type: i.type,
    label: (
      <div className="flex items-center">
        <SystemLogo
          className="mr-2"
          style={{ width: '20px' }}
          name={i.context?.incidentType || i.context?.threat_intelligence_type || 'Email'}
          type="integration"
        />
        {i.name}
      </div>
    )
  })
);

function IntegrationActionPicker({
  value,
  onChange,
  excludedOptions = [],
  includedTypes = [],
  ...props
}) {
  const { filteredOptions, allOptions, search } = contextHook();

  // Filter out the excluded values from allOptions
  let filteredAllOptions = Array.from(allOptions.entries()).filter(
    ([key, option]) => !excludedOptions.includes(option.type)
  );
  if (includedTypes.length) {
    filteredAllOptions = filteredAllOptions.filter(([key, option]) =>
      includedTypes.includes(option.type)
    );
  }
  const filteredOptionsMap = new Map(filteredAllOptions);

  if (props.disabled && props.textOnly) {
    return allOptions.has(value) ? allOptions.get(value).label : null;
  }
  return (
    <Picker
      {...props}
      style={{ width: '100%' }}
      className="package-picker"
      placeholder="Please Select"
      value={value}
      onChange={onChange}
      onSearch={search}
      options={filteredOptions || filteredOptionsMap}
    />
  );
}

export const IntegrationAction = {
  useLocations: contextHook,
  Provider: Provider,
  Picker: IntegrationActionPicker
};
