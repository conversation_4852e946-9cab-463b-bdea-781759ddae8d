import { Row, Col, Form, Select, Input, TimePicker } from 'antd';
import LRange from 'lodash/range';
import Moment from 'moment';

export default function SchedulePicker({
  label = 'Schedule',
  name = 'scheduleType',
  subname = 'scheduleInfo',
  gutter = 32
}) {
  const scheduleTypeOptions = [
    {
      label: 'Every Hour',
      value: 1
    },
    {
      label: 'Every Day',
      value: 2
    },
    {
      label: 'Every Week',
      value: 3
    },
    {
      label: 'Every Month',
      value: 4
    },
    // {
    //   label: 'Every Year',
    //   value: 'yearly'
    // },
    {
      label: 'Cron Expression',
      value: 5
    }
  ];

  const jobDayOptions = LRange(0, 7).map((i) => ({
    label: Moment(i, 'd').format('dddd'),
    value: i
  }));
  // const jobMonthOptions = LRange(1, 13)
  //   .map((m) => Moment(m, 'M').format('MMMM'))
  //   .map((d) => ({ value: d, label: d }));

  const form = Form.useFormInstance();
  // console.log(form.getFieldsValue());
  return (
    <Row gutter={gutter}>
      <Col span={24}>
        <Form.Item label={label} name={name} rules={[{ required: true }]}>
          <Select
            placeholder="Select One"
            options={scheduleTypeOptions}
            onChange={(scheduleType) => {
              form.setFieldValue(name, scheduleType);
              form.setFieldValue(subname, {});
            }}
          />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          label="Job Time"
          name={[subname, 'jobTime']}
          hidden={form.getFieldValue(name) && [5].includes(form.getFieldValue(name))}
          rules={[{ required: form.getFieldValue(name) && form.getFieldValue(name) !== 5 }]}>
          <TimePicker className="w-full" />
        </Form.Item>
        <Form.Item
          name={[subname, 'expression']}
          hidden={![5].includes(form.getFieldValue(name))}
          rules={[{ required: form.getFieldValue(name) === 5 }]}
          label="Cron Expression">
          <Input placeholder="Ex. * 2 * * 10" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Row gutter={32}>
          <Col span={12}>
            <Form.Item
              label="Job Day"
              name={[subname, 'jobDay']}
              hidden={![3, 4].includes(form.getFieldValue(name))}
              rules={[{ required: [3, 4].includes(form.getFieldValue(name)) }]}>
              <Select options={jobDayOptions} placeholder="Day" />
            </Form.Item>
          </Col>
          {/* <Col span={12}>
            <Form.Item
              label="Job Month"
              name={[subname, 'jobMonth']}
              hidden={![3, 4].includes(form.getFieldValue(name))}
              rules={[{ required: [3, 4].includes(form.getFieldValue(name)) }]}>
              <Select options={jobMonthOptions} placeholder="Month" />
            </Form.Item>
          </Col> */}
        </Row>
      </Col>
    </Row>
  );
}
