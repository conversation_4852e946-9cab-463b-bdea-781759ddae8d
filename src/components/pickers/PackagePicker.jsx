import { Row, Col, Transfer } from 'antd';
import { getAllPackagesApi } from '@/src/modules/device-automation/packages.js';
import buildPicker from './pickerBuilder';
import Icon from '../Icon.jsx';

const { Provider, contextHook } = buildPicker(getAllPackagesApi, PackagePicker, (i, options) => ({
  key: i.id,
  os: i.os,
  title: `${i.name} - ${i.displayName}`,
  nodeRenderer: (
    <Row wrap={false} align="middle">
      <Col flex="none">
        <div className="items-center inline-flex mr-2">
          <Icon name={`platform_${i.os}`} className="text-base" />
          <span className="ml-1">({i.osArch})</span>
        </div>
      </Col>
      <Col flex="none">
        {i.iconFile ? (
          <div className="mr-2">
            <img
              width={20}
              src={`${i.iconFile[0].url}${options.token.access_token}`}
              alt={i.displayName}
            />
          </div>
        ) : null}
      </Col>
      <Col flex="auto">
        {i.name}: {i.displayName}
      </Col>
    </Row>
  )
}));

function PackagePicker({ target, ...props }) {
  const { filteredOptions, allOptions } = contextHook();

  let options = filteredOptions || allOptions;

  if (target === 4) {
    options = Array.isArray(options)
      ? options
      : Array.from(options.values()).filter((i) => i.os.toLowerCase() === 'windows');
  }
  if (target === 5) {
    options = Array.isArray(options)
      ? options
      : Array.from(options.values()).filter((i) => i.os.toLowerCase() === 'linux');
  }
  if (target === 6) {
    options = Array.isArray(options)
      ? options
      : Array.from(options.values()).filter((i) => i.os.toLowerCase() === 'mac');
  }

  return (
    <Transfer
      {...props}
      showSearch
      filterOption={(searchTerm, node) => {
        return node.title.toLowerCase().indexOf(searchTerm.toLowerCase()) >= 0;
      }}
      render={(i) => i.nodeRenderer}
      dataSource={Array.isArray(options) ? options : Array.from(options.values())}
      listStyle={{
        width: '45%',
        height: 350
      }}
    />
  );
}

export const Package = {
  usePackage: contextHook,
  Provider: Provider,
  Picker: PackagePicker
};
