import Picker from './Picker.jsx';
import buildPicker from './pickerBuilder';
import { getVulnerabilityPolicyAttributesApi } from '@/src/modules/settings/api/policy-management/policies.js';

const { Provider, contextHook } = buildPicker(
  getVulnerabilityPolicyAttributesApi,
  VulnerabilityPolicyAttributePicker
);

function VulnerabilityPolicyAttributePicker({ value, onChange, ...props }) {
  const { filteredOptions, allOptions, search } = contextHook();
  if (props.disabled && props.textOnly) {
    return allOptions.has(value) ? allOptions.get(value).label : null;
  }
  return (
    <Picker
      {...props}
      style={{ width: '100%' }}
      placeholder="Please Select"
      value={value}
      onChange={onChange}
      onSearch={search}
      options={filteredOptions || allOptions}
    />
  );
}

export const VulnerabilityPolicyAttributes = {
  Provider: Provider,
  Picker: VulnerabilityPolicyAttributePicker,
  contextHook
};
