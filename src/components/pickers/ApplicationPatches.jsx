import Api from '@api';
import { <PERSON><PERSON>, Drawer, Row, Col } from 'antd';
import { CrudProvider } from '@/src/hooks/crud';
import Icon from '../Icon';
import { useState, useEffect } from 'react';
import PatchSeverityPicker from '@modules/patch/components/PatchSeverityPicker';
import FindIndex from 'lodash/findIndex';
import CloneDeep from 'lodash/cloneDeep';

export default function ApplicationPatches({ value, onChange, disabled, className, platform }) {
  useEffect(() => {
    if (value?.length) {
      fetchSelectedFn();
    }
    // eslint-disable-next-line
  }, []);
  const columns = [
    {
      title: 'Name',
      key: 'name',
      dataIndex: 'name'
    },
    {
      title: 'Severity',
      key: 'severityList',
      dataIndex: 'severityList',
      render({ record }) {
        return (
          <PatchSeverityPicker
            mode="multiple"
            value={record.severityList}
            onChange={(severityList) => {
              return updateApplicationPatch(record, severityList);
            }}
          />
        );
      }
    }

    // {
    //   title: 'Product',
    //   key: 'productFamily',
    //   dataIndex: 'productFamily'
    // },
    // {
    //   title: 'Product Type',
    //   key: 'productType',
    //   dataIndex: 'productType'
    // },
    // {
    //   title: 'Platform',
    //   key: 'platform',
    //   dataIndex: 'platform',
    //   render({ record }) {
    //     return (
    //       <div className="flex items-center">
    //         <Icon
    //           name={`platform_${record.platform.toLowerCase()}`}
    //           title={record.platform}
    //           className="text-lg mr-2"
    //         />
    //         {record.platform}
    //       </div>
    //     );
    //   }
    // },
    // {
    //   title: 'Description',
    //   key: 'description',
    //   dataIndex: 'description',
    //   ellipsis: true
    // }
  ];

  const productColumns = [
    {
      title: 'name',
      key: 'name',
      dataIndex: 'name'
    },
    {
      title: 'Product',
      key: 'productFamily',
      dataIndex: 'productFamily'
    },
    {
      title: 'Product Type',
      key: 'productType',
      dataIndex: 'productType'
    },
    {
      title: 'Platform',
      key: 'platform',
      dataIndex: 'platform',
      render({ record }) {
        return (
          <div className="flex items-center">
            <Icon
              name={`platform_${record.platform.toLowerCase()}`}
              title={record.platform}
              className="text-lg mr-2"
            />
            {record.platform}
          </div>
        );
      }
    },
    {
      title: 'Description',
      key: 'description',
      dataIndex: 'description',
      ellipsis: true
    }
  ];

  const [selectedItems, setSelectedItems] = useState(value?.map((i) => i.productId) || []);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  function fetchSelectionFn() {
    return Api.post(`/patch/patch-affected-products/search`, {
      qualification: [
        {
          operator: 'Equals',
          column: 'platform',
          value: platform
        }
      ]
    });
  }

  function fetchSelectedFn() {
    if ((value || []).length) {
      return Api.post(`/patch/patch-affected-products/search`, {
        qualification: [
          {
            operator: 'in',
            column: 'id',
            value: value?.map((i) => i.productId)
          }
        ]
      });
    }
    return Promise.resolve({ result: [], totalCount: 0 });
  }

  function handleSelect() {
    onChange(selectedItems.map((id) => ({ productId: id, severityList: [] })));
    setIsDrawerOpen(false);
  }

  function updateApplicationPatch(record, severityList) {
    const index = FindIndex(value || [], { productId: record.id });
    const currentValues = CloneDeep(value || []);

    if (index >= 0) {
      onChange([
        ...currentValues.slice(0, index),
        { productId: record.id, severityList },
        ...currentValues.slice(index + 1)
      ]);
    }
  }

  return (
    <div className={`flex flex-col flex-1 ${className}`}>
      {!disabled ? (
        <div className="text-right">
          <Button
            size="large"
            type="link"
            onClick={() => setIsDrawerOpen(true)}
            disabled={!platform}>
            <Icon name="add" className="ml-2" />
            Add Affected Products
          </Button>
        </div>
      ) : null}
      <div className={`flex flex-col -my-2 ${className}`}>
        <CrudProvider
          disableHeadingSlot
          key={(value || []).map((i) => i.productId).join(',')}
          appendColumns={
            disabled
              ? undefined
              : [
                  {
                    title: '',
                    dataIndex: 'action',
                    key: 'action',
                    width: '40px',
                    sortable: false,
                    render({ record }) {
                      return (
                        <Button
                          danger
                          type="text"
                          onClick={() => onChange(value.filter((v) => v.productId !== record.id))}>
                          <Icon name="delete" style={{ fontSize: '1.1rem' }} />
                        </Button>
                      );
                    }
                  }
                ]
          }
          disableExport
          columns={columns}
          selectedItems={selectedItems}
          onChange={setSelectedItems}
          resourceTitle="Selected Affected Products"
          fetchFn={fetchSelectedFn}
        />
      </div>
      <Drawer
        title="Select Affected Products"
        placement={'right'}
        onClose={() => {
          setIsDrawerOpen(false);
        }}
        destroyOnClose
        width="70%"
        maskClosable={false}
        open={isDrawerOpen}>
        <div className="flex flex-col min-h-0 flex-1 h-full">
          <CrudProvider
            disableExport
            disableColumnSelection
            columns={productColumns}
            allowSelection
            selectedItems={selectedItems}
            onChange={setSelectedItems}
            resourceTitle="Affected Products"
            hasSearch
            fetchFn={fetchSelectionFn}
          />
          <div className="flex-shrink-0">
            <Row>
              <Col span={24} className="text-right">
                <Button type="primary" className="mr-2" onClick={handleSelect}>
                  Select
                </Button>
                <Button type="primary" ghost onClick={() => setIsDrawerOpen(false)}>
                  Cancel
                </Button>
              </Col>
            </Row>
          </div>
        </div>
      </Drawer>
    </div>
  );
}
