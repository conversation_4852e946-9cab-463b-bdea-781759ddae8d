import React from 'react';
import { useState, useRef } from 'react';
import Merge from 'lodash/merge';
import CloneDeep from 'lodash/cloneDeep';

import { Drawer, Form, Button, Col, Row } from 'antd';

const DrawerForm = ({
  formFields,
  defaultItem,
  isOpen = false,
  onClose,
  drawerFormProps = {},
  edit,
  formActions,
  createFn,
  drawerTitle
}) => {
  const [formItem, setFormItem] = useState(CloneDeep(defaultItem));
  const [processingForm, setProcessing] = useState(false);

  const formRef = useRef(null);

  function handleFormSubmit(item) {
    item = { ...formItem, ...item };

    setProcessing(true);

    return createFn(item)
      .then(() => {
        setFormItem(null);
      })
      .finally(() => {
        setProcessing(false);

        setTimeout(() => {
          if (onClose) {
            onClose();
          }
        }, 700);
      });
  }
  function resetForm() {
    setFormItem(defaultItem);
  }
  return (
    <Drawer
      title={`
        ${drawerTitle}`}
      placement="right"
      width="50%"
      onClose={() => {
        onClose();
        setProcessing(false);
      }}
      destroyOnClose
      open={Boolean(isOpen)}>
      <Form
        layout="vertical"
        className="h-full"
        requiredMark
        ref={formRef}
        onFinish={handleFormSubmit}
        onValuesChange={(values) => {
          setFormItem(Merge({ ...formItem }, { ...values }));
        }}
        initialValues={formItem}
        {...drawerFormProps}>
        <Row className="h-full">
          <div className="flex flex-1 min-h-0 flex-col h-full min-w-0">
            <div className={`flex-1 min-h-0 ${'overflow-auto overflow-x-hidden'}`}>
              <Col span={24} className="flex-1 h-full">
                {formFields
                  ? formFields(formItem, (change) => edit({ ...formItem, ...change }), {
                      formItem
                    })
                  : null}
              </Col>
            </div>
            <div className="flex-shrink-0">
              {formItem && (
                <Col span={24} className="text-right">
                  {formActions ? (
                    formActions({
                      processingForm,
                      resetForm,
                      submitForm(data) {
                        formRef.current.validateFields().then(() => {
                          handleFormSubmit(data);
                        });
                      },
                      formItem
                    })
                  ) : (
                    <>
                      <Button
                        type="primary"
                        loading={processingForm}
                        htmlType="submit"
                        className="mr-2">
                        {formItem && formItem.id !== undefined && formItem.id !== null
                          ? 'Update'
                          : 'Create'}
                      </Button>
                      <Button type="primary" ghost htmlType="reset" onClick={resetForm}>
                        Reset
                      </Button>
                    </>
                  )}
                </Col>
              )}
            </div>
          </div>
        </Row>
      </Form>
    </Drawer>
  );
};

export default DrawerForm;
