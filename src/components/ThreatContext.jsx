import SystemLogo from './SystemLogo';

export default function ThreatContext({ record, onClick }) {
  let type = record.threat_context ? record.threat_context.split('|!!|').pop() : null;
  return record.threat_context ? (
    type === 'Zirozen' ? (
      <img
        alt="Zirozen Threat Context"
        src="/logo192.png"
        style={{ width: '18px' }}
        className="mr-2 cursor-pointer"
        onClick={() => onClick(record)}
      />
    ) : (
      <SystemLogo
        title={type}
        disabled
        onClick={() => onClick(record)}
        className="mr-2 cursor-pointer"
        style={{ width: '18px' }}
        name={type}
        type="integration"
      />
    )
  ) : (
    <div className="mr-2 w-4" />
  );
}
