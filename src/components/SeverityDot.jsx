import Capitalize from 'lodash/capitalize';

export default function SeverityDot({ severity, tooltipText, disableTooltip }) {
  return (
    <div
      className="severity-dot-wrapper"
      title={disableTooltip ? tooltipText || Capitalize(severity) : undefined}>
      <div className={`${severity.toLowerCase()} severity-dot-box severity-dot-shadow`}>
        <div className={`severity-dot severity-dot-shadow ${severity.toLowerCase()}`} />
      </div>
    </div>
  );
}
