function Description({ children, label }) {
  return (
    <div className="flex flex-col min-w-0 flex-1 w-full">
      {label && <h1>{label}</h1>}
      {children}
    </div>
  );
}

function Item({ children, label }) {
  return (
    <div className="flex flex-1 min-w-0 items-center">
      <div className="flex min-w-0 items-center text-label w-1/3">
        <small>{label}</small>
      </div>
      <div className="flex min-w-0 items-center flex-1 w-2/3" title={children}>
        <span className="text-ellipsis">{children}</span>
      </div>
    </div>
  );
}

Description.Item = Item;

export default Description;
