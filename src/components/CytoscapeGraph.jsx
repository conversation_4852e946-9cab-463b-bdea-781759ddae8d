import CytoscapeComponent from 'react-cytoscapejs';
import cytoscape from 'cytoscape';
import dagre from 'cytoscape-dagre';
import cytoscapeHtmlLabel from 'cytoscape-html-label';
import panzoom from 'cytoscape-panzoom';
import cytoscapePopper from 'cytoscape-popper';
import { computePosition, flip, offset, shift } from '@floating-ui/dom';
import { useEffect, useRef, useState } from 'react';

function popperFactory(ref, content, opts) {
  // see https://floating-ui.com/docs/computePosition#options
  const popperOptions = {
    // matching the default behaviour from Popper@2
    // https://floating-ui.com/docs/migration#configure-middleware
    middleware: [flip(), shift(), offset(5)],
    ...opts
  };

  function update() {
    computePosition(ref, content, popperOptions).then(({ x, y }) => {
      Object.assign(content.style, {
        left: `${x}px`,
        top: `${y}px`
      });
    });
  }
  update();
  return { update };
}
cytoscape.use(dagre);
cytoscape.use(cytoscapeHtmlLabel);
cytoscape.use(panzoom);
cytoscape.use(cytoscapePopper(popperFactory));

export default function CytoscapeGraph({
  hierarchy,
  dataLabelFn,
  tooltipFn,
  activeNode,
  graphStyle,
  ...props
}) {
  const cyInstance = useRef(null);
  const ref = useRef(null);
  const cyPopperDivRef = useRef(null);
  const cyPopperInstanceRef = useRef(null);
  const [width, setWidth] = useState(null);

  useEffect(() => {
    return () => {
      if (cyPopperDivRef.current) {
        cyPopperDivRef.current.parentNode.removeChild(cyPopperDivRef.current);
        cyPopperDivRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (ref.current) {
      let resizeObserver = new ResizeObserver(() => {
        setWidth(ref.current.offsetWidth);
        setTimeout(() => {
          if (cyInstance.current) {
            cyInstance.current.resize();
            cyInstance.current.fit();
            cyInstance.current.center();
          }
          if (cyPopperInstanceRef.current) {
            cyPopperInstanceRef.current.update();
          }
        });
      });

      resizeObserver.observe(ref.current);

      return () => resizeObserver.disconnect();
    }
  }, [ref]);

  useEffect(() => {
    if (cyInstance.current) {
      cyInstance.current.nodes().removeClass('selected');
      if (activeNode) {
        let node = cyInstance.current.$(`#${activeNode.id}`).first();
        if (node) {
          node.addClass('selected');
        }
      }
    }
  }, [activeNode]);

  function createTooltip() {
    const cy = cyInstance.current;

    cy.nodes().on('position', (event) => {
      if (cyPopperInstanceRef.current) {
        cyPopperInstanceRef.current.update();
      }
    });

    cy.nodes().on('mouseover', (event) => {
      if (cyPopperDivRef.current) {
        cyPopperDivRef.current.parentNode.removeChild(cyPopperDivRef.current);
        cyPopperDivRef.current = null;
      }
      let div = document.createElement('div');

      div.className =
        'rounded bg-primary px-2 py-1 shadow border border-solid border-border absolute text-white';

      div.innerHTML = tooltipFn ? tooltipFn(event.target.data()) : event.target.data('label');

      ref.current.appendChild(div);

      cyPopperDivRef.current = div;

      cyPopperInstanceRef.current = event.target.popper({
        content() {
          return cyPopperDivRef.current;
        },
        popper: {
          placement: 'top',
          removeOnDestroy: true
        }
      });
    });

    cy.nodes().on('mouseout', () => {
      if (cyPopperDivRef.current) {
        cyPopperDivRef.current.parentNode.removeChild(cyPopperDivRef.current);
        cyPopperDivRef.current = null;
      }
    });
  }
  return (
    <div ref={ref} className="relative h-full">
      {width ? (
        <CytoscapeComponent
          {...props}
          elements={CytoscapeComponent.normalizeElements(hierarchy)}
          style={{ width: `${width}px`, height: '100%' }}
          layout={{
            name: 'dagre',
            rankDir: 'LR',
            rankSep: 150,
            nodeSep: 100,
            edgeSep: 150
          }}
          maxZoom={1.3}
          cy={(cy) => {
            cyInstance.current = cy;
            if (activeNode) {
              let node = cyInstance.current.$(`#${activeNode.id}`).first();
              if (node) {
                node.addClass('selected');
              }
            }
            cy.htmlLabel([
              {
                query: 'node',
                valign: 'center',
                halign: 'center',
                valignBox: 'center',
                halignBox: 'center',
                tpl: function (data) {
                  if (dataLabelFn) {
                    return dataLabelFn(data);
                  } else {
                    return `<div class="text-center max-w-[95px]" title="${data.label}">
                        <p class="text-xs mb-0 text-white text-ellipsis whitespace-pre">
                        ${data.label}
                        </p></div>`;
                  }
                }
              }
            ]);
            // cy.fit();
            cy.center();
            cy.panzoom({
              // options here...
            });
            createTooltip();
            if (props.onNodeClick) {
              cy.nodes().on('click', function (e) {
                props.onNodeClick(e);
              });
            }
          }}
          stylesheet={[
            {
              selector: 'node',
              shape: 'round-rectangle',
              style: {
                // label: 'data(label)',
                width: 100,
                height: 50,
                shape: 'round-rectangle',
                'text-halign': 'center',
                'text-valign': 'center',
                backgroundColor: '#364658',
                borderWidth: 1,
                borderStyle: 'solid',
                borderColor: 'rgba(53, 132, 220, 0.40)',
                color: 'white',
                'text-wrap': 'ellipsis'
              }
            },
            {
              selector: 'node.selected',
              style: {
                'font-weight': 'bold',
                opacity: 1,
                'background-color': '#267ED4'
              }
            },
            {
              selector: 'edge',
              style: {
                width: 1.2,
                'line-style': 'solid',
                'line-color': '#89c540',
                'target-arrow-color': '#89c540',
                'target-arrow-shape': 'triangle-backcurve',
                // 'curve-style': 'straight',
                'curve-style': 'round-taxi',
                'taxi-direction': 'rightward',
                // 'taxi-turn': 90,
                // 'taxi-turn-min-distance': 20,
                'taxi-radius': 80
              }
            },
            ...(graphStyle || [])
          ]}
        />
      ) : null}
    </div>
  );
}
